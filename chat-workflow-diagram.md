# Knowledge Graph Visualizer - Chat Workflow Architecture

## Overview

This document provides a comprehensive description of the workflows and processes involved in a chat interaction within the 360t-kg knowledge graph visualization system. The system integrates multiple components including PostgreSQL, Graphiti, Ollama, Redis, frontend, proxy, backend, and Neo4j to create a seamless query and response experience.

## System Architecture

The system follows a 5-layer architecture:

1. **Frontend Layer**: React components (ChatView.jsx, ChatContext.jsx) - Port 5177
2. **Proxy Layer**: Express.js session management (proxy-server/) - Port 3003 
3. **API Layer**: Node.js backend (360t-kg-api/) - Port 3002
4. **AI Layer**: FastAPI Python service (main.py) - Port 8000
5. **Database Layer**: Neo4j + Ollama integration

```mermaid
graph TD
    subgraph "Client Layer"
        UI[Web UI - React/TypeScript]
        Mobile[Mobile App - Future]
    end
    
    subgraph "API Gateway"
        Gateway[API Gateway/Load Balancer]
    end
    
    subgraph "Microservices"
        GraphAPI[Graph API Service]
        ChatService[Chat AI Service]
        AuthService[Authentication Service]
        NotificationService[Notification Service]
    end
    
    subgraph "Data Layer"
        Neo4j[(Neo4j Graph Database)]
        Redis[(Redis Cache)]
        S3[File Storage]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Ollama[Ollama LLM]
        Prometheus[Prometheus Metrics]
        Grafana[Grafana Dashboards]
    end
    
    UI --> Gateway
    Gateway --> GraphAPI
    Gateway --> ChatService
    Gateway --> AuthService
    
    GraphAPI --> Neo4j
    GraphAPI --> Redis
    ChatService --> OpenAI
    ChatService --> Ollama
    ChatService --> Neo4j
    
    GraphAPI --> Prometheus
    ChatService --> Prometheus
```

## Chat Workflow Sequence

### 1. User Interaction

The chat workflow begins when a user submits a query through the frontend interface:

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Proxy
    participant Backend
    participant AI
    participant Neo4j
    participant Ollama
    
    User->>Frontend: Submit query "What is EMS?"
    Frontend->>Proxy: POST /api/chat
    Proxy->>Backend: Forward request
    Backend->>AI: Forward to FastAPI (port 8000)
```

### 2. Request Processing

The request flows through the system with each component performing specific functions:

#### Frontend to Proxy
- **Component**: 360t-kg-ui/src/components/ChatView.jsx
- **Function**: Captures user input and sends to proxy server
- **Data Flow**: 
  ```javascript
  {
    question: "What is EMS?",
    conversation_history: [...],
    graphitiSettings: {
      searchType: "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
      llmProvider: "ollama",
      ollamaModel: "gemma3:latest",
      edgeCount: 6,
      nodeCount: 2,
      diversityFactor: 0.3
    }
  }
  ```

#### Proxy to Backend API
- **Component**: proxy-server/routes/chatRoutes.js
- **Function**: Routes requests and manages sessions
- **Data Flow**: Transforms and forwards request to backend API

#### Backend API to AI Layer
- **Component**: 360t-kg-api/routes/chatRoutes.js
- **Function**: Processes request and forwards to AI service
- **Data Flow**: 
  ```javascript
  const scriptPath = path.resolve('..', 'graphiti_hybrid_search.py');
  const args = [
    '--search-type', graphitiSettings.searchType,
    '--llm-provider', graphitiSettings.llmProvider,
    '--ollama-model', graphitiSettings.ollamaModel,
    '--edge-count', String(graphitiSettings.edgeCount),
    '--node-count', String(graphitiSettings.nodeCount),
    '--diversity-factor', String(graphitiSettings.diversityFactor)
  ];
  ```

### 3. AI Processing with Graphiti

The AI layer processes the request using the Graphiti search engine:

#### Graphiti Search Engine Workflow

```mermaid
graph TD
    A[User Query] --> B[Query Enhancement]
    B --> C[Domain Knowledge Expansion]
    C --> D[Hybrid Search Execution]
    D --> E[Neo4j Entity Search]
    E --> F[Vector Similarity + BM25]
    F --> G[Result Fusion]
    G --> H[RRF Score Fusion]
    H --> I[Context Building]
    I --> J[LLM Generation]
    J --> K[Ollama API Call]
    K --> L[Response Transformation]
    L --> M[Structured Response]
```

#### Graphiti Search Parameters

| Parameter | Default Value | Description |
|---------|-------------|------------|
| searchType | COMBINED_HYBRID_SEARCH_CROSS_ENCODER | Search algorithm type |
| llmProvider | ollama | LLM provider (ollama, openai, google_genai) |
| ollamaModel | gemma3:latest | Ollama model name for response generation |
| edgeCount | 6 | Number of relationship facts to retrieve |
| nodeCount | 2 | Number of entity summaries to retrieve |
| diversityFactor | 0.3 | MMR diversity factor (0.0=relevance, 1.0=diversity) |
| temperature | 0.3 | LLM temperature (0.0-1.0) |
| timeout | 120 | Request timeout in seconds |

### 4. Database Interactions

#### Neo4j Database Operations

```mermaid
sequenceDiagram
    AI->>Neo4j: MATCH queries for entity search
    Neo4j-->>AI: Return nodes and relationships
    AI->>Neo4j: CALL gds.graph.project for GDS
    Neo4j-->>AI: Return graph projections
    AI->>Neo4j: Pathfinding algorithms
    Neo4j-->>AI: Return relationship paths
```

#### PostgreSQL Chat Storage

```sql
-- Chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. LLM Integration

#### Ollama LLM Processing

```mermaid
sequenceDiagram
    AI->>Ollama: POST /api/generate
    Ollama-->>AI: Streaming response
    AI->>AI: Response parsing and formatting
    AI->>AI: Citation extraction
    AI->>AI: Follow-up question generation
```

#### LLM Configuration

```python
# In graphiti_search_engine.py
class LLMConfiguration:
    # Graphiti internal operations (knowledge graph reasoning)
    graphiti_model: str
    graphiti_url: str
    
    # Final response generation (user-facing answers)
    response_model: str
    response_url: str
    
    # Vector embeddings
    embedding_model: str
    embedding_dimensions: int
    openai_api_key: str
```

### 6. Response Generation

#### Response Builder Process

```mermaid
graph TD
    A[Raw LLM Response] --> B[Answer Extraction]
    B --> C[Explanation Parsing]
    C --> D[Follow-up Questions]
    D --> E[Citation Matching]
    E --> F[Structured JSON]
    F --> G[Frontend-Compatible Format]
```

#### Response Structure

```json
{
  "response": {
    "role": "assistant",
    "content": "Answer with markdown formatting",
    "timestamp": "2025-07-14T10:35:00Z",
    "sourceDocuments": [...],
    "sourceNodes": [...]
  },
  "updatedHistory": [...],
  "structured_response": {
    "answer": "Formatted answer",
    "explanation": "Detailed explanation",
    "follow_up_questions": [...],
    "citations": [...],
    "metadata": {
      "search_type": "COMBINED_HYBRID_SEARCH_CROSS_ENCODER",
      "search_time_ms": 150,
      "response_time_ms": 2000,
      "total_time_ms": 2150
    }
  }
}
```

### 7. Caching with Redis

#### Redis Caching Strategy

```mermaid
sequenceDiagram
    AI->>Redis: Check cache for query hash
    Redis-->>AI: Return cached response (if exists)
    AI->>Neo4j: Query database (if cache miss)
    Neo4j-->>AI: Return results
    AI->>Redis: Store response with TTL
```

#### Cache Configuration

```javascript
// In 360t-kg-api/redis/config.js
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  cacheTTL: 300 // 5 minutes
};
```

## Component Collaboration Flow

### Complete End-to-End Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Proxy
    participant Backend
    participant AI
    participant Neo4j
    participant Ollama
    participant Redis
    participant PostgreSQL
    
    User->>Frontend: Submit query
    Frontend->>Proxy: Send request
    Proxy->>Backend: Forward request
    Backend->>AI: Forward to FastAPI
    AI->>Redis: Check cache
    Redis-->>AI: Cache miss
    AI->>Neo4j: Entity search
    Neo4j-->>AI: Return nodes/relationships
    AI->>Ollama: Generate response
    Ollama-->>AI: Return LLM response
    AI->>PostgreSQL: Save conversation
    AI-->>Backend: Return structured response
    Backend-->>Proxy: Forward response
    Proxy-->>Frontend: Return to UI
    Frontend-->>User: Display response
```

## Performance Metrics

### Response Time Breakdown

| Component | Average Time | Notes |
|---------|-------------|------|
| Frontend Processing | 50ms | Input handling and UI updates |
| Proxy Routing | 20ms | Request forwarding |
| Backend Processing | 30ms | Request validation and routing |
| AI Processing | 150ms | Graphiti initialization |
| Neo4j Query | 80ms | Entity and relationship search |
| Ollama Generation | 2000ms | LLM response generation |
| Response Formatting | 100ms | Structured response creation |
| **Total** | **2430ms** | |

### Error Handling and Fallbacks

```mermaid
graph TD
    A[Request] --> B{Success?}
    B -->|Yes| C[Return response]
    B -->|No| D{Error Type}
    D -->|Timeout| E[Return partial results]
    D -->|LLM Error| F[Fallback to cached response]
    D -->|Database Error| G[Return error message]
    D -->|Validation Error| H[Return input guidance]
```

## Configuration and Settings

### Frontend Settings Service

```javascript
// 360t-kg-ui/src/services/settingsService.js
const settings = {
  graphiti: {
    searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
    diversityFactor: 0.3,
    edgeCount: 6,
    nodeCount: 2,
    llmProvider: 'ollama',
    ollamaModel: 'gemma3:latest',
    ollamaUrl: 'http://localhost:11434',
    temperature: 0.3,
    timeout: 180,
    enableCaching: true,
    logLevel: 'info'
  }
};
```

### Environment Variables

```bash
# In .env file
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=1979@rabu
OLLAMA_URL=http://localhost:11434
OPENAI_API_KEY=sk-...
REDIS_HOST=localhost
REDIS_PORT=6379
```

## Health Checks and Monitoring

### Health Check Endpoints

```mermaid
graph TD
    A[Health Check] --> B{Service}
    B -->|API| C[Check server status]
    B -->|Neo4j| D[Check connectivity]
    B -->|Ollama| E[Check model availability]
    B -->|Redis| F[Check cache connectivity]
    B -->|PostgreSQL| G[Check database]
    C --> H[Return status]
    D --> H
    E --> H
    F --> H
    G --> H
```

### Health Check Response

```json
{
  "status": "healthy",
  "timestamp": "2025-07-14T10:35:00Z",
  "version": "1.0.0",
  "services": {
    "api": "healthy",
    "neo4j": "healthy",
    "ollama": "healthy",
    "redis": "healthy",
    "postgresql": "healthy"
  },
  "uptime": 3600
}
```

## Conclusion

The 360t-kg knowledge graph visualization system demonstrates a sophisticated integration of multiple technologies to provide a seamless chat experience. The workflow involves coordinated efforts between frontend, proxy, backend, AI, and database components, with each playing a crucial role in processing user queries and generating informative responses.

Key architectural decisions include:
- Separation of concerns between different system layers
- Use of Graphiti for advanced hybrid search capabilities
- Integration of multiple LLM providers with fallback mechanisms
- Comprehensive caching strategy using Redis
- Persistent chat storage using PostgreSQL
- Real-time monitoring and health checks

This architecture enables the system to handle complex queries, provide accurate responses with proper citations, and maintain high availability and performance.