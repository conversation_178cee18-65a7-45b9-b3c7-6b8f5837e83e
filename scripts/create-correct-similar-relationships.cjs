const neo4j = require('neo4j-driver');

const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

function normalizeForComparison(name) {
    if (!name) return '';
    
    // Convert to lowercase and remove common plural endings
    let normalized = name.toLowerCase().trim();
    
    // Remove common plural endings
    if (normalized.endsWith('ies')) {
        normalized = normalized.slice(0, -3) + 'y';
    } else if (normalized.endsWith('es') && normalized.length > 3) {
        normalized = normalized.slice(0, -2);
    } else if (normalized.endsWith('s') && normalized.length > 2) {
        normalized = normalized.slice(0, -1);
    }
    
    return normalized;
}

function areSimilar(name1, name2) {
    const norm1 = normalizeForComparison(name1);
    const norm2 = normalizeForComparison(name2);
    
    // Exact match after normalization
    if (norm1 === norm2) return true;
    
    // One contains the other (for partial matches)
    if (norm1.length > 2 && norm2.length > 2) {
        if (norm1.includes(norm2) || norm2.includes(norm1)) {
            return true;
        }
    }
    
    return false;
}

async function createSimilarRelationships() {
    const session = driver.session({ database: 'neo4j' });
    
    try {
        console.log('Fetching all entities with names...');
        
        const result = await session.run(
            `MATCH (n:Entity) WHERE n.name IS NOT NULL 
             RETURN id(n) as id, n.name as name 
             ORDER BY n.name`
        );
        
        console.log(`Found ${result.records.length} entities with names`);
        
        const entities = result.records.map(record => ({
            id: record.get('id'),
            name: record.get('name')
        }));
        
        let similarPairs = [];
        console.log('Finding similar name pairs...');
        
        // Compare each entity with every other entity
        for (let i = 0; i < entities.length; i++) {
            for (let j = i + 1; j < entities.length; j++) {
                if (areSimilar(entities[i].name, entities[j].name)) {
                    similarPairs.push({
                        id1: entities[i].id,
                        name1: entities[i].name,
                        id2: entities[j].id,
                        name2: entities[j].name
                    });
                }
            }
            
            // Progress indicator
            if (i % 100 === 0) {
                console.log(`Processed ${i}/${entities.length} entities...`);
            }
        }
        
        console.log(`Found ${similarPairs.length} similar name pairs`);
        
        // Create relationships in batches
        const batchSize = 100;
        let created = 0;
        
        for (let i = 0; i < similarPairs.length; i += batchSize) {
            const batch = similarPairs.slice(i, i + batchSize);
            
            for (const pair of batch) {
                await session.run(
                    `MATCH (n1), (n2) 
                     WHERE id(n1) = $id1 AND id(n2) = $id2
                     CREATE (n1)-[:SIMILAR_TO {reason: 'similar_name', name1: $name1, name2: $name2, created: datetime()}]->(n2)`,
                    {
                        id1: pair.id1,
                        id2: pair.id2,
                        name1: pair.name1,
                        name2: pair.name2
                    }
                );
                created++;
            }
            
            console.log(`Created ${created}/${similarPairs.length} SIMILAR_TO relationships...`);
        }
        
        console.log(`\nCreated ${created} SIMILAR_TO relationships based on name similarity!`);
        
        // Show some examples
        const exampleResult = await session.run(
            `MATCH (n1)-[r:SIMILAR_TO]->(n2) 
             RETURN n1.name as name1, n2.name as name2, r.reason as reason
             LIMIT 10`
        );
        
        console.log('\nExample SIMILAR_TO relationships:');
        exampleResult.records.forEach(record => {
            console.log(`  "${record.get('name1')}" <-> "${record.get('name2')}"`);
        });
        
    } catch (error) {
        console.error('Error creating SIMILAR_TO relationships:', error);
    } finally {
        await session.close();
    }
}

createSimilarRelationships().then(() => {
    driver.close();
    console.log('SIMILAR_TO relationships creation completed!');
}).catch(error => {
    console.error('Script failed:', error);
    driver.close();
});