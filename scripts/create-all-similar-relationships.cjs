const neo4j = require('neo4j-driver');

const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

function normalizeForComparison(name) {
    if (!name) return '';
    
    // Convert to lowercase and remove common plural endings
    let normalized = name.toLowerCase().trim();
    
    // Remove common plural endings
    if (normalized.endsWith('ies')) {
        normalized = normalized.slice(0, -3) + 'y';
    } else if (normalized.endsWith('es') && normalized.length > 3) {
        normalized = normalized.slice(0, -2);
    } else if (normalized.endsWith('s') && normalized.length > 2) {
        normalized = normalized.slice(0, -1);
    }
    
    return normalized;
}

async function createSimilarRelationshipsOptimized() {
    const session = driver.session({ database: 'neo4j' });
    
    try {
        console.log('Creating SIMILAR_TO relationships for all entities...');
        
        // Use a more efficient approach with Cypher directly
        console.log('Creating relationships based on exact name matches (case-insensitive)...');
        
        const exactMatchResult = await session.run(`
            MATCH (n1:Entity), (n2:Entity)
            WHERE n1.name IS NOT NULL 
              AND n2.name IS NOT NULL
              AND toLower(n1.name) = toLower(n2.name)
              AND id(n1) < id(n2)
              AND NOT EXISTS((n1)-[:SIMILAR_TO]-(n2))
            CREATE (n1)-[:SIMILAR_TO {reason: 'exact_name_match', created: datetime()}]->(n2)
            RETURN count(*) as exact_matches
        `);
        
        console.log(`Created ${exactMatchResult.records[0].get('exact_matches')} exact name matches`);
        
        // Handle plurals - entities ending with 's' matching singular forms
        console.log('Creating relationships for plural forms...');
        
        const pluralResult = await session.run(`
            MATCH (n1:Entity), (n2:Entity)
            WHERE n1.name IS NOT NULL 
              AND n2.name IS NOT NULL
              AND n1.name <> n2.name
              AND id(n1) < id(n2)
              AND NOT EXISTS((n1)-[:SIMILAR_TO]-(n2))
              AND (
                (toLower(n1.name) = toLower(n2.name) + 's') OR
                (toLower(n2.name) = toLower(n1.name) + 's') OR
                (toLower(n1.name) = toLower(substring(n2.name, 0, size(n2.name) - 2)) + 'y' AND toLower(n2.name) ENDS WITH 'ies') OR
                (toLower(n2.name) = toLower(substring(n1.name, 0, size(n1.name) - 2)) + 'y' AND toLower(n1.name) ENDS WITH 'ies')
              )
            CREATE (n1)-[:SIMILAR_TO {reason: 'plural_form', created: datetime()}]->(n2)
            RETURN count(*) as plural_matches
        `);
        
        console.log(`Created ${pluralResult.records[0].get('plural_matches')} plural form matches`);
        
        // Handle partial matches for longer names (contains relationship)
        console.log('Creating relationships for partial name matches...');
        
        const partialResult = await session.run(`
            MATCH (n1:Entity), (n2:Entity)
            WHERE n1.name IS NOT NULL 
              AND n2.name IS NOT NULL
              AND n1.name <> n2.name
              AND id(n1) < id(n2)
              AND size(n1.name) > 3 AND size(n2.name) > 3
              AND NOT EXISTS((n1)-[:SIMILAR_TO]-(n2))
              AND (
                toLower(n1.name) CONTAINS toLower(n2.name) OR 
                toLower(n2.name) CONTAINS toLower(n1.name)
              )
            CREATE (n1)-[:SIMILAR_TO {reason: 'partial_name_match', created: datetime()}]->(n2)
            RETURN count(*) as partial_matches
        `);
        
        console.log(`Created ${partialResult.records[0].get('partial_matches')} partial name matches`);
        
        // Get total count
        const totalResult = await session.run(`
            MATCH ()-[r:SIMILAR_TO]->() 
            RETURN count(r) as total_similar_relationships
        `);
        
        console.log(`\n✅ Total SIMILAR_TO relationships: ${totalResult.records[0].get('total_similar_relationships')}`);
        
        // Show some examples
        const exampleResult = await session.run(`
            MATCH (n1)-[r:SIMILAR_TO]->(n2) 
            RETURN n1.name as name1, n2.name as name2, r.reason as reason
            ORDER BY r.reason, n1.name
            LIMIT 20
        `);
        
        console.log('\nExample SIMILAR_TO relationships:');
        exampleResult.records.forEach(record => {
            const reason = record.get('reason');
            const name1 = record.get('name1');
            const name2 = record.get('name2');
            console.log(`  [${reason}] "${name1}" <-> "${name2}"`);
        });
        
        // Show breakdown by reason
        const reasonResult = await session.run(`
            MATCH ()-[r:SIMILAR_TO]->() 
            RETURN r.reason as reason, count(r) as count 
            ORDER BY count DESC
        `);
        
        console.log('\nBreakdown by reason:');
        reasonResult.records.forEach(record => {
            console.log(`  ${record.get('reason')}: ${record.get('count')} relationships`);
        });
        
    } catch (error) {
        console.error('Error creating SIMILAR_TO relationships:', error);
    } finally {
        await session.close();
    }
}

createSimilarRelationshipsOptimized().then(() => {
    driver.close();
    console.log('Optimized SIMILAR_TO relationships creation completed!');
}).catch(error => {
    console.error('Script failed:', error);
    driver.close();
});