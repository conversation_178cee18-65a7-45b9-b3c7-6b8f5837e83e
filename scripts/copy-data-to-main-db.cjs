const neo4j = require('neo4j-driver');

const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

async function copyData() {
    const backupSession = driver.session({ database: 'neo4jbackup' });
    const mainSession = driver.session({ database: 'neo4j' });
    
    try {
        console.log('Starting data copy from neo4jbackup to neo4j...');
        
        // Copy entities with categories in batches
        console.log('Copying Entity nodes with categories...');
        
        const entityResult = await backupSession.run(
            `MATCH (n:Entity) WHERE n.category IS NOT NULL 
             RETURN n.name as name, n.category as category, n.group_id as group_id, 
                    labels(n) as labels, properties(n) as props
             ORDER BY n.name LIMIT 5000`
        );
        
        console.log(`Found ${entityResult.records.length} Entity nodes to copy`);
        
        for (const record of entityResult.records) {
            const name = record.get('name');
            const category = record.get('category');
            const group_id = record.get('group_id');
            const props = record.get('props');
            
            await mainSession.run(
                `CREATE (n:Entity) 
                 SET n.name = $name, n.category = $category, n.group_id = $group_id,
                     n += $props`,
                { name, category, group_id, props }
            );
        }
        
        console.log('Entity nodes copied successfully!');
        
        // Verify the copy
        const countResult = await mainSession.run('MATCH (n:Entity) RETURN count(n) as count');
        console.log(`Total Entity nodes in neo4j database: ${countResult.records[0].get('count')}`);
        
    } catch (error) {
        console.error('Error copying data:', error);
    } finally {
        await backupSession.close();
        await mainSession.close();
    }
}

copyData().then(() => {
    driver.close();
    console.log('Data copy completed!');
}).catch(error => {
    console.error('Script failed:', error);
    driver.close();
});