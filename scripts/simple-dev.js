#!/usr/bin/env node

/**
 * Simple, Reliable Development Server Startup
 * 
 * This script starts services one by one with real verification
 * and proper error handling. No complex health checking - just works.
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import chalk from 'chalk';

const services = [
  {
    name: 'API Server',
    cmd: 'npm',
    args: ['run', 'dev'],
    cwd: '360t-kg-api',
    port: 3002,
    color: 'magenta',
    testUrl: 'http://localhost:3002/',
    startupTime: 8000 // 8 seconds for API + Neo4j connection
  },
  {
    name: 'Proxy Server', 
    cmd: 'npm',
    args: ['run', 'dev'],
    cwd: 'proxy-server',
    port: 3003,
    color: 'cyan',
    testUrl: 'http://localhost:3003/health',
    startupTime: 5000 // 5 seconds for proxy
  },
  {
    name: 'Python AI Service',
    cmd: '.venv/bin/python',
    args: ['main.py'],
    cwd: '.',
    port: 8000,
    color: 'green', 
    testUrl: 'http://localhost:8000/health',
    startupTime: 10000 // 10 seconds for Python + dependencies
  },
  {
    name: 'Frontend',
    cmd: 'npm',
    args: ['run', 'dev'],
    cwd: '360t-kg-ui',
    port: 5177,
    color: 'blue',
    testUrl: 'http://localhost:5177/',
    startupTime: 8000 // 8 seconds for Vite build
  }
];

const runningProcesses = [];
let isShuttingDown = false;

function log(color, message, service = '') {
  const timestamp = new Date().toISOString().slice(11, 23);
  const prefix = service ? `[${service}] ` : '';
  console.log(chalk[color](`${timestamp} ${prefix}${message}`));
}

async function killPortProcesses(port) {
  try {
    const { execSync } = await import('child_process');
    // Kill any existing processes on the port
    execSync(`lsof -ti :${port} | xargs kill -9 2>/dev/null || true`, { stdio: 'ignore' });
    log('yellow', `Cleaned up port ${port}`);
  } catch (error) {
    // Ignore cleanup errors
  }
}

async function testService(service) {
  const maxAttempts = 15; // Increased for Python service startup
  const delay = 2000; // 2 seconds between attempts for more reliable checks
  
  log('cyan', `Testing ${service.name} at ${service.testUrl}...`);
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const response = await fetch(service.testUrl, {
        method: 'GET',
        timeout: 5000, // Increased timeout for better reliability
        headers: { 'User-Agent': 'simple-dev-startup/1.0' }
      });
      
      if (response.ok) {
        // Additional verification for Python AI Service
        if (service.name === 'Python AI Service') {
          try {
            const healthData = await response.json();
            if (healthData.status === 'healthy' && healthData.services) {
              log('green', `✅ ${service.name} is fully initialized (attempt ${attempt})`);
              log('cyan', `   - Neo4j: ${healthData.services.neo4j || 'unknown'}`);
              log('cyan', `   - QA Pipeline: ${healthData.services.qa_pipeline || 'unknown'}`);
              return true;
            } else {
              log('yellow', `⚠️  ${service.name} responding but not fully ready (attempt ${attempt})`);
              if (attempt < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
              }
            }
          } catch (parseError) {
            log('yellow', `⚠️  ${service.name} health check parse error (attempt ${attempt})`);
          }
        } else {
          log('green', `✅ ${service.name} is responding (attempt ${attempt})`);
          return true;
        }
      } else {
        log('yellow', `⚠️  ${service.name} returned ${response.status} (attempt ${attempt})`);
      }
    } catch (error) {
      if (attempt === maxAttempts) {
        log('red', `❌ ${service.name} failed to respond after ${maxAttempts} attempts`);
        log('red', `   Last error: ${error.message}`);
        
        // Provide specific troubleshooting for Python AI Service
        if (service.name === 'Python AI Service') {
          log('yellow', '💡 Troubleshooting Python AI Service:');
          log('yellow', '   1. Check if .venv/bin/python exists');
          log('yellow', '   2. Verify main.py exists in root directory');
          log('yellow', '   3. Check Neo4j is running (bolt://localhost:7687)');
          log('yellow', '   4. Verify Ollama is running (http://localhost:11434)');
        }
        
        return false;
      }
      log('gray', `🔄 ${service.name} not ready, attempt ${attempt}/${maxAttempts} (${error.message.substring(0, 50)}...)`);
    }
    
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  return false;
}

async function startService(service) {
  if (isShuttingDown) return null;
  
  // Clean up port first
  await killPortProcesses(service.port);
  
  log('blue', `🚀 Starting ${service.name}...`);
  
  // Verify command exists
  if (!existsSync(service.cwd)) {
    log('red', `❌ Directory ${service.cwd} does not exist`);
    return null;
  }
  
  const child = spawn(service.cmd, service.args, {
    cwd: service.cwd,
    stdio: ['inherit', 'pipe', 'pipe'],
    env: { ...process.env, FORCE_COLOR: '1' }
  });
  
  // Handle output
  child.stdout.on('data', (data) => {
    process.stdout.write(chalk[service.color](`[${service.name}] `) + data.toString());
  });
  
  child.stderr.on('data', (data) => {
    process.stderr.write(chalk.red(`[${service.name} ERROR] `) + data.toString());
  });
  
  child.on('close', (code) => {
    if (!isShuttingDown) {
      log('red', `❌ ${service.name} exited with code ${code}`);
      cleanup();
    }
  });
  
  child.on('error', (error) => {
    log('red', `❌ Failed to start ${service.name}: ${error.message}`);
    cleanup();
  });
  
  runningProcesses.push({ child, service });
  
  // Wait for startup time
  log('cyan', `⏳ Waiting ${service.startupTime/1000}s for ${service.name} to initialize...`);
  await new Promise(resolve => setTimeout(resolve, service.startupTime));
  
  // Test if service is actually working
  const isWorking = await testService(service);
  
  if (!isWorking) {
    log('red', `❌ ${service.name} failed to start properly`);
    return null;
  }
  
  log('green', `✅ ${service.name} is running successfully`);
  return child;
}

async function cleanup() {
  if (isShuttingDown) return;
  isShuttingDown = true;
  
  log('yellow', '🛑 Shutting down all services...');
  
  // Kill all child processes
  for (const { child, service } of runningProcesses) {
    try {
      log('yellow', `Stopping ${service.name}...`);
      child.kill('SIGTERM');
      
      // Force kill after 3 seconds if still running
      setTimeout(() => {
        if (!child.killed) {
          child.kill('SIGKILL');
        }
      }, 3000);
    } catch (error) {
      // Ignore cleanup errors
    }
  }
  
  // Clean up ports
  for (const service of services) {
    await killPortProcesses(service.port);
  }
  
  log('green', '✅ Cleanup completed');
  process.exit(0);
}

async function main() {
  console.log(chalk.blue.bold('🚀 Simple Development Environment Startup'));
  console.log(chalk.gray('Starting services sequentially with real verification...\n'));
  
  try {
    // Start each service one by one
    for (const service of services) {
      const child = await startService(service);
      if (!child) {
        throw new Error(`Failed to start ${service.name}`);
      }
    }
    
    // All services started successfully
    console.log(chalk.green.bold('\n🎉 All services are running successfully!'));
    console.log(chalk.cyan('Services available at:'));
    console.log(chalk.cyan('  • Frontend: http://localhost:5177/'));
    console.log(chalk.cyan('  • API Backend: http://localhost:3002/'));
    console.log(chalk.cyan('  • Proxy Server: http://localhost:3003/'));
    console.log(chalk.cyan('  • Python AI: http://localhost:8000/'));
    console.log(chalk.gray('\nPress Ctrl+C to stop all services\n'));
    
  } catch (error) {
    log('red', `❌ Startup failed: ${error.message}`);
    await cleanup();
    process.exit(1);
  }
}

// Handle shutdown signals
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('uncaughtException', (error) => {
  log('red', `Uncaught exception: ${error.message}`);
  cleanup();
});
process.on('unhandledRejection', (reason) => {
  log('red', `Unhandled rejection: ${reason}`);
  cleanup();
});

// Start the application
main().catch(error => {
  log('red', `Failed to start: ${error.message}`);
  process.exit(1);
});