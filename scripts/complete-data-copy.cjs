const neo4j = require('neo4j-driver');

const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

async function copyAllData() {
    const backupSession = driver.session({ database: 'neo4jbackup' });
    const mainSession = driver.session({ database: 'neo4j' });
    
    try {
        console.log('Starting complete data copy from neo4jbackup to neo4j...');
        
        // Get all node types and their counts
        const nodeTypesResult = await backupSession.run(
            `MATCH (n) RETURN DISTINCT labels(n) as labels, count(n) as count ORDER BY count DESC`
        );
        
        console.log('Node types to copy:');
        nodeTypesResult.records.forEach(record => {
            const labels = record.get('labels');
            const count = record.get('count');
            console.log(`  ${labels.join(', ')}: ${count} nodes`);
        });
        
        let totalCopied = 0;
        
        // Copy each node type
        for (const record of nodeTypesResult.records) {
            const labels = record.get('labels');
            const count = record.get('count').toNumber();
            const labelFilter = labels.map(l => `n:\`${l}\``).join(' AND ');
            
            console.log(`\nCopying ${count} nodes with labels: ${labels.join(', ')}...`);
            
            // Copy in batches to avoid memory issues
            const batchSize = 1000;
            let copiedForType = 0;
            
            for (let skip = 0; skip < count; skip += batchSize) {
                const batchResult = await backupSession.run(
                    `MATCH (n) WHERE ${labelFilter} 
                     RETURN id(n) as nodeId, labels(n) as labels, properties(n) as props
                     SKIP $skip LIMIT $limit`,
                    { skip: neo4j.int(skip), limit: neo4j.int(Math.min(batchSize, count - skip)) }
                );
                
                // Create nodes in main database
                for (const nodeRecord of batchResult.records) {
                    const nodeLabels = nodeRecord.get('labels');
                    const props = nodeRecord.get('props');
                    
                    const labelString = nodeLabels.map(l => `\`${l}\``).join(':');
                    
                    await mainSession.run(
                        `CREATE (n:${labelString}) SET n += $props`,
                        { props }
                    );
                    
                    copiedForType++;
                }
                
                console.log(`  Progress: ${copiedForType}/${count} nodes copied...`);
            }
            
            totalCopied += copiedForType;
            console.log(`✓ Completed: ${copiedForType} nodes`);
        }
        
        console.log(`\n✅ Total nodes copied: ${totalCopied}`);
        
        // Now copy relationships (excluding SIMILAR_TO which we'll recreate)
        console.log('\nCopying relationships (excluding SIMILAR_TO)...');
        
        const relTypesResult = await backupSession.run(
            `MATCH ()-[r]->() WHERE type(r) <> 'SIMILAR_TO'
             RETURN DISTINCT type(r) as relType, count(r) as count ORDER BY count DESC`
        );
        
        console.log('Relationship types to copy:');
        relTypesResult.records.forEach(record => {
            console.log(`  ${record.get('relType')}: ${record.get('count')} relationships`);
        });
        
        let totalRels = 0;
        
        // Copy relationships in batches
        for (const record of relTypesResult.records) {
            const relType = record.get('relType');
            const count = record.get('count').toNumber();
            
            console.log(`\nCopying ${count} ${relType} relationships...`);
            
            const batchSize = 500;
            let copiedRels = 0;
            
            for (let skip = 0; skip < count; skip += batchSize) {
                // Get relationships with source and target node properties
                const relResult = await backupSession.run(
                    `MATCH (source)-[r:\`${relType}\`]->(target)
                     RETURN properties(source) as sourceProps, labels(source) as sourceLabels,
                            properties(target) as targetProps, labels(target) as targetLabels,
                            properties(r) as relProps
                     SKIP $skip LIMIT $limit`,
                    { skip: neo4j.int(skip), limit: neo4j.int(Math.min(batchSize, count - skip)) }
                );
                
                // Create relationships in main database
                for (const relRecord of relResult.records) {
                    const sourceProps = relRecord.get('sourceProps');
                    const targetProps = relRecord.get('targetProps');
                    const relProps = relRecord.get('relProps');
                    
                    // Find matching nodes and create relationship
                    await mainSession.run(
                        `MATCH (source), (target)
                         WHERE source = $sourceProps AND target = $targetProps
                         CREATE (source)-[r:\`${relType}\`]->(target)
                         SET r += $relProps`,
                        { sourceProps, targetProps, relProps }
                    );
                    
                    copiedRels++;
                }
                
                console.log(`  Progress: ${copiedRels}/${count} relationships copied...`);
            }
            
            totalRels += copiedRels;
            console.log(`✓ Completed: ${copiedRels} relationships`);
        }
        
        console.log(`\n✅ Total relationships copied: ${totalRels}`);
        
        // Verify the copy
        const nodeCountResult = await mainSession.run('MATCH (n) RETURN count(n) as count');
        const relCountResult = await mainSession.run('MATCH ()-[r]->() WHERE type(r) <> \'SIMILAR_TO\' RETURN count(r) as count');
        
        console.log(`\nVerification:`);
        console.log(`  Total nodes in neo4j database: ${nodeCountResult.records[0].get('count')}`);
        console.log(`  Total relationships (excluding SIMILAR_TO): ${relCountResult.records[0].get('count')}`);
        
    } catch (error) {
        console.error('Error copying data:', error);
    } finally {
        await backupSession.close();
        await mainSession.close();
    }
}

copyAllData().then(() => {
    driver.close();
    console.log('Complete data copy finished!');
}).catch(error => {
    console.error('Script failed:', error);
    driver.close();
});