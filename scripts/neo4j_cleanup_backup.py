#!/usr/bin/env python3
"""
Neo4j Database Backup Script
Creates a backup before database cleanup operations
"""

from neo4j import GraphDatabase
import json
import os
from datetime import datetime

def create_database_backup():
    """Create a comprehensive backup of the Neo4j database"""
    
    # Connection setup
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '1979@rabu'))
    
    backup_dir = '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/backups'
    os.makedirs(backup_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"{backup_dir}/neo4j_backup_{timestamp}.json"
    
    backup_data = {
        'timestamp': timestamp,
        'nodes': [],
        'relationships': [],
        'statistics': {}
    }
    
    with driver.session() as session:
        print("📊 Collecting database statistics...")
        
        # Get node statistics
        result = session.run('MATCH (n) RETURN DISTINCT labels(n) as labels, count(n) as count ORDER BY count DESC')
        node_stats = {}
        for record in result:
            labels = str(record['labels'])
            count = record['count']
            node_stats[labels] = count
        
        backup_data['statistics']['nodes'] = node_stats
        
        # Get relationship statistics
        result = session.run('CALL db.relationshipTypes()')
        rel_types = [record[0] for record in result]
        
        rel_stats = {}
        for rel_type in rel_types:
            result = session.run(f'MATCH ()-[r:{rel_type}]->() RETURN count(r) as count')
            count = result.single()['count']
            rel_stats[rel_type] = count
            
        backup_data['statistics']['relationships'] = rel_stats
        
        print("💾 Backing up nodes (this may take a while)...")
        
        # Backup all nodes
        result = session.run("""
            MATCH (n) 
            RETURN id(n) as node_id, labels(n) as labels, properties(n) as properties
        """)
        
        node_count = 0
        for record in result:
            backup_data['nodes'].append({
                'id': record['node_id'],
                'labels': record['labels'],
                'properties': dict(record['properties'])
            })
            node_count += 1
            if node_count % 10000 == 0:
                print(f"  Backed up {node_count} nodes...")
        
        print(f"✅ Backed up {node_count} nodes")
        
        print("💾 Backing up relationships...")
        
        # Backup all relationships
        result = session.run("""
            MATCH (a)-[r]->(b) 
            RETURN id(a) as start_id, id(b) as end_id, type(r) as rel_type, properties(r) as properties
        """)
        
        rel_count = 0
        for record in result:
            backup_data['relationships'].append({
                'start_id': record['start_id'],
                'end_id': record['end_id'],
                'type': record['rel_type'],
                'properties': dict(record['properties'])
            })
            rel_count += 1
            if rel_count % 10000 == 0:
                print(f"  Backed up {rel_count} relationships...")
        
        print(f"✅ Backed up {rel_count} relationships")
    
    driver.close()
    
    # Save backup to file
    print(f"💾 Saving backup to {backup_file}...")
    with open(backup_file, 'w') as f:
        json.dump(backup_data, f, indent=2)
    
    print(f"✅ Backup completed successfully!")
    print(f"📁 Backup file: {backup_file}")
    print(f"📊 Statistics:")
    print(f"   - Nodes: {len(backup_data['nodes'])}")
    print(f"   - Relationships: {len(backup_data['relationships'])}")
    
    return backup_file

if __name__ == '__main__':
    create_database_backup()