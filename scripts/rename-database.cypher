// <PERSON><PERSON><PERSON> to rename neo4jbackup database to neo4j
// Run this after starting Neo4j Desktop instance

// First, check what databases exist
SHOW DATABASES;

// Drop the existing neo4j database if it exists (be careful!)
// Uncomment the next line only if you're sure
// DROP DATABASE neo4j IF EXISTS;

// Create new neo4j database as copy of neo4jbackup
// Uncomment the next line to execute
// CREATE DATABASE neo4j AS COPY OF neo4jbackup;

// After successful copy, drop the backup database
// Uncomment the next line to execute
// DROP DATABASE neo4jbackup;

// Final verification
// SHOW DATABASES;