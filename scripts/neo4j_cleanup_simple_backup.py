#!/usr/bin/env python3
"""
Simple Neo4j Database Statistics Backup
Creates a lightweight backup of database statistics before cleanup
"""

from neo4j import GraphDatabase
import json
import os
from datetime import datetime

def create_simple_backup():
    """Create a simple statistical backup of the database"""
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '1979@rabu'))
    
    backup_dir = '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/backups'
    os.makedirs(backup_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"{backup_dir}/neo4j_stats_backup_{timestamp}.json"
    
    backup_data = {
        'timestamp': timestamp,
        'total_nodes': 0,
        'total_relationships': 0,
        'node_statistics': {},
        'relationship_statistics': {},
        'nodes_to_keep': 0,
        'nodes_to_delete': 0
    }
    
    with driver.session() as session:
        print("📊 Collecting database statistics...")
        
        # Total counts
        result = session.run('MATCH (n) RETURN count(n) as total')
        backup_data['total_nodes'] = result.single()['total']
        
        result = session.run('MATCH ()-[r]->() RETURN count(r) as total')
        backup_data['total_relationships'] = result.single()['total']
        
        # Node statistics
        result = session.run('MATCH (n) RETURN DISTINCT labels(n) as labels, count(n) as count ORDER BY count DESC')
        for record in result:
            labels = str(record['labels'])
            count = record['count']
            backup_data['node_statistics'][labels] = count
        
        # Relationship statistics
        result = session.run('CALL db.relationshipTypes()')
        rel_types = [record[0] for record in result]
        
        for rel_type in rel_types:
            result = session.run(f'MATCH ()-[r:{rel_type}]->() RETURN count(r) as count')
            count = result.single()['count']
            backup_data['relationship_statistics'][rel_type] = count
        
        # Nodes to keep vs delete
        result = session.run('MATCH (n) WHERE n:Entity OR n:Episodic RETURN count(n) as keep_count')
        backup_data['nodes_to_keep'] = result.single()['keep_count']
        
        result = session.run('MATCH (n) WHERE NOT (n:Entity OR n:Episodic) RETURN count(n) as delete_count')
        backup_data['nodes_to_delete'] = result.single()['delete_count']
    
    driver.close()
    
    # Save backup to file
    with open(backup_file, 'w') as f:
        json.dump(backup_data, f, indent=2)
    
    print(f"✅ Statistics backup completed!")
    print(f"📁 Backup file: {backup_file}")
    print(f"📊 Summary:")
    print(f"   - Total nodes: {backup_data['total_nodes']}")
    print(f"   - Total relationships: {backup_data['total_relationships']}")
    print(f"   - Nodes to keep: {backup_data['nodes_to_keep']}")
    print(f"   - Nodes to delete: {backup_data['nodes_to_delete']}")
    
    return backup_file

if __name__ == '__main__':
    create_simple_backup()