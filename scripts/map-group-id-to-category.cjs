const fs = require('fs');
const neo4j = require('neo4j-driver');

// Read CSV mapping file
const csvContent = fs.readFileSync('../360t-kg-ui/docs/group_id-to-filename-mapping.csv', 'utf8');
const lines = csvContent.split('\n');
const mappings = {};

// Parse CSV (skip header)
for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line) {
        const [group_id, filename, category, url] = line.split(',');
        if (group_id && category) {
            mappings[group_id] = category;
        }
    }
}

console.log(`Loaded ${Object.keys(mappings).length} group_id to category mappings`);

// Connect to Neo4j
const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

async function updateCategories() {
    const session = driver.session({ database: 'neo4jbackup' });
    
    try {
        let totalUpdated = 0;
        
        for (const [group_id, category] of Object.entries(mappings)) {
            console.log(`Updating group_id: ${group_id} -> category: ${category}`);
            
            const result = await session.run(
                `MATCH (n) WHERE n.group_id = $group_id 
                 SET n.category = $category 
                 RETURN count(n) as updated`,
                { group_id, category }
            );
            
            const updated = result.records[0].get('updated').toNumber();
            totalUpdated += updated;
            
            if (updated > 0) {
                console.log(`  Updated ${updated} nodes`);
            }
        }
        
        console.log(`\nTotal nodes updated: ${totalUpdated}`);
        
        // Verify the updates
        const verifyResult = await session.run(
            `MATCH (n) WHERE n.category IS NOT NULL 
             RETURN n.category as category, count(n) as count 
             ORDER BY count DESC LIMIT 10`
        );
        
        console.log('\nCategory distribution after update:');
        verifyResult.records.forEach(record => {
            console.log(`  ${record.get('category')}: ${record.get('count')} nodes`);
        });
        
    } catch (error) {
        console.error('Error updating categories:', error);
    } finally {
        await session.close();
    }
}

updateCategories().then(() => {
    driver.close();
    console.log('Category mapping completed!');
}).catch(error => {
    console.error('Script failed:', error);
    driver.close();
});