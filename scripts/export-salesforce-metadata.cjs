const neo4j = require('neo4j-driver');
const fs = require('fs');

const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

async function exportSalesforceMetadata() {
    const session = driver.session({ database: 'neo4j' });
    
    try {
        console.log('Exporting Salesforce metadata nodes...');
        
        // Export RelationID nodes
        const relationIdResult = await session.run(
            'MATCH (n:RelationID) RETURN properties(n) as props'
        );
        
        console.log(`Found ${relationIdResult.records.length} RelationID nodes`);
        
        const relationIdData = relationIdResult.records.map(record => record.get('props'));
        fs.writeFileSync(
            '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/backups/relationid-backup.json',
            JSON.stringify(relationIdData, null, 2)
        );
        
        // Export TaskRelation nodes  
        const taskRelationResult = await session.run(
            'MATCH (n:TaskRelation) RETURN properties(n) as props'
        );
        
        console.log(`Found ${taskRelationResult.records.length} TaskRelation nodes`);
        
        const taskRelationData = taskRelationResult.records.map(record => record.get('props'));
        fs.writeFileSync(
            '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/backups/taskrelation-backup.json',
            JSON.stringify(taskRelationData, null, 2)
        );
        
        console.log('✅ Salesforce metadata exported to backups/ directory');
        
    } catch (error) {
        console.error('Error exporting metadata:', error);
    } finally {
        await session.close();
    }
}

exportSalesforceMetadata().then(() => {
    driver.close();
    console.log('Export completed!');
}).catch(error => {
    console.error('Export failed:', error);
    driver.close();
});