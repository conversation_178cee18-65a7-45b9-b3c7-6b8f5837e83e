#!/usr/bin/env python3
"""
Neo4j Database Cleanup Script
Keeps only Entity and Episodic nodes, and only RELATES_TO and MENTIONS relationships
"""

from neo4j import GraphDatabase
import time

class Neo4jCleaner:
    def __init__(self):
        self.driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '1979@rabu'))
    
    def close(self):
        self.driver.close()
    
    def get_counts(self):
        """Get current database counts"""
        with self.driver.session() as session:
            # Total counts
            result = session.run('MATCH (n) RETURN count(n) as total_nodes')
            total_nodes = result.single()['total_nodes']
            
            result = session.run('MATCH ()-[r]->() RETURN count(r) as total_rels')
            total_rels = result.single()['total_rels']
            
            # Entity and Episodic counts
            result = session.run('MATCH (n) WHERE n:Entity OR n:Episodic RETURN count(n) as keep_nodes')
            keep_nodes = result.single()['keep_nodes']
            
            # RELATES_TO and MENTIONS counts
            result = session.run('MATCH ()-[r]->() WHERE type(r) = "RELATES_TO" OR type(r) = "MENTIONS" RETURN count(r) as keep_rels')
            keep_rels = result.single()['keep_rels']
            
            return {
                'total_nodes': total_nodes,
                'total_relationships': total_rels,
                'nodes_to_keep': keep_nodes,
                'relationships_to_keep': keep_rels,
                'nodes_to_delete': total_nodes - keep_nodes,
                'relationships_to_delete': total_rels - keep_rels
            }
    
    def delete_unwanted_relationships(self, batch_size=10000):
        """Delete all relationships except RELATES_TO and MENTIONS"""
        print("🗑️ Starting relationship cleanup...")
        
        with self.driver.session() as session:
            # Get all relationship types first
            result = session.run("CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType")
            all_types = [record['relationshipType'] for record in result]
            
            # Filter out the ones we want to keep
            unwanted_types = [t for t in all_types if t not in ['RELATES_TO', 'MENTIONS']]
            
            print(f"📋 Relationship types to delete: {unwanted_types}")
            
            total_deleted = 0
            for rel_type in unwanted_types:
                print(f"  🗑️ Deleting {rel_type} relationships...")
                
                while True:
                    result = session.run(f"""
                        MATCH ()-[r:{rel_type}]->()
                        WITH r LIMIT {batch_size}
                        DELETE r
                        RETURN count(r) as deleted
                    """)
                    
                    deleted = result.single()['deleted']
                    total_deleted += deleted
                    
                    if deleted == 0:
                        break
                    
                    print(f"    ✅ Deleted {deleted} {rel_type} relationships (total: {total_deleted})")
            
            print(f"✅ Relationship cleanup completed! Deleted {total_deleted} relationships")
            return total_deleted
    
    def delete_unwanted_nodes(self, batch_size=5000):
        """Delete all nodes except Entity and Episodic"""
        print("🗑️ Starting node cleanup...")
        
        with self.driver.session() as session:
            total_deleted = 0
            
            while True:
                # Delete nodes that don't have Entity or Episodic labels
                result = session.run(f"""
                    MATCH (n)
                    WHERE NOT (n:Entity OR n:Episodic)
                    WITH n LIMIT {batch_size}
                    DETACH DELETE n
                    RETURN count(n) as deleted
                """)
                
                deleted = result.single()['deleted']
                total_deleted += deleted
                
                if deleted == 0:
                    break
                
                print(f"  ✅ Deleted {deleted} nodes (total: {total_deleted})")
                
                # Small delay to prevent overwhelming the database
                time.sleep(0.1)
            
            print(f"✅ Node cleanup completed! Deleted {total_deleted} nodes")
            return total_deleted
    
    def clean_orphaned_relationships(self):
        """Clean up any orphaned relationships after node deletion"""
        print("🧹 Cleaning up orphaned relationships...")
        
        with self.driver.session() as session:
            # This query will find relationships where either start or end node is missing
            # but in our case, DETACH DELETE should have handled this automatically
            result = session.run("""
                MATCH ()-[r]->()
                RETURN count(r) as remaining_rels
            """)
            
            remaining = result.single()['remaining_rels']
            print(f"✅ {remaining} relationships remain after cleanup")
            return remaining
    
    def verify_cleanup(self):
        """Verify the cleanup was successful"""
        print("🔍 Verifying cleanup results...")
        
        counts = self.get_counts()
        
        print("📊 Final Database State:")
        print(f"  📦 Total nodes: {counts['total_nodes']}")
        print(f"  🔗 Total relationships: {counts['total_relationships']}")
        
        with self.driver.session() as session:
            # Verify only Entity and Episodic nodes remain
            result = session.run('MATCH (n) RETURN DISTINCT labels(n) as labels, count(n) as count ORDER BY count DESC')
            print("  📋 Node types remaining:")
            for record in result:
                labels = record['labels']
                count = record['count']
                print(f"    {labels}: {count}")
            
            # Verify only RELATES_TO and MENTIONS relationships remain
            result = session.run('CALL db.relationshipTypes()')
            rel_types = [record[0] for record in result]
            print(f"  🔗 Relationship types remaining: {rel_types}")
            
            for rel_type in rel_types:
                result = session.run(f'MATCH ()-[r:{rel_type}]->() RETURN count(r) as count')
                count = result.single()['count']
                print(f"    {rel_type}: {count}")
        
        return counts

def main():
    """Main cleanup function"""
    cleaner = Neo4jCleaner()
    
    try:
        print("🚀 Starting Neo4j database cleanup...")
        print("=" * 60)
        
        # Show initial state
        initial_counts = cleaner.get_counts()
        print("📊 Initial Database State:")
        print(f"  📦 Total nodes: {initial_counts['total_nodes']}")
        print(f"  🔗 Total relationships: {initial_counts['total_relationships']}")
        print(f"  ✅ Nodes to keep: {initial_counts['nodes_to_keep']}")
        print(f"  🗑️ Nodes to delete: {initial_counts['nodes_to_delete']}")
        print(f"  ✅ Relationships to keep: {initial_counts['relationships_to_keep']}")
        print(f"  🗑️ Relationships to delete: {initial_counts['relationships_to_delete']}")
        print()
        
        # Confirm before proceeding
        confirmation = input("⚠️ This will permanently delete data. Continue? (yes/no): ")
        if confirmation.lower() != 'yes':
            print("❌ Operation cancelled by user")
            return
        
        # Phase 1: Delete unwanted relationships
        print("\n" + "=" * 60)
        print("PHASE 1: Deleting unwanted relationships")
        print("=" * 60)
        cleaner.delete_unwanted_relationships()
        
        # Phase 2: Delete unwanted nodes
        print("\n" + "=" * 60)
        print("PHASE 2: Deleting unwanted nodes")
        print("=" * 60)
        cleaner.delete_unwanted_nodes()
        
        # Phase 3: Clean up orphaned relationships
        print("\n" + "=" * 60)
        print("PHASE 3: Cleaning up orphaned relationships")
        print("=" * 60)
        cleaner.clean_orphaned_relationships()
        
        # Phase 4: Verify results
        print("\n" + "=" * 60)
        print("PHASE 4: Verification")
        print("=" * 60)
        final_counts = cleaner.verify_cleanup()
        
        print("\n" + "=" * 60)
        print("🎉 CLEANUP COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print(f"📦 Nodes: {initial_counts['total_nodes']} → {final_counts['total_nodes']}")
        print(f"🔗 Relationships: {initial_counts['total_relationships']} → {final_counts['total_relationships']}")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        raise
    finally:
        cleaner.close()

if __name__ == '__main__':
    main()