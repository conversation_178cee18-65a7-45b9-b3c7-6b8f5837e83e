// <PERSON><PERSON><PERSON> to create SIMILAR_TO relationships between nodes
// This creates relationships based on shared categories and properties

// Switch to the neo4j database
:use neo4j;

// Create SIMILAR_TO relationships between nodes that share the same category
MATCH (n1:Document), (n2:Document)
WHERE n1.category = n2.category 
  AND n1 <> n2
  AND NOT EXISTS((n1)-[:SIMILAR_TO]-(n2))
WITH n1, n2
CREATE (n1)-[:SIMILAR_TO {reason: 'same_category', category: n1.category, created: datetime()}]->(n2);

// Create SIMILAR_TO relationships between nodes with similar group_id patterns
MATCH (n1:Document), (n2:Document)
WHERE n1.group_id CONTAINS 'user_guides' 
  AND n2.group_id CONTAINS 'user_guides'
  AND n1 <> n2
  AND NOT EXISTS((n1)-[:SIMILAR_TO]-(n2))
  AND (n1.group_id CONTAINS split(n2.group_id, '_')[2] OR n2.group_id CONTAINS split(n1.group_id, '_')[2])
WITH n1, n2
CREATE (n1)-[:SIMILAR_TO {reason: 'similar_guide_type', created: datetime()}]->(n2);

// Create SIMILAR_TO relationships between nodes with similar titles or content
MATCH (n1:Document), (n2:Document)
WHERE n1.title IS NOT NULL 
  AND n2.title IS NOT NULL
  AND n1 <> n2
  AND NOT EXISTS((n1)-[:SIMILAR_TO]-(n2))
  AND (toLower(n1.title) CONTAINS toLower(split(n2.title, ' ')[0]) 
       OR toLower(n2.title) CONTAINS toLower(split(n1.title, ' ')[0]))
WITH n1, n2
CREATE (n1)-[:SIMILAR_TO {reason: 'similar_title', created: datetime()}]->(n2);

// Count the created relationships
MATCH ()-[r:SIMILAR_TO]->()
RETURN count(r) as similar_relationships_created;