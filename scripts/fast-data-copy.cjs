const neo4j = require('neo4j-driver');

const driver = neo4j.driver(
    'bolt://localhost:7687',
    neo4j.auth.basic('neo4j', '1979@rabu')
);

async function fastCopy() {
    const backupSession = driver.session({ database: 'neo4jbackup' });
    const mainSession = driver.session({ database: 'neo4j' });
    
    try {
        console.log('Fast copying all nodes...');
        
        // Copy all nodes with their original IDs for relationship mapping
        const nodeResult = await backupSession.run(
            `MATCH (n) 
             RETURN id(n) as originalId, labels(n) as labels, properties(n) as props
             ORDER BY id(n)`
        );
        
        console.log(`Found ${nodeResult.records.length} nodes to copy`);
        
        const idMapping = new Map(); // originalId -> newId
        let copiedNodes = 0;
        const batchSize = 1000;
        
        for (let i = 0; i < nodeResult.records.length; i += batchSize) {
            const batch = nodeResult.records.slice(i, i + batchSize);
            
            // Create nodes in batch
            const tx = mainSession.beginTransaction();
            
            try {
                for (const record of batch) {
                    const originalId = record.get('originalId').toNumber();
                    const labels = record.get('labels');
                    const props = record.get('props');
                    
                    const labelString = labels.map(l => `\`${l}\``).join(':');
                    
                    const result = await tx.run(
                        `CREATE (n:${labelString}) SET n += $props RETURN id(n) as newId`,
                        { props }
                    );
                    
                    const newId = result.records[0].get('newId').toNumber();
                    idMapping.set(originalId, newId);
                    copiedNodes++;
                }
                
                await tx.commit();
                console.log(`Progress: ${copiedNodes}/${nodeResult.records.length} nodes copied`);
                
            } catch (error) {
                await tx.rollback();
                throw error;
            }
        }
        
        console.log(`✅ Copied ${copiedNodes} nodes`);
        
        // Now copy relationships (excluding SIMILAR_TO)
        console.log('Copying relationships...');
        
        const relResult = await backupSession.run(
            `MATCH (source)-[r]->(target) 
             WHERE type(r) <> 'SIMILAR_TO'
             RETURN id(source) as sourceId, id(target) as targetId, 
                    type(r) as relType, properties(r) as relProps`
        );
        
        console.log(`Found ${relResult.records.length} relationships to copy`);
        
        let copiedRels = 0;
        
        for (let i = 0; i < relResult.records.length; i += batchSize) {
            const batch = relResult.records.slice(i, i + batchSize);
            
            const tx = mainSession.beginTransaction();
            
            try {
                for (const record of batch) {
                    const sourceId = record.get('sourceId').toNumber();
                    const targetId = record.get('targetId').toNumber();
                    const relType = record.get('relType');
                    const relProps = record.get('relProps');
                    
                    const newSourceId = idMapping.get(sourceId);
                    const newTargetId = idMapping.get(targetId);
                    
                    if (newSourceId && newTargetId) {
                        await tx.run(
                            `MATCH (source), (target) 
                             WHERE id(source) = $sourceId AND id(target) = $targetId
                             CREATE (source)-[r:\`${relType}\`]->(target)
                             SET r += $relProps`,
                            { sourceId: neo4j.int(newSourceId), targetId: neo4j.int(newTargetId), relProps }
                        );
                        copiedRels++;
                    }
                }
                
                await tx.commit();
                console.log(`Progress: ${copiedRels} relationships copied`);
                
            } catch (error) {
                await tx.rollback();
                console.error('Batch failed, continuing...', error.message);
            }
        }
        
        console.log(`✅ Copied ${copiedRels} relationships`);
        
        // Verify
        const finalNodeCount = await mainSession.run('MATCH (n) RETURN count(n) as count');
        const finalRelCount = await mainSession.run('MATCH ()-[r]->() RETURN count(r) as count');
        
        console.log('\nFinal verification:');
        console.log(`  Nodes: ${finalNodeCount.records[0].get('count')}`);
        console.log(`  Relationships: ${finalRelCount.records[0].get('count')}`);
        
    } catch (error) {
        console.error('Error in fast copy:', error);
    } finally {
        await backupSession.close();
        await mainSession.close();
    }
}

fastCopy().then(() => {
    driver.close();
    console.log('Fast data copy completed!');
}).catch(error => {
    console.error('Fast copy failed:', error);
    driver.close();
});