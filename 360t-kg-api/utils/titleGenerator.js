/**
 * Utility functions for generating smart conversation titles from user messages
 */

/**
 * Generates a clean, readable title from a user message
 * @param {string} message - The user's first message
 * @returns {string} - A clean title for the conversation
 */
function generateTitleFromMessage(message) {
  if (!message || typeof message !== 'string') {
    return 'New Conversation';
  }

  // Clean the message
  let cleanMessage = message.trim();
  
  // Remove common prefixes/patterns
  cleanMessage = cleanMessage
    .replace(/^(what|how|why|when|where|who|can|could|would|should|is|are|am|do|does|did|tell me|explain|help me)/i, '')
    .trim();
  
  // Remove special characters and extra spaces
  cleanMessage = cleanMessage
    .replace(/[^\w\s.,!?-]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
  
  // Capitalize first letter
  if (cleanMessage.length > 0) {
    cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
  }
  
  // Truncate if too long
  const maxLength = 50;
  if (cleanMessage.length > maxLength) {
    cleanMessage = cleanMessage.substring(0, maxLength).trim();
    // Try to break at word boundary
    const lastSpace = cleanMessage.lastIndexOf(' ');
    if (lastSpace > maxLength * 0.7) {
      cleanMessage = cleanMessage.substring(0, lastSpace);
    }
    cleanMessage += '...';
  }
  
  // Return original (truncated) if cleaning resulted in empty string
  if (!cleanMessage || cleanMessage.length < 3) {
    const originalTruncated = message.length > maxLength 
      ? message.substring(0, maxLength).trim() + '...'
      : message;
    return originalTruncated.charAt(0).toUpperCase() + originalTruncated.slice(1);
  }
  
  return cleanMessage;
}

/**
 * Validates if a message is suitable for title generation
 * @param {string} message - The message to validate
 * @returns {boolean} - Whether the message is suitable for a title
 */
function isValidForTitle(message) {
  if (!message || typeof message !== 'string') {
    return false;
  }
  
  const trimmed = message.trim();
  
  // Too short
  if (trimmed.length < 3) {
    return false;
  }
  
  // Too long (likely not a question)
  if (trimmed.length > 200) {
    return false;
  }
  
  // Contains mostly special characters
  const alphanumericCount = (trimmed.match(/[a-zA-Z0-9]/g) || []).length;
  if (alphanumericCount < trimmed.length * 0.5) {
    return false;
  }
  
  return true;
}

/**
 * Extracts key terms from a message for enhanced title generation
 * @param {string} message - The user message
 * @returns {string[]} - Array of key terms
 */
function extractKeyTerms(message) {
  if (!message || typeof message !== 'string') {
    return [];
  }
  
  // Common stop words to filter out
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
    'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does',
    'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'what', 'how',
    'when', 'where', 'why', 'who', 'which', 'that', 'this', 'these', 'those'
  ]);
  
  const words = message
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word));
  
  // Return unique terms, prioritizing longer words
  return [...new Set(words)].sort((a, b) => b.length - a.length).slice(0, 5);
}

module.exports = {
  generateTitleFromMessage,
  isValidForTitle,
  extractKeyTerms
};