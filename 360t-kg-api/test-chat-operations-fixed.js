const { Pool } = require('pg');
const config = require('./config/database.js');

async function testChatFlowFixed() {
  const pool = new Pool(config);
  
  try {
    console.log('🧪 Testing chat flow with corrected database queries...\n');
    
    // Test 1: Database connection
    await pool.query('SELECT 1');
    console.log('✅ Database connection successful');
    
    // Test 2: Create test user (using actual schema)
    const userResult = await pool.query(
      'INSERT INTO users (username, email) VALUES ($1, $2) RETURNING id',
      ['test-user-123', '<EMAIL>']
    );
    const userId = userResult.rows[0].id;
    console.log('✅ Created test user:', userId);
    
    // Test 3: Create chat session
    const sessionResult = await pool.query(
      'INSERT INTO chat_sessions (user_id, title) VALUES ($1, $2) RETURNING id, user_id, title, created_at',
      [userId, 'QA Test']
    );
    console.log('✅ Created conversation:', sessionResult.rows[0]);
    
    // Test 4: Add user message
    const msgResult = await pool.query(
      'INSERT INTO messages (conversation_id, role, content) VALUES ($1, $2, $3) RETURNING id, conversation_id, role, content, created_at',
      [sessionResult.rows[0].id, 'user', 'what is ems?']
    );
    console.log('✅ Added message:', msgResult.rows[0]);
    
    // Test 5: Add assistant response
    const assistantResult = await pool.query(
      'INSERT INTO messages (conversation_id, role, content) VALUES ($1, $2, $3) RETURNING id, conversation_id, role, content, created_at',
      [sessionResult.rows[0].id, 'assistant', 'EMS stands for Emergency Medical Services.']
    );
    console.log('✅ Added assistant response:', assistantResult.rows[0]);
    
    // Test 6: Retrieve messages
    const msgs = await pool.query(
      'SELECT * FROM messages WHERE conversation_id = $1 ORDER BY created_at ASC',
      [sessionResult.rows[0].id]
    );
    console.log('✅ Retrieved messages:', msgs.rows.length, 'messages');
    msgs.rows.forEach(msg => {
      console.log(`  - ${msg.role}: ${msg.content.substring(0, 50)}...`);
    });
    
    // Test 7: Get complete conversation
    const conversation = await pool.query(`
      SELECT
        cs.id,
        cs.title,
        cs.created_at,
        json_agg(
          json_build_object(
            'id', m.id,
            'role', m.role,
            'content', m.content,
            'created_at', m.created_at
          ) ORDER BY m.created_at
        ) as messages
      FROM chat_sessions cs
      LEFT JOIN messages m ON cs.id = m.conversation_id
      WHERE cs.id = $1
      GROUP BY cs.id
    `, [sessionResult.rows[0].id]);
    
    console.log('✅ Retrieved complete conversation:', JSON.stringify(conversation.rows[0], null, 2));
    
    // Test 8: Clean up test data
    await pool.query('DELETE FROM messages WHERE conversation_id = $1', [sessionResult.rows[0].id]);
    await pool.query('DELETE FROM chat_sessions WHERE id = $1', [sessionResult.rows[0].id]);
    await pool.query('DELETE FROM users WHERE id = $1', [userId]);
    console.log('✅ Cleaned up test data');
    
    console.log('\n🎉 Chat flow test PASSED!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack trace:', error.stack);
    
    // Provide more detailed error information
    if (error.code === '42P01') {
      console.error('💡 Hint: Table does not exist. Run the database schema setup first.');
    } else if (error.code === '23503') {
      console.error('💡 Hint: Foreign key constraint violation. Ensure referenced records exist.');
    } else if (error.code === '23505') {
      console.error('💡 Hint: Unique constraint violation. Check for duplicate values.');
    }
    
  } finally {
    await pool.end();
  }
}

// Run the test
if (require.main === module) {
  testChatFlowFixed();
}

module.exports = { testChatFlowFixed };