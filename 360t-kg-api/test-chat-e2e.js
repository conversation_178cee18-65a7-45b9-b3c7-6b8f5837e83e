/**
 * Comprehensive End-to-End Test Suite for Chat Functionality
 * 
 * This test suite provides complete end-to-end testing for the chat system,
 * including navigation, query submission, response validation, and error handling.
 */

const puppeteer = require('puppeteer');
const { expect } = require('chai');
const { describe, it, before, after } = require('mocha');

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  timeout: 180000, // 180 seconds for response timeout
  headless: process.env.TEST_HEADLESS !== 'false',
  viewport: { width: 1280, height: 720 }
};

describe('Chat E2E Test Suite', function() {
  this.timeout(TEST_CONFIG.timeout + 30000); // Add buffer for setup/teardown
  
  let browser;
  let page;

  before(async function() {
    // Launch browser
    browser = await puppeteer.launch({
      headless: TEST_CONFIG.headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    page = await browser.newPage();
    await page.setViewport(TEST_CONFIG.viewport);
    
    // Set up console error monitoring
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.error('Browser Console Error:', msg.text());
      }
    });
    
    page.on('pageerror', (error) => {
      console.error('Page Error:', error);
    });
  });

  after(async function() {
    if (browser) {
      await browser.close();
    }
  });

  describe('End-to-End Chat Flow', function() {
    it('should navigate to /chat and load successfully', async function() {
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      
      // Wait for page to load
      await page.waitForSelector('[data-testid="chat-container"]', { timeout: 10000 });
      
      // Verify page title
      const title = await page.title();
      expect(title).to.include('Chat');
      
      // Verify chat input is present
      const chatInput = await page.$('[data-testid="chat-input"]');
      expect(chatInput).to.not.be.null;
    });

    it('should submit query "what is ems" and receive valid response', async function() {
      const testQuery = 'what is ems';
      
      // Navigate to chat
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      await page.waitForSelector('[data-testid="chat-input"]', { timeout: 10000 });
      
      // Submit query
      await page.type('[data-testid="chat-input"]', testQuery);
      await page.click('[data-testid="send-button"]');
      
      // Wait for response with timeout
      const responseSelector = '[data-testid="chat-response"]:last-child';
      await page.waitForSelector(responseSelector, { timeout: TEST_CONFIG.timeout });
      
      // Validate response
      const responseText = await page.$eval(responseSelector, el => el.textContent);
      expect(responseText).to.be.a('string');
      expect(responseText.length).to.be.greaterThan(0);
      
      // Check for error indicators
      const errorElements = await page.$$('[data-testid="error-message"]');
      expect(errorElements).to.have.length(0);
    });

    it('should handle streaming responses correctly', async function() {
      const testQuery = 'what is ems';
      
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      await page.waitForSelector('[data-testid="chat-input"]', { timeout: 10000 });
      
      // Submit query
      await page.type('[data-testid="chat-input"]', testQuery);
      await page.click('[data-testid="send-button"]');
      
      // Monitor for streaming indicators
      const streamingSelector = '[data-testid="streaming-indicator"]';
      const hasStreaming = await page.waitForSelector(streamingSelector, { timeout: 5000 }).catch(() => false);
      
      if (hasStreaming) {
        // Wait for streaming to complete
        await page.waitForSelector(streamingSelector, { hidden: true, timeout: TEST_CONFIG.timeout });
      }
      
      // Verify final response
      const responseText = await page.$eval('[data-testid="chat-response"]:last-child', el => el.textContent);
      expect(responseText).to.be.a('string');
      expect(responseText.length).to.be.greaterThan(0);
    });

    it('should have zero console errors during chat interaction', async function() {
      const consoleErrors = [];
      
      // Collect console errors
      page.on('console', (msg) => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      // Perform chat interaction
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      await page.waitForSelector('[data-testid="chat-input"]', { timeout: 10000 });
      
      await page.type('[data-testid="chat-input"]', 'what is ems');
      await page.click('[data-testid="send-button"]');
      
      // Wait for response
      await page.waitForSelector('[data-testid="chat-response"]:last-child', { timeout: 30000 });
      
      // Verify no console errors
      expect(consoleErrors).to.have.length(0);
    });

    it('should handle API errors gracefully', async function() {
      // Mock API failure by intercepting requests
      await page.setRequestInterception(true);
      
      page.on('request', (request) => {
        if (request.url().includes('/api/chat')) {
          request.respond({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Internal Server Error' })
          });
        } else {
          request.continue();
        }
      });
      
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      await page.waitForSelector('[data-testid="chat-input"]', { timeout: 10000 });
      
      await page.type('[data-testid="chat-input"]', 'what is ems');
      await page.click('[data-testid="send-button"]');
      
      // Wait for error handling
      const errorSelector = '[data-testid="error-message"]';
      await page.waitForSelector(errorSelector, { timeout: 10000 });
      
      const errorText = await page.$eval(errorSelector, el => el.textContent);
      expect(errorText).to.include('error');
    });

    it('should maintain conversation history', async function() {
      const queries = ['what is ems', 'tell me more'];
      
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      await page.waitForSelector('[data-testid="chat-input"]', { timeout: 10000 });
      
      for (const query of queries) {
        await page.type('[data-testid="chat-input"]', query);
        await page.click('[data-testid="send-button"]');
        await page.waitForSelector('[data-testid="chat-response"]:last-child', { timeout: 30000 });
      }
      
      // Verify conversation history
      const messages = await page.$$eval('[data-testid="chat-message"]', msgs => 
        msgs.map(msg => msg.textContent)
      );
      
      expect(messages).to.have.length.at.least(queries.length * 2); // queries + responses
    });

    it('should complete within 180 second timeout', async function() {
      const startTime = Date.now();
      
      await page.goto(`${TEST_CONFIG.baseUrl}/chat`);
      await page.waitForSelector('[data-testid="chat-input"]', { timeout: 10000 });
      
      await page.type('[data-testid="chat-input"]', 'what is ems');
      await page.click('[data-testid="send-button"]');
      
      await page.waitForSelector('[data-testid="chat-response"]:last-child', { timeout: TEST_CONFIG.timeout });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).to.be.lessThan(TEST_CONFIG.timeout);
    });
  });
});

// CLI execution
if (require.main === module) {
  const Mocha = require('mocha');
  const mocha = new Mocha();
  
  mocha.addFile(__filename);
  mocha.run((failures) => {
    process.exit(failures ? 1 : 0);
  });
}