// ============================================
// PostgreSQL Database Configuration
// ============================================

const { Pool } = require('pg');
const config = require('../config/database');

// Connection pool configuration
const pool = new Pool({
  host: config.host,
  port: config.port,
  database: config.database,
  user: config.user,
  password: config.password,
  ssl: config.ssl || false,
  max: config.maxConnections || 20,
  idleTimeoutMillis: config.idleTimeoutMillis || 30000,
  connectionTimeoutMillis: config.connectionTimeoutMillis || 2000,
});

// Health check function
const checkDatabaseHealth = async () => {
  try {
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
};

// Transaction wrapper
const withTransaction = async (callback) => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// Query builder with error handling
const query = async (text, params) => {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query', { text, duration, rows: result.rowCount });
    return result;
  } catch (error) {
    console.error('Database query error:', { text, error: error.message });
    throw error;
  }
};

module.exports = {
  pool,
  query,
  withTransaction,
  checkDatabaseHealth,
};