const { Pool } = require('pg');
const config = require('./database/config.js');

async function testChatOperations() {
  const pool = new Pool(config);
  
  try {
    console.log('🧪 Starting chat operations test...\n');
    
    // Test 1: Create a conversation
    const createResult = await pool.query(
      'INSERT INTO chat_sessions (user_id, title, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING *',
      ['test-user-123', 'Test Conversation']
    );
    console.log('✅ Created conversation:', createResult.rows[0]);
    
    // Test 2: Add a message
    const messageResult = await pool.query(
      'INSERT INTO messages (session_id, role, content, created_at) VALUES ($1, $2, $3, NOW()) RETURNING *',
      [createResult.rows[0].id, 'user', 'Hello, this is a test message']
    );
    console.log('✅ Added message:', messageResult.rows[0]);
    
    // Test 3: Add an assistant response
    const assistantResult = await pool.query(
      'INSERT INTO messages (session_id, role, content, created_at) VALUES ($1, $2, $3, NOW()) RETURNING *',
      [createResult.rows[0].id, 'assistant', 'Hello! This is a test response from the assistant.']
    );
    console.log('✅ Added assistant response:', assistantResult.rows[0]);
    
    // Test 4: Get conversation with messages
    const getResult = await pool.query(`
      SELECT cs.*, 
             COALESCE(
               json_agg(
                 json_build_object(
                   'id', m.id,
                   'role', m.role,
                   'content', m.content,
                   'created_at', m.created_at
                 ) ORDER BY m.created_at
               ) FILTER (WHERE m.id IS NOT NULL), 
               '[]'
             ) as messages
      FROM chat_sessions cs
      LEFT JOIN messages m ON cs.id = m.session_id
      WHERE cs.id = $1
      GROUP BY cs.id
    `, [createResult.rows[0].id]);
    
    console.log('✅ Retrieved conversation with messages:', JSON.stringify(getResult.rows[0], null, 2));
    
    // Test 5: Update conversation
    const updateResult = await pool.query(
      'UPDATE chat_sessions SET title = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
      ['Updated Test Conversation', createResult.rows[0].id]
    );
    console.log('✅ Updated conversation:', updateResult.rows[0]);
    
    // Test 6: Get all conversations for user
    const allConversations = await pool.query(`
      SELECT cs.*, 
             COUNT(m.id) as message_count,
             MAX(m.created_at) as last_message_at
      FROM chat_sessions cs
      LEFT JOIN messages m ON cs.id = m.session_id
      WHERE cs.user_id = $1
      GROUP BY cs.id
      ORDER BY cs.updated_at DESC
    `, ['test-user-123']);
    
    console.log('✅ Retrieved all conversations for user:', allConversations.rows);
    
    // Test 7: Delete conversation (cleanup)
    await pool.query('DELETE FROM messages WHERE session_id = $1', [createResult.rows[0].id]);
    await pool.query('DELETE FROM chat_sessions WHERE id = $1', [createResult.rows[0].id]);
    console.log('✅ Cleaned up test data');
    
    console.log('\n🎉 All CRUD operations successful!');
    
  } catch (error) {
    console.error('❌ Error testing chat operations:', error);
  } finally {
    await pool.end();
  }
}

testChatOperations();