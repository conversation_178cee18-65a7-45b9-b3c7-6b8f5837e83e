#!/usr/bin/env node

/**
 * Migration script to update existing conversation titles from timestamp-based 
 * to smart titles generated from the first user message
 */

const { Pool } = require('pg');
const { generateTitleFromMessage, isValidForTitle } = require('../utils/titleGenerator');

// PostgreSQL connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'kg_qa_db',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

/**
 * Main migration function
 */
async function migrateConversationTitles() {
  console.log('🚀 Starting conversation title migration...');
  
  try {
    // Find conversations with timestamp-based titles
    const findConversationsQuery = `
      SELECT cs.id, cs.title, cs.created_at,
             m.content as first_user_message
      FROM chat_sessions cs
      LEFT JOIN LATERAL (
        SELECT content 
        FROM messages 
        WHERE session_id = cs.id AND role = 'user' 
        ORDER BY created_at ASC 
        LIMIT 1
      ) m ON true
      WHERE cs.title LIKE 'Conversation from %'
         OR cs.title = 'New Conversation'
      ORDER BY cs.created_at DESC;
    `;
    
    const result = await pool.query(findConversationsQuery);
    const conversations = result.rows;
    
    console.log(`📊 Found ${conversations.length} conversations to migrate`);
    
    if (conversations.length === 0) {
      console.log('✅ No conversations need migration');
      return;
    }
    
    let updated = 0;
    let skipped = 0;
    
    for (const conversation of conversations) {
      const { id, title: oldTitle, first_user_message } = conversation;
      
      console.log(`\n🔍 Processing conversation ${id}:`);
      console.log(`   Old title: "${oldTitle}"`);
      console.log(`   First message: "${first_user_message?.substring(0, 100)}..."`);
      
      if (!first_user_message) {
        console.log(`   ⚠️  Skipping - no user messages found`);
        skipped++;
        continue;
      }
      
      if (!isValidForTitle(first_user_message)) {
        console.log(`   ⚠️  Skipping - message not suitable for title generation`);
        skipped++;
        continue;
      }
      
      const newTitle = generateTitleFromMessage(first_user_message);
      
      if (newTitle === oldTitle) {
        console.log(`   ✅ Title unchanged - already optimal`);
        skipped++;
        continue;
      }
      
      // Update the conversation title
      const updateQuery = `
        UPDATE chat_sessions 
        SET title = $1, updated_at = NOW() 
        WHERE id = $2
        RETURNING title;
      `;
      
      const updateResult = await pool.query(updateQuery, [newTitle, id]);
      
      if (updateResult.rows.length > 0) {
        console.log(`   ✅ Updated to: "${newTitle}"`);
        updated++;
      } else {
        console.log(`   ❌ Failed to update conversation ${id}`);
      }
    }
    
    console.log('\n📈 Migration Summary:');
    console.log(`   Total conversations processed: ${conversations.length}`);
    console.log(`   Successfully updated: ${updated}`);
    console.log(`   Skipped: ${skipped}`);
    console.log('✅ Migration completed successfully');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

/**
 * Test function to preview what the migration would do without making changes
 */
async function previewMigration() {
  console.log('👀 Previewing conversation title migration (no changes will be made)...');
  
  try {
    const findConversationsQuery = `
      SELECT cs.id, cs.title, cs.created_at,
             m.content as first_user_message
      FROM chat_sessions cs
      LEFT JOIN LATERAL (
        SELECT content 
        FROM messages 
        WHERE session_id = cs.id AND role = 'user' 
        ORDER BY created_at ASC 
        LIMIT 1
      ) m ON true
      WHERE cs.title LIKE 'Conversation from %'
         OR cs.title = 'New Conversation'
      ORDER BY cs.created_at DESC
      LIMIT 10;
    `;
    
    const result = await pool.query(findConversationsQuery);
    const conversations = result.rows;
    
    console.log(`\n📊 Preview: ${conversations.length} conversations would be processed`);
    
    for (const conversation of conversations) {
      const { id, title: oldTitle, first_user_message } = conversation;
      
      console.log(`\n🔍 Conversation ${id}:`);
      console.log(`   Current: "${oldTitle}"`);
      
      if (first_user_message && isValidForTitle(first_user_message)) {
        const newTitle = generateTitleFromMessage(first_user_message);
        console.log(`   Would become: "${newTitle}"`);
      } else {
        console.log(`   Would be skipped (no suitable message)`);
      }
    }
    
  } catch (error) {
    console.error('❌ Preview failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// CLI handling
if (require.main === module) {
  const args = process.argv.slice(2);
  const isPreview = args.includes('--preview') || args.includes('-p');
  
  if (isPreview) {
    previewMigration().catch(process.exit);
  } else {
    console.log('💡 Tip: Run with --preview to see what would be changed without making any updates');
    migrateConversationTitles().catch(process.exit);
  }
}

module.exports = {
  migrateConversationTitles,
  previewMigration
};