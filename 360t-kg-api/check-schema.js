const { Pool } = require('pg');
const config = require('./config/database.js');

async function checkSchema() {
  const pool = new Pool(config);
  try {
    console.log('🔍 Checking actual database schema...');
    
    // Check if users table exists
    const usersTable = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    if (usersTable.rows.length === 0) {
      console.log('❌ users table does not exist');
    } else {
      console.log('✅ users table columns:');
      usersTable.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
      });
    }
    
    // Check if chat_sessions table exists
    const sessionsTable = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'chat_sessions' 
      ORDER BY ordinal_position
    `);
    
    if (sessionsTable.rows.length === 0) {
      console.log('❌ chat_sessions table does not exist');
    } else {
      console.log('✅ chat_sessions table columns:');
      sessionsTable.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
      });
    }
    
    // Check if messages table exists
    const messagesTable = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'messages' 
      ORDER BY ordinal_position
    `);
    
    if (messagesTable.rows.length === 0) {
      console.log('❌ messages table does not exist');
    } else {
      console.log('✅ messages table columns:');
      messagesTable.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  } finally {
    await pool.end();
  }
}

checkSchema();