// ============================================
// Redis Configuration
// ============================================

const Redis = require('ioredis');
const config = require('../config/redis');

// Redis connection configuration
const redisConfig = {
  host: config.host,
  port: config.port,
  password: config.password,
  db: config.database || 0,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxLoadingTimeout: 3000,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnClusterDown: 100,
  enableOfflineQueue: true,
  readOnly: false,
  stringNumbers: false,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxLoadingTimeout: 3000,
};

// Create Redis client
const redis = new Redis(redisConfig);

// Redis pub/sub for real-time updates
const pubClient = new Redis(redisConfig);
const subClient = new Redis(redisConfig);

// Cache key prefixes
const CACHE_PREFIXES = {
  SESSION: 'session:',
  USER: 'user:',
  CHAT: 'chat:',
  GRAPH: 'graph:',
  SETTINGS: 'settings:',
  API: 'api:',
};

// Cache TTL constants (in seconds)
const CACHE_TTL = {
  SESSION: 3600, // 1 hour
  USER: 1800, // 30 minutes
  CHAT: 300, // 5 minutes
  GRAPH: 600, // 10 minutes
  SETTINGS: 86400, // 24 hours
  API: 60, // 1 minute
};

// Health check function
const checkRedisHealth = async () => {
  try {
    await redis.ping();
    return true;
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
};

// Cache wrapper functions
const cache = {
  async get(key) {
    try {
      const value = await redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  },

  async set(key, value, ttl = CACHE_TTL.SESSION) {
    try {
      await redis.setex(key, ttl, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Redis set error:', error);
      return false;
    }
  },

  async del(key) {
    try {
      await redis.del(key);
      return true;
    } catch (error) {
      console.error('Redis del error:', error);
      return false;
    }
  },

  async exists(key) {
    try {
      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis exists error:', error);
      return false;
    }
  },

  async expire(key, ttl) {
    try {
      await redis.expire(key, ttl);
      return true;
    } catch (error) {
      console.error('Redis expire error:', error);
      return false;
    }
  },

  async incr(key) {
    try {
      return await redis.incr(key);
    } catch (error) {
      console.error('Redis incr error:', error);
      return null;
    }
  },

  async decr(key) {
    try {
      return await redis.decr(key);
    } catch (error) {
      console.error('Redis decr error:', error);
      return null;
    }
  },

  async hget(key, field) {
    try {
      const value = await redis.hget(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Redis hget error:', error);
      return null;
    }
  },

  async hset(key, field, value) {
    try {
      await redis.hset(key, field, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Redis hset error:', error);
      return false;
    }
  },

  async hdel(key, field) {
    try {
      await redis.hdel(key, field);
      return true;
    } catch (error) {
      console.error('Redis hdel error:', error);
      return false;
    }
  },

  async lpush(key, value) {
    try {
      await redis.lpush(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Redis lpush error:', error);
      return false;
    }
  },

  async lrange(key, start, stop) {
    try {
      const values = await redis.lrange(key, start, stop);
      return values.map(v => JSON.parse(v));
    } catch (error) {
      console.error('Redis lrange error:', error);
      return [];
    }
  },

  async sadd(key, member) {
    try {
      await redis.sadd(key, JSON.stringify(member));
      return true;
    } catch (error) {
      console.error('Redis sadd error:', error);
      return false;
    }
  },

  async smembers(key) {
    try {
      const members = await redis.smembers(key);
      return members.map(m => JSON.parse(m));
    } catch (error) {
      console.error('Redis smembers error:', error);
      return [];
    }
  },
};

// Pub/Sub functions
const pubSub = {
  async publish(channel, message) {
    try {
      await pubClient.publish(channel, JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Redis publish error:', error);
      return false;
    }
  },

  async subscribe(channel, callback) {
    try {
      subClient.subscribe(channel);
      subClient.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          callback(JSON.parse(message));
        }
      });
      return true;
    } catch (error) {
      console.error('Redis subscribe error:', error);
      return false;
    }
  },

  async unsubscribe(channel) {
    try {
      await subClient.unsubscribe(channel);
      return true;
    } catch (error) {
      console.error('Redis unsubscribe error:', error);
      return false;
    }
  },
};

// Rate limiting
const rateLimiter = {
  async checkLimit(key, limit, window) {
    try {
      const current = await redis.incr(key);
      if (current === 1) {
        await redis.expire(key, window);
      }
      return current <= limit;
    } catch (error) {
      console.error('Rate limiter error:', error);
      return false;
    }
  },

  async getRemaining(key, limit) {
    try {
      const current = await redis.get(key);
      return Math.max(0, limit - (parseInt(current) || 0));
    } catch (error) {
      console.error('Rate limiter get remaining error:', error);
      return limit;
    }
  },
};

// Cleanup function
const cleanup = async () => {
  await redis.quit();
  await pubClient.quit();
  await subClient.quit();
};

module.exports = {
  redis,
  pubClient,
  subClient,
  cache,
  pubSub,
  rateLimiter,
  checkRedisHealth,
  cleanup,
  CACHE_PREFIXES,
  CACHE_TTL,
};