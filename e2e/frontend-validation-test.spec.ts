import { test, expect } from '@playwright/test';

test.describe('Frontend Validation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5177');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('should load the application without errors', async ({ page }) => {
    // Check that the page title is correct
    await expect(page).toHaveTitle('360T Knowledge Graph');
    
    // Check that the main navigation elements are present
    await expect(page.getByRole('button', { name: 'Chat' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Explorer' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Analysis' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Documentation' })).toBeVisible();
    
    // Check that the logo and heading are present
    await expect(page.getByRole('img', { name: '360T Logo' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Knowledge Graph' })).toBeVisible();
  });

  test('should load graph data successfully', async ({ page }) => {
    // Wait for graph data to load by checking console logs
    const graphDataLoaded = page.waitForEvent('console', msg => 
      msg.text().includes('✅ Initial graph data fetched')
    );
    
    await graphDataLoaded;
    
    // Navigate to Explorer to see the graph
    await page.getByRole('button', { name: 'Explorer' }).click();
    
    // Check that node categories are displayed
    await expect(page.getByText('Node Categories')).toBeVisible();
    await expect(page.getByText('Relationship types')).toBeVisible();
    
    // Check that we have nodes loaded (should show node count)
    await expect(page.getByText(/\(\d+\)/)).toBeVisible(); // Matches patterns like "(5206)"
    
    // Check that search functionality is available
    await expect(page.getByPlaceholder('Search nodes...')).toBeVisible();
  });

  test('should not have JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    
    // Collect JavaScript errors
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Wait for the page to fully load
    await page.waitForTimeout(5000);
    
    // Navigate to different views to test for errors
    await page.getByRole('button', { name: 'Explorer' }).click();
    await page.waitForTimeout(1000);
    
    await page.getByRole('button', { name: 'Chat' }).click();
    await page.waitForTimeout(1000);
    
    // Check that no JavaScript errors occurred
    expect(errors).toHaveLength(0);
  });

  test('should have working API connectivity', async ({ page }) => {
    // Monitor network requests
    const apiRequests: string[] = [];
    
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        apiRequests.push(request.url());
      }
    });
    
    // Wait for initial load
    await page.waitForTimeout(3000);
    
    // Check that API requests were made
    expect(apiRequests.length).toBeGreaterThan(0);
    
    // Check that graph API was called
    const graphApiCalled = apiRequests.some(url => url.includes('/api/graph/'));
    expect(graphApiCalled).toBe(true);
  });

  test('should load within performance requirements', async ({ page }) => {
    const startTime = Date.now();
    
    // Wait for the graph data to load
    await page.waitForEvent('console', msg => 
      msg.text().includes('✅ Initial graph data fetched'), 
      { timeout: 5000 }
    );
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
  });

  test('should handle navigation between views', async ({ page }) => {
    // Test navigation to Explorer
    await page.getByRole('button', { name: 'Explorer' }).click();
    await expect(page.getByText('Node Categories')).toBeVisible();
    
    // Test navigation back to Chat
    await page.getByRole('button', { name: 'Chat' }).click();
    await expect(page.getByRole('button', { name: 'Chat' })).toHaveAttribute('class', /active/);
    
    // Test navigation to Analysis
    await page.getByRole('button', { name: 'Analysis' }).click();
    await expect(page.getByRole('button', { name: 'Analysis' })).toHaveAttribute('class', /active/);
  });

  test('should have all services running', async ({ page }) => {
    // Test that all required services are accessible
    const response1 = await page.request.get('http://localhost:8000/health');
    expect(response1.ok()).toBe(true);
    
    const response2 = await page.request.get('http://localhost:3003/health');
    expect(response2.ok()).toBe(true);
    
    const response3 = await page.request.get('http://localhost:3002/api/graph/minimal');
    expect(response3.ok()).toBe(true);
  });
});
