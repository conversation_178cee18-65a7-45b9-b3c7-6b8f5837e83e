# Knowledge Graph Visualizer - Updated Startup Process

## Root Cause Analysis Summary

The frontend was failing to load graph data due to **504 Gateway Timeout errors**. Our investigation revealed that the **Node.js API service (port 3002)** was missing from the startup sequence, causing the proxy to timeout when trying to route graph-related requests.

### The Problem
- Frontend expected graph endpoints: `/api/graph/minimal`, `/api/graph/visualization`, `/api/graph/initial`
- These endpoints are served by the Node.js API service (port 3002)
- The Node.js API service was not being started automatically
- Proxy server (port 3003) was timing out trying to forward requests to the missing service

### The Solution
Updated the startup script to include all four required services in the correct dependency order.

## Service Architecture

The Knowledge Graph Visualizer requires **four services** running in a specific order:

```
┌─────────────────────────────────────────────────────────────────┐
│                    SERVICE DEPENDENCY CHAIN                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  1. FastAPI Service (port 8000)                                │
│     └── Provides: Chat functionality, conversation management   │
│                                                                 │
│  2. Node.js API Service (port 3002)                           │
│     └── Provides: Graph endpoints (/api/graph/*)              │
│                                                                 │
│  3. Proxy Server (port 3003)                                  │
│     └── Routes requests between frontend and backends          │
│                                                                 │
│  4. Frontend Service (port 5177)                              │
│     └── Serves the React application                           │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Request Flow

```
Frontend (5177) → Proxy (3003) → Node.js API (3002)  [/api/graph/*, /api/settings]
Frontend (5177) → Proxy (3003) → FastAPI (8000)      [/api/chat, /api/conversations]
```

## Updated Startup Script

### Features
- **Dependency-aware startup**: Services start in the correct order
- **Health checks**: Each service is validated before starting the next
- **Error handling**: Script stops if any service fails to start
- **Process management**: Clean shutdown of all services on Ctrl+C
- **Status reporting**: Clear logging and final status summary

### Usage

```bash
# Start all services
./start-services.sh

# Test that all services are running correctly
./test-startup-services.sh
```

### Service Health Endpoints

| Service | Port | Health Check URL |
|---------|------|------------------|
| FastAPI | 8000 | http://localhost:8000/health |
| Node.js API | 3002 | http://localhost:3002/api/health |
| Proxy | 3003 | http://localhost:3003/health |
| Frontend | 5177 | http://localhost:5177/ |

## Troubleshooting

### If Services Fail to Start

1. **Check port conflicts**:
   ```bash
   lsof -i :8000 -i :3002 -i :3003 -i :5177
   ```

2. **Check service logs**:
   - FastAPI: Check terminal output or `fastapi.log`
   - Node.js API: Check `360t-kg-api/server.log`
   - Proxy: Check `proxy-server/logs/`
   - Frontend: Check terminal output or `360t-kg-ui/vite.log`

3. **Restart individual services**:
   ```bash
   # FastAPI
   python main.py

   # Node.js API
   cd 360t-kg-api && npm start

   # Proxy
   cd proxy-server && npm start

   # Frontend
   cd 360t-kg-ui && npm run dev
   ```

### Common Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| 504 Gateway Timeout | Node.js API service not running | Start Node.js API service first |
| Frontend blank page | Services not started in order | Use updated startup script |
| Graph data not loading | Proxy can't reach Node.js API | Check Node.js API health |
| Chat not working | FastAPI service not running | Start FastAPI service |

## Validation

The `test-startup-services.sh` script validates:
- ✅ All ports are listening
- ✅ All health checks pass
- ✅ API endpoints respond correctly
- ✅ Frontend serves content

## Files Modified

- `start-services.sh` - Updated with all four services and health checks
- `test-startup-services.sh` - New validation script
- `STARTUP_PROCESS_DOCUMENTATION.md` - This documentation

## Next Steps

1. Use the updated startup script for all development
2. Run validation script to confirm services are working
3. Monitor service logs for any issues
4. Consider containerizing services for easier deployment
