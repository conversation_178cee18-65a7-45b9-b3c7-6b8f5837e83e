import { test, expect } from '@playwright/test';

test.describe('Header Fix Validation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:5177');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.modern-header', { timeout: 10000 });
  });

  test('Validate Header Height Fix - Desktop', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    console.log('\n🔍 VALIDATING HEADER HEIGHT FIX');
    console.log('=' .repeat(50));
    
    // Get updated CSS variable value
    const cssHeight = await page.evaluate(() => {
      const value = getComputedStyle(document.documentElement).getPropertyValue('--header-height');
      return value.trim();
    });
    console.log(`✅ Updated CSS Variable --header-height: ${cssHeight}`);
    
    // Measure actual header
    const headerBox = await page.locator('.modern-header').boundingBox();
    if (headerBox) {
      console.log(`✅ Actual Header Height: ${headerBox.height}px`);
      
      // Check if heights match (allowing for small rounding differences)
      const cssPx = parseFloat(cssHeight.replace('px', ''));
      const actualPx = headerBox.height;
      const matches = Math.abs(actualPx - cssPx) < 2;
      
      console.log(`✅ CSS and Actual Heights Match: ${matches ? 'YES' : 'NO'}`);
      
      if (actualPx >= 90 && actualPx <= 100) {
        console.log('✅ Header height is in realistic range (90-100px)');
      } else {
        console.log(`⚠️  Header height ${actualPx}px may need adjustment`);
      }
    }
    
    // Test navigation appearance
    const navBox = await page.locator('.nav-list').boundingBox();
    if (navBox) {
      console.log(`✅ Navigation Height: ${navBox.height}px`);
      
      if (navBox.height <= headerBox.height) {
        console.log('✅ Navigation fits comfortably within header');
      } else {
        console.log('❌ Navigation still overflows header');
      }
    }
  });

  test('Validate Chat View Layout Gap Fix', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Navigate to Chat view
    await page.getByRole('button', { name: /chat/i }).click();
    await page.waitForTimeout(1000);
    
    console.log('\n🔍 VALIDATING CHAT VIEW LAYOUT');
    console.log('=' .repeat(50));
    
    // Measure header
    const headerBox = await page.locator('.modern-header').boundingBox();
    
    // Find chat container
    const chatContainer = page.locator('.chat-container, .chat-view, [class*="chat"]').first();
    const chatContainerBox = await chatContainer.boundingBox();
    
    if (headerBox && chatContainerBox) {
      const headerBottom = headerBox.y + headerBox.height;
      const chatTop = chatContainerBox.y;
      const gap = chatTop - headerBottom;
      
      console.log(`Header Bottom: ${headerBottom}px`);
      console.log(`Chat Container Top: ${chatTop}px`);
      console.log(`Gap: ${gap}px`);
      
      if (gap <= 5) {
        console.log('✅ Chat view gap is acceptable (≤5px)');
      } else if (gap <= 20) {
        console.log('⚠️  Chat view has moderate gap');
      } else {
        console.log('❌ Chat view still has excessive gap');
      }
    }
  });

  test('Validate Explorer View Layout', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Navigate to Explorer view
    await page.getByRole('button', { name: /explorer/i }).click();
    await page.waitForTimeout(1000);
    
    console.log('\n🔍 VALIDATING EXPLORER VIEW LAYOUT');
    console.log('=' .repeat(50));
    
    // Measure header
    const headerBox = await page.locator('.modern-header').boundingBox();
    
    // Measure main content area
    const mainContentBox = await page.locator('main').boundingBox();
    
    if (headerBox && mainContentBox) {
      const headerBottom = headerBox.y + headerBox.height;
      const contentTop = mainContentBox.y;
      const gap = contentTop - headerBottom;
      
      console.log(`Header Bottom: ${headerBottom}px`);
      console.log(`Content Top: ${contentTop}px`);
      console.log(`Gap: ${gap}px`);
      
      if (gap <= 5) {
        console.log('✅ Explorer view gap is acceptable (≤5px)');
      } else {
        console.log('⚠️  Explorer view has noticeable gap');
      }
    }
  });

  test('Generate Fix Summary Report', async ({ page }) => {
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 HEADER FIX VALIDATION SUMMARY');
    console.log('=' .repeat(60));
    
    const cssHeight = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue('--header-height').trim();
    });
    
    const headerBox = await page.locator('.modern-header').boundingBox();
    
    console.log('\n📋 SOLUTION IMPLEMENTED:');
    console.log('• Strategy: Realistic Sizing (Option B)');
    console.log(`• Updated --header-height: ${cssHeight}`);
    console.log(`• Actual Rendered Height: ${headerBox?.height}px`);
    
    console.log('\n✅ EXPECTED IMPROVEMENTS:');
    console.log('• Header no longer artificially compressed');
    console.log('• Navigation has proper space to render');
    console.log('• Visual hierarchy restored');
    console.log('• Accessibility standards maintained');
    console.log('• Layout gaps should be eliminated');
    
    console.log('\n📐 NEXT STEPS:');
    console.log('• Verify all dependent layouts updated');
    console.log('• Test mobile responsiveness');
    console.log('• Cross-browser validation');
  });
});