import { test, expect } from '@playwright/test';

/**
 * Comprehensive Chat Functionality Test Suite
 * 
 * Tests the complete chat system end-to-end including:
 * - Basic message sending and receiving
 * - Database persistence verification
 * - Error handling scenarios
 * - Response format validation
 * - Conversation history handling
 * - Performance requirements
 */

test.describe('Chat Functionality - Comprehensive Tests', () => {
  let baseURL: string;

  test.beforeAll(async () => {
    baseURL = process.env.BASE_URL || 'http://localhost:5177';
  });

  test.beforeEach(async ({ page }) => {
    // Navigate to chat view
    await page.goto(`${baseURL}/?view=chat`);
    
    // Wait for the chat interface to load
    await page.waitForSelector('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]', { 
      timeout: 10000 
    });
  });

  test('should successfully send and receive chat messages', async ({ page }) => {
    console.log('🧪 Testing basic chat message functionality...');

    // Find the chat input field
    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    // Verify chat input is visible and enabled
    await expect(chatInput).toBeVisible();
    await expect(chatInput).toBeEnabled();

    // Type a test message
    const testMessage = 'Hello, this is a comprehensive test message for Playwright validation';
    await chatInput.fill(testMessage);

    // Monitor network requests
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );

    // Send the message (try multiple methods)
    await Promise.race([
      page.keyboard.press('Enter'),
      chatInput.press('Enter'),
      page.locator('[data-testid="send-button"], .send-button, button[type="submit"]').click().catch(() => {})
    ]);

    // Wait for the API response
    const response = await responsePromise;
    console.log(`📡 API Response Status: ${response.status()}`);
    
    // Verify successful API response
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    
    // Validate response structure
    expect(responseData).toHaveProperty('response');
    expect(responseData).toHaveProperty('conversationId');
    expect(responseData).toHaveProperty('updatedHistory');
    
    // Verify response contains assistant message
    expect(responseData.response).toHaveProperty('role', 'assistant');
    expect(responseData.response).toHaveProperty('content');
    expect(responseData.response.content).toBeTruthy();

    // Wait for message to appear in chat UI - try multiple selectors
    await page.waitForTimeout(2000); // Give UI time to update
    
    // Try to find the user message in various formats
    let userMessageFound = false;
    const userMessageSelectors = [
      `text=${testMessage}`,
      `*[data-testid="user-message"]:has-text("${testMessage}")`,
      `.user-message:has-text("${testMessage}")`,
      `.message:has-text("${testMessage}")`,
      `.chat-message:has-text("${testMessage}")`
    ];
    
    for (const selector of userMessageSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0) {
          await expect(element.first()).toBeVisible();
          userMessageFound = true;
          console.log(`✅ Found user message with selector: ${selector}`);
          break;
        }
      } catch (e) {
        // Try next selector
      }
    }

    // If no specific message found, at least verify messages container has content
    if (!userMessageFound) {
      console.log('⚠️  Specific user message not found, checking for general message activity...');
      await page.waitForFunction(
        () => {
          const containers = document.querySelectorAll('.messages, .message-list, .chat-messages, .conversation');
          for (const container of containers) {
            if (container.children.length > 0) return true;
          }
          return false;
        },
        { timeout: 10000 }
      );
    }

    // Wait for assistant response - use more flexible approach
    console.log('🤖 Waiting for assistant response...');
    await page.waitForFunction(
      () => {
        const messageContainers = document.querySelectorAll('.messages, .message-list, .chat-messages, .conversation, [data-testid="messages-container"]');
        for (const container of messageContainers) {
          if (container.children.length >= 2) return true;
        }
        // Also check for any assistant message indicators
        const assistantMessages = document.querySelectorAll('.assistant-message, .bot-message, [data-testid="assistant-message"]');
        return assistantMessages.length > 0;
      },
      { timeout: 60000 } // Longer timeout for AI response
    );

    console.log('✅ Basic chat functionality test passed');
  });

  test('should handle conversation history correctly', async ({ page }) => {
    console.log('🧪 Testing conversation history handling...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    // Send first message
    const firstMessage = 'What is EMS?';
    await chatInput.fill(firstMessage);
    
    const firstResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );
    
    await chatInput.press('Enter');
    await firstResponsePromise;
    
    // Wait for first response to appear
    await page.waitForFunction(
      () => document.querySelectorAll('.message, .chat-message').length >= 2,
      { timeout: 30000 }
    );

    // Send second message with context
    const secondMessage = 'Tell me more about EMS configuration';
    await chatInput.fill(secondMessage);
    
    const secondResponsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );
    
    await chatInput.press('Enter');
    const secondResponse = await secondResponsePromise;
    
    // Verify second response includes conversation history
    expect(secondResponse.status()).toBe(200);
    const secondResponseData = await secondResponse.json();
    
    // Check that conversation history is being maintained
    expect(secondResponseData.updatedHistory).toBeDefined();
    expect(secondResponseData.updatedHistory.length).toBeGreaterThanOrEqual(4); // 2 user + 2 assistant messages

    console.log('✅ Conversation history test passed');
  });

  test('should handle API errors gracefully', async ({ page }) => {
    console.log('🧪 Testing error handling...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    // Intercept API calls and simulate server error
    await page.route('**/api/chat/message', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated server error for testing' })
      });
    });

    // Try to send a message
    await chatInput.fill('This should trigger an error');
    await chatInput.press('Enter');

    // Wait for error message to appear
    await page.waitForSelector('.error-message, .alert-error, [class*="error"]', { 
      timeout: 10000 
    });

    // Verify error message is displayed
    const errorMessage = page.locator('.error-message, .alert-error, [class*="error"]').first();
    await expect(errorMessage).toBeVisible();

    console.log('✅ Error handling test passed');
  });

  test('should validate structured response format', async ({ page }) => {
    console.log('🧪 Testing structured response format...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    await chatInput.fill('Test structured response');
    
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );
    
    await chatInput.press('Enter');
    const response = await responsePromise;
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    
    // Validate structured response format (v2.0)
    expect(responseData.response).toHaveProperty('content');
    
    // Parse the structured content if it's JSON
    let structuredContent;
    try {
      structuredContent = JSON.parse(responseData.response.content);
    } catch (e) {
      // Content might not be JSON, that's ok
      structuredContent = { version: 'unknown' };
    }

    // If it's a structured response, validate v2.0 format
    if (structuredContent.version === '2.0') {
      expect(structuredContent).toHaveProperty('answer');
      expect(structuredContent).toHaveProperty('entities');
      expect(structuredContent).toHaveProperty('metadata');
      expect(structuredContent.entities).toBeInstanceOf(Array);
    }

    console.log('✅ Structured response format test passed');
  });

  test('should meet performance requirements', async ({ page }) => {
    console.log('🧪 Testing performance requirements...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    await chatInput.fill('Performance test message');
    
    const startTime = Date.now();
    
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );
    
    await chatInput.press('Enter');
    const response = await responsePromise;
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log(`📊 Response time: ${responseTime}ms`);
    
    // Verify response time is within acceptable limits (120 seconds for complex queries with AI processing)
    expect(responseTime).toBeLessThan(120000);
    expect(response.status()).toBe(200);

    console.log('✅ Performance requirements test passed');
  });

  test('should validate database persistence', async ({ page, request }) => {
    console.log('🧪 Testing database persistence...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    const testMessage = `Database persistence test ${Date.now()}`;
    await chatInput.fill(testMessage);
    
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );
    
    await chatInput.press('Enter');
    const response = await responsePromise;
    
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    
    // Verify conversation ID is returned
    expect(responseData).toHaveProperty('conversationId');
    const conversationId = responseData.conversationId;
    expect(conversationId).toBeTruthy();

    // Verify conversation was saved to database by fetching it
    const conversationResponse = await request.get(`${baseURL}/api/chat/conversations/${conversationId}`);
    
    if (conversationResponse.status() === 200) {
      const conversationData = await conversationResponse.json();
      expect(conversationData).toHaveProperty('id', conversationId);
      console.log('✅ Database persistence verified');
    } else {
      console.log('⚠️  Database persistence check skipped (conversation API not available)');
    }
  });

  test('should handle empty messages appropriately', async ({ page }) => {
    console.log('🧪 Testing empty message handling...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    // Try to send empty message
    await chatInput.fill('');
    await chatInput.press('Enter');
    
    // Should either prevent sending or show validation error
    await page.waitForTimeout(2000);
    
    // Verify no API call was made or error was shown
    const messages = page.locator('.message, .chat-message');
    const messageCount = await messages.count();
    
    // Should not add empty messages to chat
    expect(messageCount).toBe(0);

    console.log('✅ Empty message handling test passed');
  });

  test('should handle special characters and long messages', async ({ page }) => {
    console.log('🧪 Testing special characters and long messages...');

    const chatInput = await page.locator('[data-testid="chat-input"], .chat-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    
    // Test with special characters
    const specialMessage = 'Test with special chars: @#$%^&*()[]{}|\\:";\'<>?,./';
    await chatInput.fill(specialMessage);
    
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/chat/message') && response.request().method() === 'POST'
    );
    
    await chatInput.press('Enter');
    const response = await responsePromise;
    
    expect(response.status()).toBe(200);
    
    // Verify special characters were processed (API worked)
    // UI verification is challenging due to escaping, focus on API success
    await page.waitForTimeout(2000); // Give UI time to update
    
    // Try to verify the message was processed by checking for any new message content
    await page.waitForFunction(
      () => {
        const containers = document.querySelectorAll('.messages, .message-list, .chat-messages, .conversation');
        for (const container of containers) {
          if (container.children.length > 0) return true;
        }
        return false;
      },
      { timeout: 10000 }
    );

    console.log('✅ Special characters test passed');
  });
});

test.describe('Chat API Integration Tests', () => {
  test('should validate API contract', async ({ request }) => {
    console.log('🧪 Testing API contract validation...');

    const baseURL = process.env.BASE_URL || 'http://localhost:3003';
    
    // Test API endpoint directly
    const response = await request.post(`${baseURL}/api/chat/message`, {
      data: {
        message: 'API contract test',
        history: [],
        graphitiSettings: {}
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    expect(response.status()).toBe(200);
    
    const responseData = await response.json();
    
    // Validate API response structure
    expect(responseData).toHaveProperty('response');
    expect(responseData).toHaveProperty('conversationId');
    expect(responseData).toHaveProperty('updatedHistory');
    
    expect(responseData.response).toHaveProperty('role', 'assistant');
    expect(responseData.response).toHaveProperty('content');
    expect(responseData.updatedHistory).toBeInstanceOf(Array);

    console.log('✅ API contract validation passed');
  });

  test('should handle malformed requests appropriately', async ({ request }) => {
    console.log('🧪 Testing malformed request handling...');

    const baseURL = process.env.BASE_URL || 'http://localhost:3003';
    
    // Test with missing required fields
    const response = await request.post(`${baseURL}/api/chat/message`, {
      data: {
        // Missing 'message' field
        history: [],
        graphitiSettings: {}
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    expect(response.status()).toBe(400);
    
    const errorData = await response.json();
    expect(errorData).toHaveProperty('error');
    expect(errorData.error).toContain('required');

    console.log('✅ Malformed request handling test passed');
  });
});