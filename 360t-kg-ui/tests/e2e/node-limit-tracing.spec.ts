import { test, expect } from '@playwright/test';

test.describe('3D Graph Node Limit Tracing', () => {
  test('should trace the actual node limit values used in 3D mode', async ({ page }) => {
    console.log('🔍 Tracing 3D graph node limit execution path...');
    
    // Navigate to the application
    await page.goto('/');
    await page.waitForSelector('.app', { timeout: 10000 });
    
    // Capture console logs to trace the execution
    const consoleLogs = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('node limit') || 
          text.includes('nodeLimit') || 
          text.includes('maxNodes') ||
          text.includes('Setting node limit') ||
          text.includes('3D graph data transformation') ||
          text.includes('limited from')) {
        consoleLogs.push(text);
      }
    });
    
    // Switch to 3D mode to trigger the node limit logic
    const toggle3DButton = page.locator('button[title*="3D"], button:has-text("3D")');
    if (await toggle3DButton.isVisible()) {
      console.log('📊 Switching to 3D mode...');
      await toggle3DButton.click();
      await page.waitForTimeout(3000); // Allow time for 3D mode to initialize
    } else {
      console.log('⚠️ 3D toggle button not found, checking if already in 3D mode');
    }
    
    // Wait for any graph data to load
    await page.waitForTimeout(5000);
    
    // Inject code to inspect the actual values being used
    const nodeLimit3DValues = await page.evaluate(() => {
      // Try to access settings service
      let settingsServiceValues = {};
      try {
        if (window.settingsService) {
          const settings = window.settingsService.getAll();
          settingsServiceValues = {
            maxNodes3D: settings.performance?.maxNodes3D,
            maxNodes2D: settings.performance?.maxNodes2D,
            maxNodes: settings.performance?.maxNodes
          };
        }
      } catch (e) {
        console.log('Could not access settingsService:', e.message);
      }
      
      // Check for any global config values
      let globalValues = {};
      try {
        // Look for any exposed configuration
        if (window.GRAPH_LIMITS) {
          globalValues.GRAPH_LIMITS = window.GRAPH_LIMITS;
        }
        if (window.customConfig) {
          globalValues.customConfig = window.customConfig;
        }
      } catch (e) {
        console.log('Could not access global config:', e.message);
      }
      
      // Check localStorage for any stored settings
      let localStorageValues = {};
      try {
        const storedSettings = localStorage.getItem('kg-visualizer-settings');
        if (storedSettings) {
          const parsed = JSON.parse(storedSettings);
          localStorageValues = {
            maxNodes3D: parsed.performance?.maxNodes3D,
            maxNodes2D: parsed.performance?.maxNodes2D,
            maxNodes: parsed.performance?.maxNodes
          };
        }
      } catch (e) {
        console.log('Could not access localStorage settings:', e.message);
      }
      
      return {
        settingsService: settingsServiceValues,
        global: globalValues,
        localStorage: localStorageValues,
        timestamp: new Date().toISOString()
      };
    });
    
    console.log('\n📊 Node Limit Values Found:');
    console.log('Settings Service:', nodeLimit3DValues.settingsService);
    console.log('Global Config:', nodeLimit3DValues.global);
    console.log('LocalStorage:', nodeLimit3DValues.localStorage);
    
    console.log('\n📝 Console Logs Related to Node Limits:');
    consoleLogs.forEach((log, index) => {
      console.log(`${index + 1}. ${log}`);
    });
    
    // Try to trigger a graph load with data to see the actual limiting in action
    const searchField = page.locator('input[placeholder*="Search"]');
    if (await searchField.isVisible()) {
      console.log('\n🔍 Triggering graph load to see node limiting...');
      await searchField.fill('EMS');
      await page.waitForTimeout(2000);
      
      // Look for search results and click one
      const searchResults = page.locator('.search-results li').first();
      if (await searchResults.isVisible()) {
        await searchResults.click();
        await page.waitForTimeout(3000);
      }
    }
    
    // Wait a bit more and capture any additional logs
    await page.waitForTimeout(2000);
    
    console.log('\n📝 Final Console Logs:');
    consoleLogs.forEach((log, index) => {
      console.log(`${index + 1}. ${log}`);
    });
    
    // Look for specific patterns in the logs that indicate which value was used
    const limitingLogs = consoleLogs.filter(log => 
      log.includes('limited from') || 
      log.includes('Setting node limit') ||
      log.includes('nodeLimit3D') ||
      log.includes('effectiveLimit')
    );
    
    console.log('\n🎯 Key Node Limiting Logs:');
    limitingLogs.forEach((log, index) => {
      console.log(`${index + 1}. ${log}`);
    });
    
    // Verify we have some information about node limits
    const hasNodeLimitInfo = 
      Object.keys(nodeLimit3DValues.settingsService).length > 0 ||
      Object.keys(nodeLimit3DValues.localStorage).length > 0 ||
      consoleLogs.length > 0;
    
    expect(hasNodeLimitInfo).toBe(true);
    
    console.log('\n✅ Node limit tracing completed');
  });

  test('should inspect the actual customConfig passed to 3D component', async ({ page }) => {
    console.log('🔧 Inspecting customConfig passed to 3D component...');
    
    await page.goto('/');
    await page.waitForSelector('.app', { timeout: 10000 });
    
    // Inject monitoring code before switching to 3D
    await page.evaluate(() => {
      // Monitor React component props
      window.customConfigHistory = [];
      
      // Override console.log to capture 3D Graph Props Debug
      const originalLog = console.log;
      console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('3D Graph Props Debug') || 
            message.includes('customConfigKeys') ||
            message.includes('nodeLimit')) {
          window.customConfigHistory.push({
            timestamp: new Date().toISOString(),
            message: message,
            args: args
          });
        }
        return originalLog.apply(console, args);
      };
    });
    
    // Switch to 3D mode
    const toggle3DButton = page.locator('button[title*="3D"], button:has-text("3D")');
    if (await toggle3DButton.isVisible()) {
      await toggle3DButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Get the captured customConfig information
    const customConfigInfo = await page.evaluate(() => {
      return {
        history: window.customConfigHistory || [],
        currentTimestamp: new Date().toISOString()
      };
    });
    
    console.log('\n🔧 CustomConfig History:');
    customConfigInfo.history.forEach((entry, index) => {
      console.log(`${index + 1}. [${entry.timestamp}] ${entry.message}`);
    });
    
    expect(customConfigInfo.history.length).toBeGreaterThanOrEqual(0);
    
    console.log('\n✅ CustomConfig inspection completed');
  });
});
