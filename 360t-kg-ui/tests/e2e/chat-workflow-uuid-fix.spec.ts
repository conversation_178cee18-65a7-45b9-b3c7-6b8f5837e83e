import { test, expect } from '@playwright/test';

test.describe('Chat Workflow with UUID Conversations', () => {
  test.beforeEach(async ({ page }) => {
    // Clear localStorage to ensure clean state
    await page.goto('http://localhost:5178/?view=chat');
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    await page.reload();
    await page.waitForLoadState('networkidle');
  });

  test('should debug current browser console errors', async ({ page }) => {
    console.log('🧪 Debugging browser console errors and network requests...');
    
    // Monitor console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
        console.log('❌ Console Error:', msg.text());
      }
    });
    
    // Monitor network requests to see what's being sent
    const requests: any[] = [];
    const responses: any[] = [];
    
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        requests.push({
          method: request.method(),
          url: request.url(),
          headers: request.headers(),
          postData: request.postData()
        });
        console.log(`📤 Request: ${request.method()} ${request.url()}`);
        if (request.postData()) {
          console.log(`📤 Request data:`, request.postData());
        }
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        responses.push({
          status: response.status(),
          url: response.url(),
          headers: response.headers()
        });
        console.log(`📥 Response: ${response.status()} ${response.url()}`);
      }
    });

    // Navigate to chat view and wait for page load
    await page.goto('http://localhost:5178/?view=chat');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Try to find any conversation-related elements or create new conversation
    const newConvButton = page.locator('button').filter({ hasText: '+' }).or(
      page.locator('button').filter({ hasText: 'New' })
    ).first();
    
    if (await newConvButton.isVisible({ timeout: 5000 })) {
      console.log('🆕 Found new conversation button, clicking...');
      await newConvButton.click();
      await page.waitForTimeout(2000);
    } else {
      console.log('⚠️ No new conversation button found, continuing with existing state');
    }

    // Try to send a message if there's an input field
    const messageInput = page.locator('input[type="text"]').or(
      page.locator('textarea')
    ).first();
    
    if (await messageInput.isVisible({ timeout: 5000 })) {
      console.log('💬 Found message input, sending test message...');
      await messageInput.fill('What is a UUID test?');
      await messageInput.press('Enter');
      await page.waitForTimeout(5000);
    } else {
      console.log('⚠️ No message input found');
    }

    // Check if conversation was created with UUID
    const conversationRequests = requests.filter(r => 
      r.url.includes('/api/chat/conversations') && r.method === 'POST'
    );
    expect(conversationRequests.length).toBeGreaterThan(0);
    console.log('✅ Conversation creation request found:', conversationRequests[0]);

    // Get the latest conversations response to find the UUID
    const conversationResponse = responses.find(r => 
      r.url.includes('/api/chat/conversations') && r.status === 201
    );
    expect(conversationResponse).toBeDefined();
    console.log('✅ Conversation creation response found:', conversationResponse);

    // Wait for UI to update
    await page.waitForTimeout(2000);

    // Check for message loading requests with UUID
    const messageRequests = requests.filter(r => 
      r.url.includes('/messages') && r.method === 'GET'
    );
    
    if (messageRequests.length > 0) {
      console.log('📨 Message loading requests:', messageRequests);
      
      // Check if any request has invalid ID format (integer instead of UUID)
      const invalidRequests = messageRequests.filter(r => {
        const match = r.url.match(/conversations\/([^\/]+)\/messages/);
        if (match) {
          const id = match[1];
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
          console.log(`🔍 Conversation ID "${id}" is ${isValidUUID ? 'valid UUID' : 'INVALID (not UUID)'}`);
          return !isValidUUID;
        }
        return false;
      });
      
      expect(invalidRequests.length).toBe(0);
      console.log('✅ All message requests use valid UUID conversation IDs');
    }

    // Try to send a chat message
    const messageInput2 = page.locator('[data-testid="message-input"]');
    await messageInput2.fill('Test message to check UUID handling');
    await messageInput2.press('Enter');
    
    // Wait for chat response
    await page.waitForTimeout(3000);
    
    // Check chat requests
    const chatRequests = requests.filter(r => 
      r.url.includes('/api/chat') && r.method === 'POST' && !r.url.includes('conversations')
    );
    
    if (chatRequests.length > 0) {
      console.log('💬 Chat requests:', chatRequests);
      
      // Check if chat request format is correct
      const chatRequest = chatRequests[chatRequests.length - 1];
      if (chatRequest.postData) {
        const payload = JSON.parse(chatRequest.postData);
        expect(payload).toHaveProperty('question');
        console.log('✅ Chat request has correct format with "question" field');
      }
    }
    
    // Check for any 422 or 500 errors
    const errorResponses = responses.filter(r => r.status >= 400);
    if (errorResponses.length > 0) {
      console.log('❌ Error responses found:', errorResponses);
      
      // Log specific error types
      const uuidErrors = errorResponses.filter(r => 
        r.url.includes('/messages') && r.status === 500
      );
      if (uuidErrors.length > 0) {
        console.log('❌ UUID-related message loading errors:', uuidErrors);
      }
      
      const validationErrors = errorResponses.filter(r => 
        r.url.includes('/api/chat') && r.status === 422
      );
      if (validationErrors.length > 0) {
        console.log('❌ Chat validation errors (422):', validationErrors);
      }
    }
    
    // Final assessment
    console.log('📊 Test Summary:');
    console.log(`  - Total API requests: ${requests.length}`);
    console.log(`  - Total API responses: ${responses.length}`);
    console.log(`  - Error responses: ${errorResponses.length}`);
    console.log(`  - Conversation requests: ${conversationRequests.length}`);
    console.log(`  - Message requests: ${messageRequests.length}`);
    console.log(`  - Chat requests: ${chatRequests.length}`);
  });

  test('should handle browser localStorage cleanup correctly', async ({ page }) => {
    console.log('🧪 Testing localStorage cleanup for invalid conversation IDs...');
    
    // Set up invalid conversation data in localStorage
    await page.goto('http://localhost:5178/?view=chat');
    await page.evaluate(() => {
      // Simulate old integer-based conversation IDs
      const invalidChatStorage = {
        state: {
          conversations: [
            { id: '6', title: 'Invalid Integer ID Conversation' },
            { id: '42', title: 'Another Invalid ID' }
          ],
          currentConversation: { id: '6', title: 'Current Invalid' }
        },
        version: 1
      };
      localStorage.setItem('chat-storage', JSON.stringify(invalidChatStorage));
    });
    
    // Reload page to trigger cleanup
    await page.reload();
    await page.waitForTimeout(2000);
    
    // Check if cleanup was triggered
    const cleanupMessage = await page.evaluate(() => {
      // Check console logs for cleanup messages
      return localStorage.getItem('chat-storage');
    });
    
    console.log('🧹 localStorage after cleanup:', cleanupMessage);
    
    // Should either be null (cleared) or contain valid UUIDs only
    if (cleanupMessage) {
      const parsed = JSON.parse(cleanupMessage);
      if (parsed.state?.conversations) {
        for (const conv of parsed.state.conversations) {
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(conv.id);
          expect(isValidUUID).toBe(true);
        }
      }
    }
    
    console.log('✅ localStorage cleanup working correctly');
  });
});