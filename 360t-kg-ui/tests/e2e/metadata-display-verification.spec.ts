import { test, expect } from '@playwright/test';

test.describe('Metadata Display Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:5178');
    
    // Wait for the app to load
    await page.waitForLoadState('networkidle');
  });

  test('should display metadata in chat responses', async ({ page }) => {
    // Type a simple question
    const chatInput = page.locator('textarea[placeholder*="knowledge graph"]');
    await chatInput.fill('What is a trading system?');
    
    // Send the message
    await chatInput.press('Enter');
    
    // Wait for the response to arrive
    await page.waitForSelector('.chat-message.assistant', { timeout: 30000 });
    
    // Look for metadata section
    const metadataSection = page.locator('.response-metadata');
    await expect(metadataSection).toBeVisible({ timeout: 10000 });
    
    // Check for metadata details element
    const metadataDetails = page.locator('.response-metadata details');
    await expect(metadataDetails).toBeVisible();
    
    // Expand metadata details
    await metadataDetails.click();
    
    // Check for basic metadata fields
    const metadataGrid = page.locator('.metadata-grid');
    await expect(metadataGrid).toBeVisible();
    
    // Check for processing time
    const processingTime = page.locator('.metadata-label:has-text("Processing Time:")');
    await expect(processingTime).toBeVisible();
    
    // Check for search type
    const searchType = page.locator('.metadata-label:has-text("Search Type:")');
    await expect(searchType).toBeVisible();
    
    // Check for knowledge graph results
    const kgResults = page.locator('.metadata-label:has-text("Knowledge Graph Results:")');
    await expect(kgResults).toBeVisible();
  });

  test('should display advanced Graphiti search metadata', async ({ page }) => {
    // Type a question that will trigger Graphiti search
    const chatInput = page.locator('textarea[placeholder*="knowledge graph"]');
    await chatInput.fill('Tell me about workflow patterns');
    
    // Send the message
    await chatInput.press('Enter');
    
    // Wait for the response
    await page.waitForSelector('.chat-message.assistant', { timeout: 30000 });
    
    // Look for metadata section and expand it
    const metadataDetails = page.locator('.response-metadata details');
    await expect(metadataDetails).toBeVisible();
    await metadataDetails.click();
    
    // Check for Graphiti search section
    const graphitiSection = page.locator('.metadata-section:has-text("🔍 Graphiti Search")');
    await expect(graphitiSection).toBeVisible();
    
    // Check for algorithm field
    const algorithm = page.locator('.metadata-label:has-text("Algorithm:")');
    await expect(algorithm).toBeVisible();
    
    // Check for diversity factor
    const diversityFactor = page.locator('.metadata-label:has-text("Diversity Factor:")');
    await expect(diversityFactor).toBeVisible();
    
    // Check for requested/returned counts
    const requested = page.locator('.metadata-label:has-text("Requested:")');
    await expect(requested).toBeVisible();
    
    const returned = page.locator('.metadata-label:has-text("Returned:")');
    await expect(returned).toBeVisible();
  });

  test('should display LLM models metadata', async ({ page }) => {
    // Type a question
    const chatInput = page.locator('textarea[placeholder*="knowledge graph"]');
    await chatInput.fill('What are the main components?');
    
    // Send the message
    await chatInput.press('Enter');
    
    // Wait for the response
    await page.waitForSelector('.chat-message.assistant', { timeout: 30000 });
    
    // Look for metadata section and expand it
    const metadataDetails = page.locator('.response-metadata details');
    await expect(metadataDetails).toBeVisible();
    await metadataDetails.click();
    
    // Check for LLM models section
    const llmSection = page.locator('.metadata-section:has-text("🤖 LLM Models")');
    await expect(llmSection).toBeVisible();
    
    // Check for Graphiti Search LLM subsection
    const graphitiLLM = page.locator('.metadata-subsection:has-text("Graphiti Search LLM:")');
    await expect(graphitiLLM).toBeVisible();
    
    // Check for Response Generation LLM subsection
    const responseLLM = page.locator('.metadata-subsection:has-text("Response Generation LLM:")');
    await expect(responseLLM).toBeVisible();
    
    // Check for model field
    const modelField = page.locator('.metadata-label:has-text("Model:")');
    await expect(modelField.first()).toBeVisible();
    
    // Check for purpose field (should be visible for both LLM models)
    const purposeField = page.locator('.metadata-label:has-text("Purpose:")');
    await expect(purposeField.first()).toBeVisible();
    
    // Check for URL field (Graphiti Search LLM)
    const urlField = page.locator('.metadata-label:has-text("URL:")');
    if (await urlField.count() > 0) {
      await expect(urlField.first()).toBeVisible();
    }
    
    // Check for Database field if present
    const databaseField = page.locator('.metadata-label:has-text("Database:")');
    if (await databaseField.count() > 0) {
      await expect(databaseField.first()).toBeVisible();
    }
    
    // Check for Query Enhancement field if present
    const queryEnhancementField = page.locator('.metadata-label:has-text("Query Enhancement:")');
    if (await queryEnhancementField.count() > 0) {
      await expect(queryEnhancementField.first()).toBeVisible();
    }
  });

  test('should show accurate metadata values', async ({ page }) => {
    // Type a question
    const chatInput = page.locator('textarea[placeholder*="knowledge graph"]');
    await chatInput.fill('Test query for metadata verification');
    
    // Send the message
    await chatInput.press('Enter');
    
    // Wait for the response
    await page.waitForSelector('.chat-message.assistant', { timeout: 30000 });
    
    // Look for metadata section and expand it
    const metadataDetails = page.locator('.response-metadata details');
    await expect(metadataDetails).toBeVisible();
    await metadataDetails.click();
    
    // Check that processing time is reasonable (should be > 0 and < 30000ms)
    const processingTimeValue = page.locator('.metadata-label:has-text("Processing Time:") + .metadata-value');
    await expect(processingTimeValue).toBeVisible();
    
    // Check that search type is not "unknown"
    const searchTypeValue = page.locator('.metadata-label:has-text("Search Type:") + .metadata-value');
    await expect(searchTypeValue).toBeVisible();
    await expect(searchTypeValue).not.toContainText('unknown');
    
    // Check that knowledge graph results shows some data
    const kgResultsValue = page.locator('.metadata-label:has-text("Knowledge Graph Results:") + .metadata-value');
    await expect(kgResultsValue).toBeVisible();
    await expect(kgResultsValue).toContainText('sources');
    await expect(kgResultsValue).toContainText('entities');
  });

  test('should maintain metadata display across multiple queries', async ({ page }) => {
    // First query
    const chatInput = page.locator('textarea[placeholder*="knowledge graph"]');
    await chatInput.fill('First test query');
    await chatInput.press('Enter');
    
    // Wait for first response
    await page.waitForSelector('.chat-message.assistant', { timeout: 30000 });
    
    // Check first metadata
    const firstMetadata = page.locator('.response-metadata').first();
    await expect(firstMetadata).toBeVisible();
    
    // Second query
    await chatInput.fill('Second test query');
    await chatInput.press('Enter');
    
    // Wait for second response
    await page.waitForSelector('.chat-message.assistant', { timeout: 30000 });
    
    // Check that both metadata sections are visible
    const allMetadata = page.locator('.response-metadata');
    await expect(allMetadata).toHaveCount(2);
    
    // Both should be visible
    await expect(allMetadata.first()).toBeVisible();
    await expect(allMetadata.last()).toBeVisible();
  });
});