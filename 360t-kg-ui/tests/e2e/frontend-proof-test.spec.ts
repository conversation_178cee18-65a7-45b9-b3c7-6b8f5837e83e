import { test, expect } from '@playwright/test';

test.describe('Frontend Proof Test with Screenshots', () => {
  test('should capture screenshot showing the frontend is running', async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5177');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Take full page screenshot
    await page.screenshot({ 
      path: '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/frontend-running-proof.png',
      fullPage: true 
    });
    
    // Verify the page title or main content
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    // Check if we can see the main app content
    const bodyText = await page.locator('body').textContent();
    expect(bodyText).toBeTruthy();
    
    console.log('✅ Screenshot saved to frontend-running-proof.png');
  });
  
  test('should show graph visualization is loaded', async ({ page }) => {
    await page.goto('http://localhost:5177');
    
    // Wait for graph to potentially load
    await page.waitForTimeout(8000);
    
    // Take screenshot of the graph area
    await page.screenshot({ 
      path: '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/graph-visualization-proof.png',
      fullPage: true 
    });
    
    // Check for graph elements
    const hasGraphElements = await page.evaluate(() => {
      const svg = document.querySelector('svg');
      const canvas = document.querySelector('canvas');
      const graphContainer = document.querySelector('[data-testid="graph-container"], .graph-container, #graph-view');
      
      return {
        hasSvg: !!svg,
        hasCanvas: !!canvas,
        hasGraphContainer: !!graphContainer,
        svgNodeCount: svg ? svg.querySelectorAll('circle, rect, path').length : 0,
        bodyContent: document.body.innerText.substring(0, 200)
      };
    });
    
    console.log('Graph elements found:', JSON.stringify(hasGraphElements, null, 2));
    console.log('✅ Graph visualization screenshot saved to graph-visualization-proof.png');
  });
  
  test('should test Neo4j data is accessible via API', async ({ page }) => {
    await page.goto('http://localhost:5177');
    
    // Try to access the API endpoint directly
    const apiResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/graph/nodes?limit=5');
        const data = await response.json();
        return {
          status: response.status,
          hasNodes: data.nodes && data.nodes.length > 0,
          nodeCount: data.nodes ? data.nodes.length : 0,
          firstNodeSample: data.nodes && data.nodes[0] ? JSON.stringify(data.nodes[0]).substring(0, 100) : null
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('API Response:', JSON.stringify(apiResponse, null, 2));
    
    // Take screenshot showing the network activity or console
    await page.screenshot({ 
      path: '/Users/<USER>/Documents/Trainings/KnowledgeGraphVisualizer/api-test-proof.png',
      fullPage: true 
    });
    
    console.log('✅ API test screenshot saved to api-test-proof.png');
  });
});