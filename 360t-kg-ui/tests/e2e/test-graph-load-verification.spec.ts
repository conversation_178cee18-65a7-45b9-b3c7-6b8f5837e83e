import { test, expect } from '@playwright/test';

test.describe('Graph Load Verification', () => {
  test('should load graph nodes and display correctly', async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5177');
    
    // Wait for the graph container to be visible
    await expect(page.locator('[data-testid="graph-container"], .graph-container, #graph-view')).toBeVisible({ timeout: 30000 });
    
    // Wait for graph data to load (look for SVG or canvas elements)
    const graphElement = page.locator('svg, canvas').first();
    await expect(graphElement).toBeVisible({ timeout: 30000 });
    
    // Check for nodes (SVG circles or similar graph elements)
    await page.waitForFunction(() => {
      const svg = document.querySelector('svg');
      const canvas = document.querySelector('canvas');
      
      if (svg) {
        const circles = svg.querySelectorAll('circle');
        return circles.length > 0;
      }
      
      if (canvas) {
        const ctx = canvas.getContext('2d');
        return ctx !== null;
      }
      
      return false;
    }, { timeout: 30000 });
    
    console.log('✅ Graph visualization loaded successfully');
  });
  
  test('should display entity categories correctly', async ({ page }) => {
    await page.goto('http://localhost:5177');
    
    // Wait for the graph to load
    await page.waitForTimeout(5000);
    
    // Look for legend or category indicators
    const legendElement = page.locator('.legend, [data-testid="legend"], .node-legend').first();
    
    try {
      await expect(legendElement).toBeVisible({ timeout: 10000 });
      console.log('✅ Legend/category display found');
    } catch (e) {
      console.log('ℹ️ Legend not immediately visible, checking for nodes');
      
      // Alternative: check if nodes are rendered (even without visible legend)
      const hasNodes = await page.evaluate(() => {
        const svg = document.querySelector('svg');
        if (svg) {
          const circles = svg.querySelectorAll('circle');
          return circles.length > 0;
        }
        return false;
      });
      
      expect(hasNodes).toBe(true);
      console.log('✅ Graph nodes are rendered');
    }
  });
  
  test('should verify node count is reasonable', async ({ page }) => {
    await page.goto('http://localhost:5177');
    
    // Wait for graph to load
    await page.waitForTimeout(8000);
    
    // Count visible nodes
    const nodeCount = await page.evaluate(() => {
      const svg = document.querySelector('svg');
      if (svg) {
        const circles = svg.querySelectorAll('circle');
        return circles.length;
      }
      
      // For canvas-based 3D graphs, we can't count directly
      // but we can check if the canvas has content
      const canvas = document.querySelector('canvas');
      if (canvas) {
        const ctx = canvas.getContext('2d');
        return ctx ? 1 : 0; // Assume nodes exist if canvas context is valid
      }
      
      return 0;
    });
    
    console.log(`Found ${nodeCount} nodes in the graph`);
    expect(nodeCount).toBeGreaterThan(0);
    
    // For our cleaned dataset, we expect a reasonable number of visible nodes
    // (not all 17,485 would be visible at once due to pagination/limits)
    if (nodeCount > 1) {
      console.log('✅ Graph is displaying multiple nodes correctly');
    }
  });
  
  test('should handle graph interactions', async ({ page }) => {
    await page.goto('http://localhost:5177');
    
    // Wait for graph to load
    await page.waitForTimeout(5000);
    
    // Try to interact with the graph area
    const graphArea = page.locator('svg, canvas').first();
    await expect(graphArea).toBeVisible();
    
    // Click on the graph area (this should not cause errors)
    await graphArea.click();
    
    // Wait a bit to see if any errors occur
    await page.waitForTimeout(2000);
    
    // Check console for any JavaScript errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });
    
    // Reload to catch any console errors
    await page.reload();
    await page.waitForTimeout(3000);
    
    console.log('✅ Graph interactions completed without major errors');
  });
});