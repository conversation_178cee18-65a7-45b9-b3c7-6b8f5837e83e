import { test, expect } from '@playwright/test';

test.describe('Chat UUID Debug', () => {
  test('debug browser console errors and network requests', async ({ page }) => {
    console.log('🧪 Debugging browser console errors and network requests...');
    
    // Monitor console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
        console.log('❌ Console Error:', msg.text());
      }
    });
    
    // Monitor network requests
    const requests: any[] = [];
    const responses: any[] = [];
    
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        requests.push({
          method: request.method(),
          url: request.url(),
          postData: request.postData()
        });
        console.log(`📤 Request: ${request.method()} ${request.url()}`);
        if (request.postData()) {
          console.log(`📤 Data:`, request.postData());
        }
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        responses.push({
          status: response.status(),
          url: response.url()
        });
        console.log(`📥 Response: ${response.status()} ${response.url()}`);
      }
    });

    // Navigate to chat and wait
    await page.goto('http://localhost:5178/?view=chat');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    // Look for error responses that match the browser console logs
    const errorResponses = responses.filter(r => r.status >= 400);
    console.log('🚨 Error responses found:', errorResponses.length);
    
    errorResponses.forEach(resp => {
      console.log(`❌ ${resp.status} ${resp.url}`);
    });

    // Look for specific UUID errors in message loading
    const messageErrors = errorResponses.filter(r => 
      r.url.includes('/messages') && r.status === 500
    );
    
    if (messageErrors.length > 0) {
      console.log('❌ UUID message loading errors:', messageErrors.length);
      messageErrors.forEach(err => {
        console.log(`   - ${err.url}`);
        // Extract conversation ID from URL
        const match = err.url.match(/conversations\/([^\/]+)\/messages/);
        if (match) {
          const id = match[1];
          const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
          console.log(`   - ID "${id}" is ${isValidUUID ? 'valid UUID' : 'INVALID (integer)'}`)
        }
      });
    }

    // Look for 422 chat validation errors  
    const chatErrors = errorResponses.filter(r => 
      r.url.includes('/api/chat') && r.status === 422
    );
    
    if (chatErrors.length > 0) {
      console.log('❌ Chat validation errors (422):', chatErrors.length);
    }

    // Check console errors
    console.log('📋 Console errors found:', consoleErrors.length);
    consoleErrors.forEach(err => {
      if (err.includes('getMessages')) {
        console.log('   - getMessages error:', err);
      }
      if (err.includes('422') || err.includes('500')) {
        console.log('   - HTTP error:', err);
      }
    });
    
    // Final summary
    console.log('📊 Debug Summary:');
    console.log(`  - API requests: ${requests.length}`);
    console.log(`  - Error responses: ${errorResponses.length}`);
    console.log(`  - Console errors: ${consoleErrors.length}`);
    console.log(`  - Message loading errors: ${messageErrors.length}`);
    console.log(`  - Chat validation errors: ${chatErrors.length}`);
  });
});