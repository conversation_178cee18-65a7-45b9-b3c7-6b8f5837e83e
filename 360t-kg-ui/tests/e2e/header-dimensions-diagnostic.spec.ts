import { test, expect } from '@playwright/test';

test.describe('Header Dimensions Diagnostic', () => {
  let measurements = {
    desktop: {},
    tablet: {},
    mobile: {}
  };

  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5177');
    
    // Wait for the React app to load by waiting for multiple elements
    await page.waitForLoadState('networkidle');
    
    // Try different selectors to see what's actually on the page
    try {
      await page.waitForSelector('.modern-header', { timeout: 5000 });
    } catch (e) {
      // If modern-header is not found, log what's actually on the page
      const bodyContent = await page.locator('body').innerHTML();
      console.log('Page body content:', bodyContent.substring(0, 500));
      
      // Try alternative selectors
      const headerSelectors = ['header', '.header', '[class*="header"]', 'nav'];
      for (const selector of headerSelectors) {
        const exists = await page.locator(selector).count() > 0;
        console.log(`Selector "${selector}" found: ${exists}`);
      }
      
      throw new Error('Could not find header element. Check console output for page content.');
    }
  });

  test('Phase 1: Measure Header Dimensions vs CSS Constraints - Desktop', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    console.log('\n🔍 DESKTOP MEASUREMENTS (1920x1080)');
    console.log('=' .repeat(50));
    
    // Get CSS variable value
    const cssHeight = await page.evaluate(() => {
      const value = getComputedStyle(document.documentElement).getPropertyValue('--header-height');
      return value.trim();
    });
    console.log(`CSS Variable --header-height: ${cssHeight}`);
    
    // Measure actual header
    const headerBox = await page.locator('.modern-header').boundingBox();
    if (headerBox) {
      console.log(`Actual Header Height: ${headerBox.height}px`);
      console.log(`Actual Header Width: ${headerBox.width}px`);
      measurements.desktop.header = headerBox;
      measurements.desktop.cssHeight = cssHeight;
    }
    
    // Measure logo container
    const logoBox = await page.locator('.brand-logo').boundingBox();
    if (logoBox) {
      console.log(`Logo Height: ${logoBox.height}px`);
      console.log(`Logo Width: ${logoBox.width}px`);
      measurements.desktop.logo = logoBox;
    }
    
    // Measure navigation container
    const navBox = await page.locator('.nav-list').boundingBox();
    if (navBox) {
      console.log(`Navigation Height: ${navBox.height}px`);
      console.log(`Navigation Width: ${navBox.width}px`);
      measurements.desktop.navigation = navBox;
    }
    
    // Measure header main container (with padding)
    const headerMainBox = await page.locator('.header-main').boundingBox();
    if (headerMainBox) {
      console.log(`Header Main Container Height: ${headerMainBox.height}px`);
      measurements.desktop.headerMain = headerMainBox;
    }
    
    // Get computed styles for padding analysis
    const headerStyles = await page.evaluate(() => {
      const header = document.querySelector('.header-main');
      if (header) {
        const computed = getComputedStyle(header);
        return {
          paddingTop: computed.paddingTop,
          paddingBottom: computed.paddingBottom,
          paddingLeft: computed.paddingLeft,
          paddingRight: computed.paddingRight,
          gap: computed.gap
        };
      }
      return null;
    });
    
    if (headerStyles) {
      console.log(`Header Padding: ${headerStyles.paddingTop} ${headerStyles.paddingRight} ${headerStyles.paddingBottom} ${headerStyles.paddingLeft}`);
      console.log(`Header Gap: ${headerStyles.gap}`);
      measurements.desktop.padding = headerStyles;
    }
    
    // Calculate minimum required height
    const logoPx = parseFloat(logoBox?.height || 0);
    const navPx = parseFloat(navBox?.height || 0);
    const paddingTopPx = parseFloat(headerStyles?.paddingTop || 0);
    const paddingBottomPx = parseFloat(headerStyles?.paddingBottom || 0);
    
    const minRequiredHeight = Math.max(logoPx, navPx) + paddingTopPx + paddingBottomPx;
    console.log(`Calculated Minimum Required Height: ${minRequiredHeight}px`);
    
    // Analysis
    const cssPx = parseFloat(cssHeight.replace('px', ''));
    const actualPx = headerBox?.height || 0;
    const discrepency = Math.abs(actualPx - cssPx);
    
    console.log(`\n📊 ANALYSIS:`);
    console.log(`CSS Constraint: ${cssPx}px`);
    console.log(`Actual Rendered: ${actualPx}px`);
    console.log(`Discrepancy: ${discrepency}px`);
    console.log(`Fits in 56px constraint: ${minRequiredHeight <= 56 ? '✅ YES' : '❌ NO'}`);
    
    measurements.desktop.analysis = {
      cssHeight: cssPx,
      actualHeight: actualPx,
      minRequired: minRequiredHeight,
      discrepancy: discrepency,
      fitsIn56px: minRequiredHeight <= 56
    };
  });

  test('Phase 1: Test Layout Gaps in Explorer View', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Navigate to Explorer view
    await page.getByRole('button', { name: /explorer/i }).click();
    await page.waitForTimeout(1000);
    
    console.log('\n🔍 EXPLORER VIEW LAYOUT ANALYSIS');
    console.log('=' .repeat(50));
    
    // Measure header
    const headerBox = await page.locator('.modern-header').boundingBox();
    
    // Measure main content area
    const mainContentBox = await page.locator('main').boundingBox();
    
    if (headerBox && mainContentBox) {
      const headerBottom = headerBox.y + headerBox.height;
      const contentTop = mainContentBox.y;
      const gap = contentTop - headerBottom;
      
      console.log(`Header Bottom: ${headerBottom}px`);
      console.log(`Content Top: ${contentTop}px`);
      console.log(`Gap Between Header and Content: ${gap}px`);
      
      measurements.desktop.explorerGap = gap;
      
      // Check if there's unwanted space
      if (gap > 5) {
        console.log(`⚠️  WARNING: Excessive gap detected in Explorer view`);
      } else {
        console.log(`✅ Explorer view layout looks good`);
      }
    }
  });

  test('Phase 1: Test Layout Gaps in Chat View', async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Navigate to Chat view
    await page.getByRole('button', { name: /chat/i }).click();
    await page.waitForTimeout(1000);
    
    console.log('\n🔍 CHAT VIEW LAYOUT ANALYSIS');
    console.log('=' .repeat(50));
    
    // Measure header
    const headerBox = await page.locator('.modern-header').boundingBox();
    
    // Try to find chat container
    const chatContainer = page.locator('.chat-container, .chat-view, [class*="chat"]').first();
    const chatContainerBox = await chatContainer.boundingBox();
    
    if (headerBox && chatContainerBox) {
      const headerBottom = headerBox.y + headerBox.height;
      const chatTop = chatContainerBox.y;
      const gap = chatTop - headerBottom;
      
      console.log(`Header Bottom: ${headerBottom}px`);
      console.log(`Chat Container Top: ${chatTop}px`);
      console.log(`Gap Between Header and Chat: ${gap}px`);
      
      measurements.desktop.chatGap = gap;
      
      // Check for the massive gap issue from screenshot
      if (gap > 20) {
        console.log(`❌ CRITICAL: Massive gap detected in Chat view (matches screenshot issue)`);
      } else if (gap > 5) {
        console.log(`⚠️  WARNING: Noticeable gap detected in Chat view`);
      } else {
        console.log(`✅ Chat view layout looks good`);
      }
    }
  });

  test('Phase 1: Mobile Responsive Analysis', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    console.log('\n🔍 MOBILE MEASUREMENTS (375x667)');
    console.log('=' .repeat(50));
    
    // Wait for responsive changes
    await page.waitForTimeout(500);
    
    // Measure header on mobile
    const headerBox = await page.locator('.modern-header').boundingBox();
    if (headerBox) {
      console.log(`Mobile Header Height: ${headerBox.height}px`);
      measurements.mobile.header = headerBox;
    }
    
    // Check if mobile menu toggle is visible
    const mobileToggle = await page.locator('.mobile-menu-toggle').isVisible();
    console.log(`Mobile Menu Toggle Visible: ${mobileToggle ? '✅ YES' : '❌ NO'}`);
    
    // Test mobile menu if toggle is visible
    if (mobileToggle) {
      await page.locator('.mobile-menu-toggle').click();
      await page.waitForTimeout(300);
      
      const mobileNav = await page.locator('.header-nav.nav-open').isVisible();
      console.log(`Mobile Navigation Opens: ${mobileNav ? '✅ YES' : '❌ NO'}`);
      
      if (mobileNav) {
        const mobileNavBox = await page.locator('.header-nav.nav-open').boundingBox();
        if (mobileNavBox) {
          console.log(`Mobile Navigation Height: ${mobileNavBox.height}px`);
          
          // Check positioning relative to header
          const headerBottom = headerBox.y + headerBox.height;
          const navTop = mobileNavBox.y;
          const mobileNavGap = navTop - headerBottom;
          
          console.log(`Mobile Nav Gap from Header: ${mobileNavGap}px`);
          measurements.mobile.navGap = mobileNavGap;
        }
      }
    }
  });

  test('Phase 2: Generate Comprehensive Report', async ({ page }) => {
    console.log('\n' + '=' .repeat(80));
    console.log('🎯 COMPREHENSIVE HEADER DIAGNOSTIC REPORT');
    console.log('=' .repeat(80));
    
    // Summary of findings
    const analysis = measurements.desktop.analysis;
    if (analysis) {
      console.log('\n📋 EXECUTIVE SUMMARY:');
      console.log(`• CSS Height Constraint: ${analysis.cssHeight}px`);
      console.log(`• Actual Rendered Height: ${analysis.actualHeight}px`);
      console.log(`• Minimum Required Height: ${analysis.minRequired}px`);
      console.log(`• Current Solution Viable: ${analysis.fitsIn56px ? '✅ YES' : '❌ NO'}`);
      
      if (measurements.desktop.explorerGap !== undefined) {
        console.log(`• Explorer View Gap: ${measurements.desktop.explorerGap}px ${measurements.desktop.explorerGap > 5 ? '⚠️' : '✅'}`);
      }
      
      if (measurements.desktop.chatGap !== undefined) {
        console.log(`• Chat View Gap: ${measurements.desktop.chatGap}px ${measurements.desktop.chatGap > 20 ? '❌' : measurements.desktop.chatGap > 5 ? '⚠️' : '✅'}`);
      }
      
      console.log('\n🎯 RECOMMENDED SOLUTION:');
      if (analysis.fitsIn56px) {
        console.log('✅ OPTION A: Ultra-Flat Redesign');
        console.log('   - Current content can fit in 56px constraint');
        console.log('   - Optimize spacing and padding to eliminate gaps');
        console.log('   - Maintain accessibility standards');
      } else {
        console.log('✅ OPTION B: Realistic Sizing');
        console.log(`   - Update --header-height to ${Math.ceil(analysis.minRequired)}px`);
        console.log('   - Update all dependent layout calculations');
        console.log('   - Maintain visual hierarchy and accessibility');
      }
      
      console.log('\n📐 DETAILED MEASUREMENTS CAPTURED ✅');
      console.log('Ready for Phase 3: Implementation');
    }
  });
});