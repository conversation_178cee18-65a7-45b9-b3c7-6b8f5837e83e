<!DOCTYPE html>
<html>
<head>
    <title>Clear Chat Cache</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .success { color: green; margin: 10px 0; font-weight: bold; }
        .info { color: #666; margin: 10px 0; }
        .warning { color: orange; margin: 10px 0; font-weight: bold; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>360T Chat Cache Cleaner</h1>
        <p class="info">This utility clears cached conversation data that may contain invalid integer IDs from previous sessions.</p>
        
        <div id="status"></div>
        
        <button class="button" onclick="clearChatCache()">Clear Chat Cache</button>
        <button class="button" onclick="showCacheInfo()">Show Cache Info</button>
        <button class="button" onclick="goToChat()">Go to Chat</button>
        
        <div id="output"></div>
    </div>

    <script>
        function clearChatCache() {
            const output = document.getElementById('output');
            const status = document.getElementById('status');
            
            try {
                // Clear all chat-related localStorage
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('chat') || key.includes('conversation'))) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // Clear sessionStorage too
                sessionStorage.clear();
                
                status.innerHTML = `<div class="success">✅ Cache cleared successfully!</div>`;
                output.innerHTML = `<pre>Removed ${keysToRemove.length} cache keys:
${keysToRemove.map(k => '  - ' + k).join('\\n')}</pre>`;
                
                console.log('Chat cache cleared:', keysToRemove);
                
            } catch (error) {
                status.innerHTML = `<div class="warning">❌ Error clearing cache: ${error.message}</div>`;
                console.error('Error clearing cache:', error);
            }
        }
        
        function showCacheInfo() {
            const output = document.getElementById('output');
            
            const chatKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('chat') || key.includes('conversation'))) {
                    chatKeys.push(key);
                }
            }
            
            let info = `Found ${chatKeys.length} chat-related cache keys:\\n`;
            chatKeys.forEach(key => {
                const value = localStorage.getItem(key);
                info += `\\n${key}:\\n`;
                try {
                    const parsed = JSON.parse(value);
                    if (parsed.state && parsed.state.conversations) {
                        info += `  Conversations: ${parsed.state.conversations.length}\\n`;
                        parsed.state.conversations.forEach(conv => {
                            const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(conv.id);
                            info += `    - ID: ${conv.id} (${isValidUUID ? 'Valid UUID' : 'INVALID - Integer'})\\n`;
                        });
                    }
                } catch (e) {
                    info += `  (Raw data, length: ${value.length})\\n`;
                }
            });
            
            output.innerHTML = `<pre>${info}</pre>`;
        }
        
        function goToChat() {
            window.location.href = '/?view=chat';
        }
        
        // Auto-show cache info on load
        window.onload = showCacheInfo;
    </script>
</body>
</html>