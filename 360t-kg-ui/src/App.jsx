import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import * as d3 from 'd3';
import debounce from 'lodash.debounce';
import './App.css';
import './styles/animations.css';
import Header from './components/Header';
import { GraphSkeleton, DataProcessingSkeleton, PhaseLoadingIndicator } from './components/LoadingSkeletons';

import GraphViewWrapper from './components/GraphViewWrapper';
import NodeDetailsModern from './components/NodeDetailsModern';
import RelationshipDetails from './components/RelationshipDetails';
import Legend from './components/Legend';
import SearchField from './components/SearchField';
import ChatView from './components/ChatView';
import DocumentationLayout from './components/DocumentationLayout';
import { useDocumentation } from './components/useDocumentation.js';
import { useSearch } from './components/useSearch.js';
import {
  getRelationships,
  getInitialGraph,
  runImpactAnalysis,
  runDependencyAnalysis,
  findPaths,
  runCentralityAnalysis,
  searchNodes
} from './services/api';
import settingsService from './services/settingsService';
import { useSettings } from './hooks/useSettings';
import AdvancedAnalysisPanel from './components/AdvancedAnalysisPanel';

function App() {
  // Removed debug logging - infinite re-rendering issue resolved
  const [graphData, setGraphData] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedRelationship, setSelectedRelationship] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isDetailsCollapsed, setIsDetailsCollapsed] = useState(false);

  // Enhanced loading state management for performance optimization
  const [loadingPhase, setLoadingPhase] = useState('idle'); // idle, fetching, transforming, rendering, complete
  const [loadingProgress, setLoadingProgress] = useState(0);

  // 3D Force graph ref for force control
  const forceGraphRef = useRef();
  const [currentView, setCurrentView] = useState('explorer');
  const [analysisType, setAnalysisType] = useState(null);
  const [targetNode, setTargetNode] = useState(null);
  const [centralityType, setCentralityType] = useState('degree');
  const [is3DMode, setIs3DMode] = useState(false);

  // 3D control states
  const [nodeSize3D, setNodeSize3D] = useState(2);

  // Relationship filtering state for detailed view
  const [isDetailView, setIsDetailView] = useState(false);
  const [relationshipTypeFilter, setRelationshipTypeFilter] = useState(null);

  const [expandedDoc, setExpandedDoc] = useState(null);
  const { docContent, docLoading, docError } = useDocumentation(expandedDoc);
  const [graphContainerRef, setGraphContainerRef] = useState(null);
  const [svgRef, setSvgRef] = useState(null);

  // Search-related state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [nodeLimit, setNodeLimit] = useState(0); // Will be initialized based on mode

  // Use settings service instead of local state
  const { settings, isReady: settingsReady } = useSettings();
  
  // Convert settings to the format expected by components
  const nodeConfig = useMemo(() => {
    if (!settingsReady || !settings) {
      return { colors: {}, sizes: {}, shapes: {}, relationshipColors: {}, relationshipLineStyles: {}, nodeTypeVisibility: {}, nodeLimit: 0 };
    }

    return {
      colors: settings.nodeColors || {},
      sizes: settings.nodeSizes || {},
      shapes: settings.nodeShapes || {},
      relationshipColors: settings.relationshipColors || {},
      relationshipLineStyles: settings.relationshipLineStyles || {},
      nodeTypeVisibility: settings.nodeTypeVisibility || {},
      nodeLimit: nodeLimit // Pass the current nodeLimit to the graph
    };
  }, [settings, settingsReady, nodeLimit]);

  const docMapping = {
    'getting-started': 'getting-started',
    'user-guide': 'user-guide',
    'data-model': 'data-model',
    'api-reference': 'api-reference',
    'analytics-guide': 'analytics-guide',
    'query-guide': 'query-guide',
    'visualization': 'visualization',
    'development': 'development',
    'administration': 'administration',
    'troubleshooting': 'troubleshooting',
    'monitoring-guide': 'monitoring-guide',
    'validation-guide': 'validation-guide'
  };

  // History management functions
  const updateURL = useCallback((state) => {
    const url = new URL(window.location);
    
    // Update search params based on state
    if (state.view) {
      url.searchParams.set('view', state.view);
    }
    if (state.nodeId) {
      url.searchParams.set('nodeId', state.nodeId);
    } else {
      url.searchParams.delete('nodeId');
    }
    if (state.showDetails !== undefined) {
      if (state.showDetails) {
        url.searchParams.set('details', 'true');
      } else {
        url.searchParams.delete('details');
      }
    }
    if (state.expandedDoc) {
      url.searchParams.set('doc', state.expandedDoc);
    } else {
      url.searchParams.delete('doc');
    }
    if (state.is3DMode !== undefined) {
      if (state.is3DMode) {
        url.searchParams.set('mode', '3d');
      } else {
        url.searchParams.delete('mode');
      }
    }

    // Use replaceState for initial load, pushState for subsequent navigation
    // This prevents the "trivial session history context" warning
    const isInitialLoad = window.history.length <= 1;
    if (isInitialLoad) {
      window.history.replaceState(state, '', url.toString());
    } else {
      window.history.pushState(state, '', url.toString());
    }
  }, []);

  const restoreFromURL = useCallback(async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const view = urlParams.get('view') || 'explorer';
    const rawNodeId = urlParams.get('nodeId');
    // Properly decode nodeId to handle names with spaces and special characters
    const nodeId = rawNodeId ? decodeURIComponent(rawNodeId) : null;
    const showDetails = urlParams.get('details') === 'true';
    const expandedDoc = urlParams.get('doc');
    const is3DMode = urlParams.get('mode') === '3d';

    // Restore view state
    setCurrentView(view);
    setIs3DMode(is3DMode);
    
    if (expandedDoc) {
      setExpandedDoc(expandedDoc);
    }

    // Restore node selection if present
    if (nodeId && showDetails) {
      setLoading(true);
      try {
        // We need to find the node data first
        // This could be from search results or we might need to make an API call
        const relationships = await getRelationships(nodeId);
        if (relationships && relationships.nodes) {
          const node = relationships.nodes.find(n => n.id == nodeId);
          if (node) {
            setSelectedNode(node);
            setShowDetails(true);
            
            // Convert edges to links for D3
            if (relationships.edges) {
              const graphData = {
                nodes: relationships.nodes,
                links: relationships.edges.map(edge => ({
                  ...edge,
                  source: edge.from,
                  target: edge.to,
                  type: edge.label
                }))
              };
              setGraphData(graphData);
            }
          }
        }
      } catch (err) {
        console.error('Error restoring node from URL:', err);
        // Clear invalid URL params
        const url = new URL(window.location);
        url.searchParams.delete('nodeId');
        url.searchParams.delete('details');
        window.history.replaceState({}, '', url.toString());
      } finally {
        setLoading(false);
      }
    } else if (view === 'explorer') {
      // Load initial graph when in explorer view without specific node
      const startTime = performance.now();
      console.log('🔄 Starting initial graph data fetch...');
      setLoading(true);
      try {
        const initialGraphData = await getInitialGraph(nodeLimit);
        const fetchTime = performance.now() - startTime;
        console.log(`✅ Initial graph data fetched in ${fetchTime.toFixed(2)}ms: ${initialGraphData?.nodes?.length || 0} nodes, ${initialGraphData?.links?.length || 0} links`);
        setGraphData(initialGraphData);
      } catch (err) {
        console.error('Error loading initial graph:', err);
        setError('Failed to load initial graph. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  }, []);

  // Handle browser back/forward buttons
  useEffect(() => {
    const handlePopState = (event) => {
      // Restore state from URL parameters
      restoreFromURL();
    };

    // Listen for popstate events (back/forward button)
    window.addEventListener('popstate', handlePopState);
    
    // Initial load - check URL parameters
    restoreFromURL();

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [restoreFromURL]);

  // Set initial URL on first load - default to chat view
  useEffect(() => {
    if (window.location.search === '') {
      updateURL({ view: 'chat' });
    }
  }, [updateURL]);

  // Initialize settings service on startup
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        await settingsService.initialize();
      } catch (err) {
        console.error('Error initializing settings:', err);
      }
    };

    initializeSettings();
  }, []);

  // Initialize and update nodeLimit based on 2D/3D mode
  useEffect(() => {
    if (settingsReady && settings) {
      const defaultLimit2D = settings.performance?.maxNodes2D || 2000;
      const defaultLimit3D = settings.performance?.maxNodes3D || 1000;
      const newLimit = is3DMode ? defaultLimit3D : defaultLimit2D;

      // Only update if the current limit is 0 (uninitialized) or if switching modes
      if (nodeLimit === 0 || (is3DMode && nodeLimit !== defaultLimit3D) || (!is3DMode && nodeLimit !== defaultLimit2D)) {
        console.log(`Setting node limit for ${is3DMode ? '3D' : '2D'} mode: ${newLimit}`);
        setNodeLimit(newLimit);
      }
    }
  }, [settingsReady, settings, is3DMode, nodeLimit]);

  // Enhanced nodeLimit setter that persists to settings store
  const setNodeLimitWithPersistence = (newLimit) => {
    const limitValue = parseInt(newLimit) || 0;
    setNodeLimit(limitValue);
    
    // Persist to settings store based on current mode
    if (settingsReady) {
      const key = is3DMode ? 'performance.maxNodes3D' : 'performance.maxNodes2D';
      settingsService.set(key, limitValue);
      console.log(`Persisted node limit for ${is3DMode ? '3D' : '2D'} mode: ${limitValue}`);
    }
  };

  // Auto-close detail panels when view changes
  useEffect(() => {
    // Close any open detail panels when switching views
    if (showDetails && (selectedNode || selectedRelationship)) {
      console.log('🔄 Auto-closing detail panels due to view change:', currentView);
      setShowDetails(false);
      setSelectedNode(null);
      setSelectedRelationship(null);
    }
  }, [currentView]); // Only depend on currentView to trigger when view changes

  // Event handlers for custom events
  useEffect(() => {
    // Cleanup function to remove all tooltips when switching views
    const cleanupTooltips = () => {
      // Remove all D3 tooltips
      d3.select("body").selectAll(".document-tooltip").remove();
      // Remove React tooltips
      document.querySelectorAll('.custom-tooltip, .node-chip-tooltip').forEach(el => el.remove());
    };

    const handleLoadInitialGraph = async () => {
      cleanupTooltips(); // Clean up before switching
      
      // If we're already in explorer view with graph data, don't reload
      // unless we're currently showing search results or node details
      if (currentView === 'explorer' && graphData && !showDetails && (!hookResults || hookResults.length === 0)) {
        return;
      }
      
      // Clear search results when loading initial graph
      if (clearSearch) {
        clearSearch();
      }
      
      const startTime = performance.now();
      console.log('🔄 Starting handleLoadInitialGraph with phase tracking...');

      // Phase 1: Initialize loading
      setLoading(true);
      setLoadingPhase('fetching');
      setLoadingProgress(0);
      setError(null);

      try {
        // Phase 2: Fetch data
        setLoadingProgress(20);
        const fetchStartTime = performance.now();
        const initialGraphData = await getInitialGraph(nodeLimit);
        const fetchTime = performance.now() - fetchStartTime;

        // Phase 3: Transform data (simulated progress)
        setLoadingPhase('transforming');
        setLoadingProgress(60);

        // Small delay to show transformation phase
        await new Promise(resolve => setTimeout(resolve, 100));

        // Phase 4: Prepare for rendering
        setLoadingPhase('rendering');
        setLoadingProgress(90);

        const totalTime = performance.now() - startTime;
        console.log(`✅ handleLoadInitialGraph completed in ${totalTime.toFixed(2)}ms (fetch: ${fetchTime.toFixed(2)}ms): ${initialGraphData?.nodes?.length || 0} nodes, ${initialGraphData?.links?.length || 0} links`);

        setGraphData(initialGraphData);

        // Phase 5: Complete
        setLoadingProgress(100);
        setLoadingPhase('complete');

        // Reset to idle after a brief moment
        setTimeout(() => {
          setLoadingPhase('idle');
          setLoadingProgress(0);
        }, 500);

      } catch (err) {
        console.error('Error loading initial graph:', err);
        setError('Failed to load initial graph. Please try again.');
        setLoadingPhase('idle');
        setLoadingProgress(0);
      } finally {
        setLoading(false);
      }
    };

    const handleShowAnalysis = () => {
      cleanupTooltips(); // Clean up before switching
      setCurrentView('analysis');
      setShowDetails(false);
      clearSearch();
      updateURL({ view: 'analysis' });
    };

    const handleShowDocumentation = () => {
      cleanupTooltips(); // Clean up before switching
      setCurrentView('documentation');
      setShowDetails(false);
      clearSearch();
      updateURL({ view: 'documentation' });
    };

    const handleShowChat = () => {
      cleanupTooltips(); // Clean up before switching
      setCurrentView('chat');
      setShowDetails(false);
      clearSearch();
      updateURL({ view: 'chat' });
    };

    // Add error handler for WebSocket connection issues
    const handleWebSocketError = (event) => {
      console.warn('WebSocket connection error:', event);
      // We don't need to do anything here since our API service 
      // now handles retries and fallbacks automatically
    };

    window.addEventListener('loadInitialGraph', handleLoadInitialGraph);
    window.addEventListener('showAnalysis', handleShowAnalysis);
    window.addEventListener('showDocumentation', handleShowDocumentation);
    window.addEventListener('showChat', handleShowChat);
    window.addEventListener('error', (e) => {
      if (e.message && (
          e.message.includes('WebSocket') || 
          e.message.includes('ws://') || 
          e.message.includes('ERR_CONNECTION_REFUSED')
        )) {
        handleWebSocketError(e);
      }
    });
    
    return () => {
      window.removeEventListener('loadInitialGraph', handleLoadInitialGraph);
      window.removeEventListener('showAnalysis', handleShowAnalysis);
      window.removeEventListener('showDocumentation', handleShowDocumentation);
      window.removeEventListener('showChat', handleShowChat);
      window.removeEventListener('error', (e) => {
        if (e.message && (
            e.message.includes('WebSocket') || 
            e.message.includes('ws://') || 
            e.message.includes('ERR_CONNECTION_REFUSED')
          )) {
          handleWebSocketError(e);
        }
      });
      
      // Final cleanup when component unmounts
      cleanupTooltips();
    };
  }, [updateURL]);

  // Create a memoized version of the graph data with deep comparison to prevent unnecessary re-renders
  const memoizedGraphData = useMemo(() => {
    if (!graphData) return null;

    // Create a stable reference by stringifying and parsing the data
    // This ensures that objects with the same content have the same reference
    const stableData = JSON.parse(JSON.stringify(graphData));

    // Debug logging removed - infinite re-rendering issue resolved

    return stableData;
  }, [JSON.stringify(graphData)]); // Use JSON.stringify for deep comparison
  
  const { 
    searchResults: hookResults, 
    handleSearchResults, 
    clearSearch = () => {} // Provide safe default
  } = useSearch(
    setGraphData,
    setSelectedNode,
    setShowDetails,
    setLoading,
    setError,
    setAnalysisResults
  ) || { searchResults: [], handleSearchResults: () => {}, clearSearch: () => {} };

  // Use hookResults directly instead of syncing to avoid infinite loops
  // The search results from the hook are already managed properly

  // Handle node selection - memoized to avoid re-creating on every render
  const handleNodeSelect = useCallback(async (node) => {
    console.log('🔍 App handleNodeSelect - DEBUGGING NODE SELECTION IN APP:');
    console.log('1. Node received from ChatView:', node);
    console.log('2. Node ID:', node?.id);
    console.log('3. Node name:', node?.name);
    console.log('4. Node source:', node?.source);
    console.log('5. Node type:', typeof node);
    console.log('6. Node keys:', node ? Object.keys(node) : 'no node');
    
    if (!node) {
      console.error('❌ App handleNodeSelect: No node provided');
      return;
    }

    // Clear search results when selecting a node from search results
    clearSearch();
    let nodeToSelect = node;

    console.log('7. Setting selectedNode and showDetails to true');
    console.log('8. Selected node to set:', nodeToSelect);
    setSelectedNode(nodeToSelect);
    setShowDetails(true);

    // Set detail view mode and filter to show only RELATES_TO relationships
    setIsDetailView(true);
    setRelationshipTypeFilter(['RELATES_TO']);

    // Automatically switch to 3D mode when viewing node details (user preference)
    setIs3DMode(true);

    // Update URL to reflect selected node and 3D mode
    updateURL({
      view: currentView,
      nodeId: node.id,
      showDetails: true,
      is3DMode: true
    });
    
    try {
      const relationships = await getRelationships(node.id);
      // Convert edges to links for D3
      if (relationships && relationships.edges) {
        const graphData = {
          nodes: relationships.nodes,
          links: relationships.edges.map(edge => ({
            ...edge,
            source: edge.from,
            target: edge.to,
            type: edge.label
          }))
        };
        setGraphData(graphData);
      }
    } catch (err) {
      console.error('Error fetching relationships:', err);
      setError('Failed to load relationships. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [updateURL, currentView]);

  // Handle impact analysis results - memoized
  const handleAnalysisResults = useCallback((results) => {
    if (results && results.nodes) {
      setAnalysisResults(results);
      
      // Convert data format for D3
      const graphData = {
        nodes: results.nodes,
        links: results.links || []
      };
      setGraphData(graphData);
    }
  }, []);

  // Handle relationship selection - memoized
  const handleRelationshipSelect = useCallback((relationship) => {
    setSelectedRelationship(relationship);
    setSelectedNode(null); // Clear node selection when selecting relationship
    setShowDetails(true);

    // Update URL to reflect selected relationship
    updateURL({
      view: currentView,
      showDetails: true
    });
  }, [currentView, updateURL]);

  // Close the details panel - memoized
  const handleCloseDetails = useCallback(() => {
    setShowDetails(false);
    setSelectedNode(null);
    setSelectedRelationship(null);

    // Reset detail view state and relationship filtering
    setIsDetailView(false);
    setRelationshipTypeFilter(null);

    // Update URL to remove node selection
    updateURL({ view: currentView, showDetails: false });
    
    // Check if we're in Explorer view (not Analysis)
    if (currentView === 'explorer') {
      // Set loading state
      setLoading(true);
      
      // Directly load the initial graph data
      getInitialGraph(nodeLimit)
        .then(initialGraphData => {
          // Explicitly update graph data with full graph
          setGraphData(initialGraphData);
          setLoading(false);
        })
        .catch(err => {
          console.error('Error reloading initial graph:', err);
          setError('Failed to reload full graph. Please try again.');
          setLoading(false);
        });
    }
  }, [currentView, updateURL]);

  // Determine main content class based on panel visibility and collapse state
  const mainContentClass = `main-content ${showDetails ? 'with-details' : ''} ${showDetails && isDetailsCollapsed ? 'collapsed' : ''}`;

  // Handle analysis click - memoized
  const handleAnalysisClick = useCallback(async (type, node = selectedNode) => {
    if (!node && type !== 'centrality') {
      setError('Please select a node first');
      return;
    }

    setLoading(true);
    setError(null);
    setAnalysisType(type);

    try {
      let result;
      switch (type) {
        case 'impact':
          result = await runImpactAnalysis(node.id);
          break;
        case 'dependencies':
          result = await runDependencyAnalysis(node.id);
          break;
        case 'paths':
          if (!targetNode) {
            setError('Please select a target node');
            setLoading(false);
            return;
          }
          if (targetNode.id === node.id) {
            setError('Source and target nodes must be different');
            setLoading(false);
            return;
          }
          try {
            result = await findPaths(node.id, targetNode.id);
          } catch (pathError) {
            if (pathError.message.includes('No paths found')) {
              setError('No paths found between the selected nodes');
            } else {
              setError('Failed to find paths between nodes. Please try again.');
            }
            setLoading(false);
            return;
          }
          break;
        default:
          setError('Invalid analysis type');
          setLoading(false);
          return;
      }
      
      if (result) {
        // Convert edges to links if needed
        if (result.edges && !result.links) {
          result.links = result.edges.map(edge => ({
            ...edge,
            source: edge.from || edge.source,
            target: edge.to || edge.target,
            type: edge.label
          }));
          delete result.edges;
        }
        setGraphData(result);
      }
    } catch (err) {
      console.error('Error running analysis:', err);
      setError('Analysis failed. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [selectedNode, targetNode]);

  // Handle centrality analysis
  const handleCentralityAnalysis = useCallback(async (type) => {
    setLoading(true);
    setError(null);
    setAnalysisType('centrality');
    setCentralityType(type);
    
    try {
      const result = await runCentralityAnalysis(type);
      if (result) {
        setGraphData(result);
      }
    } catch (err) {
      console.error('Error running centrality analysis:', err);
      setError('Centrality analysis failed. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Update node configuration
  const handleNodeConfigChange = useCallback(async (newConfig) => {
    try {
      settingsService.update(newConfig); // Fixed: use 'update' instead of 'updateSettings'
    } catch (error) {
      console.error('Error updating node configuration:', error);
    }
  }, []);

  // Handle force configuration changes from Legend
  const handleForceConfigChange = useCallback((config) => {
    console.log('🔧 App: Force configuration changed:', config);
    // Force configuration is applied directly in the Legend component
    // This callback can be used for additional logic if needed
  }, []);

  // Handle node type visibility filter changes from Legend
  const handleFilterChange = useCallback((nodeTypeVisibility) => {
    console.log('🔍 App: Node type visibility changed:', nodeTypeVisibility);

    // Update the node configuration to include visibility settings
    const updatedConfig = {
      ...nodeConfig,
      nodeTypeVisibility
    };

    // Update the node configuration which will trigger graph re-render
    handleNodeConfigChange(updatedConfig);
  }, [nodeConfig, handleNodeConfigChange]);

  // Search functionality
  const handleSearch = useCallback((query) => {
    if (!query.trim() || !graphData?.nodes) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    const filtered = graphData.nodes
      .filter(node => {
        const name = node.name || node.properties?.name || '';
        return name.toLowerCase().includes(query.toLowerCase());
      })
      .slice(0, 10) // Limit to 10 results
      .map(node => ({
        id: node.id,
        name: node.name || node.properties?.name || 'Unnamed',
        category: node.properties?.category,
        group: node.group || node.labels?.[0] || 'Unknown',
        color: node.color || '#64748b',
        data: node
      }));

    setSearchResults(filtered);
    setShowSearchResults(filtered.length > 0);
  }, [graphData]);

  // Enhanced search query handler that also manages dropdown visibility
  const handleSearchQueryChange = useCallback((query) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  }, []);

  // Handle details panel collapse change
  const handleDetailsCollapseChange = useCallback((isCollapsed) => {
    setIsDetailsCollapsed(isCollapsed);
  }, []);



  // Debounced search to prevent performance issues
  const debouncedSearch = useCallback(
    debounce((query) => {
      handleSearch(query);
    }, 300), // 300ms delay
    [handleSearch]
  );

  // Center on node function - will be provided by UnifiedGraphWrapper
  const [centerOnNodeFn, setCenterOnNodeFn] = useState(null);

  const handleCenterOnNode = useCallback((node) => {
    setShowSearchResults(false);
    setSearchQuery('');
    if (centerOnNodeFn) {
      centerOnNodeFn(node.data || node);
    }
    // Also select the node to show NodeDetails panel
    handleNodeSelect(node.data || node);
  }, [centerOnNodeFn, handleNodeSelect]);

  // Callback to receive centerOnNode function from UnifiedGraphWrapper
  const handleCenterOnNodeReady = useCallback((centerFn) => {
    setCenterOnNodeFn(() => centerFn);
  }, []); // Empty dependency array to prevent infinite re-renders

  // Handle node click to close search dropdown
  const handleNodeClick = useCallback((node) => {
    setShowSearchResults(false);
    setSearchQuery('');
  }, []);

  // Handle node expansion from NodeDetails
  const handleNodeExpand = useCallback((expandedData) => {
    if (expandedData && expandedData.nodes && expandedData.edges && graphData && graphData.nodes && graphData.edges) {
      // Merge expanded data with existing graph data
      const existingNodeIds = new Set(graphData.nodes.map(node => node.id));
      const existingEdgeIds = new Set(graphData.edges.map(edge => edge.id));

      // Filter out nodes and edges that already exist
      const newNodes = expandedData.nodes.filter(node => !existingNodeIds.has(node.id));
      const newEdges = expandedData.edges.filter(edge => !existingEdgeIds.has(edge.id));

      if (newNodes.length > 0 || newEdges.length > 0) {
        setGraphData(prevData => ({
          nodes: [...prevData.nodes, ...newNodes],
          edges: [...prevData.edges, ...newEdges]
        }));
        console.log(`✅ Node expanded: Added ${newNodes.length} new nodes and ${newEdges.length} new edges`);
      } else {
        console.log('ℹ️ No new nodes or edges to add from expansion');
      }
    }
  }, [graphData]);

  const renderContent = () => {
    switch (currentView) {
      case 'analysis':
        return (
          <div className="content-wrapper">
            <h2>Graph Analysis</h2>
            <AdvancedAnalysisPanel />
          </div>
        );
      case 'chat':
        return (
          <div className="content-wrapper chat-content-wrapper">
            <ChatView onNodeSelect={handleNodeSelect} />
          </div>
        );
      case 'documentation':
        return (
          <DocumentationLayout
            expandedDoc={expandedDoc}
            onDocSelect={setExpandedDoc}
            docContent={docContent}
            docLoading={docLoading}
            docError={docError}
            updateURL={updateURL}
          />
        );


      default:
        return (
          <div className="content-wrapper">
            <div className="explorer-content">

              
              <div className="graph-layout">
                <div className="graph-container">
                  {loading && (
                    <div className="loading-overlay">
                      {loadingPhase === 'fetching' && <GraphSkeleton is3DMode={is3DMode} />}
                      {loadingPhase === 'transforming' && <DataProcessingSkeleton />}
                      {(loadingPhase === 'rendering' || loadingPhase === 'complete') && (
                        <div className="phase-loading-container">
                          <PhaseLoadingIndicator phase={loadingPhase} progress={loadingProgress} />
                        </div>
                      )}
                    </div>
                  )}
                  
                  {error && <div className="error-message">
                    {error}
                    <button className="retry-button" onClick={() => window.dispatchEvent(new Event('loadInitialGraph'))}>
                      Retry
                    </button>
                  </div>}
                  
                  {!loading && !error && (
                    <>
                      {hookResults.length > 0 ? (
                        <div className="search-results">
                          <h3>Search Results ({hookResults.length})</h3>
                          <ul className="result-list">
                            {hookResults.map(result => (
                              <li 
                                key={result.id} 
                                className="result-item"
                                onClick={() => handleNodeSelect(result)}
                              >
                                <span className="result-name">
                                  {result.properties?.name || 
                                   result.properties?.test_case_id || 
                                   result.label || 
                                   result.id}
                                </span>
                                <span className="result-type">
                                  {result.labels && result.labels.length > 0 
                                    ? result.labels[0] 
                                    : result.group || 'Node'}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : (
                        graphData ? (
                          <GraphViewWrapper
                            ref={forceGraphRef}
                            data={memoizedGraphData}
                            selectedNode={selectedNode}
                            onNodeSelect={handleNodeSelect}
                            onRelationshipSelect={handleRelationshipSelect}
                            customConfig={nodeConfig}
                            nodeColors={nodeConfig.colors}
                            nodeSizes={nodeConfig.sizes}
                            nodeShapes={nodeConfig.shapes}
                            relationshipColors={nodeConfig.relationshipColors}
                            relationshipLineStyles={nodeConfig.relationshipLineStyles}
                            onCenterOnNodeReady={handleCenterOnNodeReady}
                            onNodeClick={handleNodeClick}
                            onNodeExpand={handleNodeExpand}
                            is3DMode={is3DMode}
                            // Relationship filtering props
                            relationshipTypeFilter={relationshipTypeFilter}
                            isDetailView={isDetailView}
                            // 3D control props
                            nodeSize3D={nodeSize3D}
                            onEscape={() => {
                              setIs3DMode(false);
                              handleCloseDetails();
                            }}
                          />
                        ) : (
                          <div className="graph-placeholder">
                            <p>Search for a node or select a relationship type to visualize</p>
                          </div>
                        )
                      )}
                    </>
                  )}
                </div>
                
                {graphData && !loading && !error && !hookResults.length > 0 && (
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    {/* Search Field - positioned above legend */}
                    <SearchField
                      searchQuery={searchQuery}
                      setSearchQuery={handleSearchQueryChange}
                      searchResults={searchResults}
                      showSearchResults={showSearchResults}
                      onSearch={debouncedSearch}
                      onCenterOnNode={handleCenterOnNode}
                    />

                    {/* Legend Panel */}
                    <div className="legend-wrapper">
                      <Legend
                        data={memoizedGraphData}
                        initialConfig={nodeConfig}
                        onNodeConfigChange={handleNodeConfigChange}
                        onFilterChange={handleFilterChange}
                        // Node limit props
                        nodeLimit={nodeLimit}
                        setNodeLimit={setNodeLimitWithPersistence}
                        // 3D mode props
                        is3DMode={is3DMode}
                        nodeSize={nodeSize3D}
                        onNodeSizeChange={setNodeSize3D}
                        // 3D Force control props
                        forceGraphRef={forceGraphRef}
                        onForceConfigChange={handleForceConfigChange}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="app">
      <Header
        onSwitchView={setCurrentView}
        currentView={currentView}
        onNodeConfigChange={handleNodeConfigChange}
        config={nodeConfig}
        is3DMode={is3DMode}
        onToggle3DMode={(newMode) => {
          setIs3DMode(newMode);
          updateURL({ view: currentView, is3DMode: newMode });
        }}
      />
      <div className="app-container">
        <div className={mainContentClass}>
          {renderContent()}
        </div>
        {showDetails && (selectedNode || selectedRelationship) && (
          selectedNode ? (
            <NodeDetailsModern
              selectedNode={selectedNode}
              onClose={handleCloseDetails}
              onAnalysisResults={handleAnalysisResults}
              onNodeSelect={handleNodeSelect}
              onNodeExpand={handleNodeExpand}
              onRelationshipSelect={handleRelationshipSelect}
              onCollapseChange={handleDetailsCollapseChange}
            />
          ) : (
            <RelationshipDetails
              selectedRelationship={selectedRelationship}
              onClose={handleCloseDetails}
              onNodeSelect={handleNodeSelect}
            />
          )
        )}
      </div>
    </div>
  );
}

export default App;
