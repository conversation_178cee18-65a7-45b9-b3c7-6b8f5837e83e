/* Base styles */
:root {
  --primary-color: #00973A;  /* 360T green */
  --primary-dark: #007d30;
  --secondary-color: #f0f6ff;
  --text-color: #333;
  --border-color: #ddd;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --info-color: #2196f3;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  background-color: #f5f5f5;
  line-height: 1.6;
}

/* App Layout */
.app {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.app-container {
  display: flex;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease;
}

.main-content.with-details {
  width: 72%; /* Updated for 28% modern details panel (was 76% for 24% panel) */
}

.main-content.with-details.collapsed {
  width: 97%; /* When details panel is collapsed to 3% width */
}

/* Header Component */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1.5rem;
  background-color: var(--card-bg);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--border-color);
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  color: var(--primary-color);
  width: 2rem;
  height: 2rem;
}

.app-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.main-nav ul {
  display: flex;
  list-style: none;
  gap: 0.5rem;
}

.nav-button {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
}

.nav-button:hover {
  background-color: var(--background-color);
}

.nav-button.active {
  color: var(--primary-color);
  background-color: rgba(37, 99, 235, 0.1);
}

.user-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  color: var(--text-color);
  transition: var(--transition);
}

.icon-button:hover {
  background-color: var(--background-color);
}

.icon-button.active {
  background-color: var(--primary-color);
  color: white;
}

.icon-button.active:hover {
  background-color: var(--primary-dark);
}

/* Mode Toggle Button (2D/3D) */
.mode-toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  background-color: transparent;
  border: 1px solid var(--border-color);
  transition: var(--transition);
  min-width: 2.5rem;
  height: 2.5rem;
}

.mode-toggle-button:hover {
  background-color: var(--background-color);
  border-color: var(--primary-color);
}

.mode-toggle-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.mode-toggle-button.active:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* Phase Loading Container */
.phase-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 2rem;
}

.app-phase-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

/* Animation for collapsible sections */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Search Bar Component */
.search-wrapper {
  padding: 1rem;
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.search-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.search-form {
  display: flex;
  width: 100%;
}

.search-input-container {
  position: relative;
  flex: 1;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem 0 0 0.375rem;
  font-size: 1rem;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 151, 58, 0.2);
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.search-button:hover {
  background-color: var(--primary-dark);
}

.search-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.search-error {
  color: var(--error-color);
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

/* Search Results */
.search-results {
  padding: 1rem;
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.search-results h3 {
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--text-light);
}

.results-list {
  list-style: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 0.5rem;
}

.result-item {
  display: flex;
}

.result-button {
  display: flex;
  flex-direction: column;
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  transition: var(--transition);
}

.result-button:hover {
  border-color: var(--primary-light);
  box-shadow: var(--shadow-sm);
}

.result-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.result-type {
  font-size: 0.75rem;
  color: var(--text-light);
  padding: 0.125rem 0.5rem;
  background-color: var(--background-color);
  border-radius: 1rem;
  display: inline-block;
}

/* Graph Component */
.explorer-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--header-height, 96px)); /* Dynamic header height with realistic fallback */
  width: 100%;
  overflow: hidden;
}

.graph-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  overflow: hidden;
}

.graph-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.graph-svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* Graph element styles */
.graph-link {
  stroke: #999;
  stroke-opacity: 0.6;
}

.node-group circle {
  stroke: #fff;
  stroke-width: 2px;
}

.node-label {
  fill: #333;
  font-size: 12px;
  pointer-events: none;
}

.link-label {
  fill: #666;
  text-anchor: middle;
  pointer-events: none;
}

/* Loading and error states - App level */
.app-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
}

.app-loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 1rem;
  margin: 1rem;
  background-color: #fff5f5;
  border: 1px solid #feb2b2;
  border-radius: 4px;
  color: #e53e3e;
  text-align: center;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: var(--primary-dark);
}

/* Details Panel */
.details-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 28%; /* Updated to match modern details panel width */
  height: 100%;
  background-color: var(--card-bg);
  border-left: 1px solid var(--border-color);
  z-index: 10;
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.empty-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-style: italic;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: white;
  flex-shrink: 0;
}

.panel-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.close-button {
  font-size: 1.5rem;
  line-height: 1;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  color: var(--text-light);
  transition: var(--transition);
}

.close-button:hover {
  background-color: var(--background-color);
  color: var(--text-color);
}

.details-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* New vertical property layout */
.properties-vertical {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.property-item-vertical {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.property-name-green {
  font-weight: 600;
  color: #00973A;
  font-size: 0.9rem;
  text-transform: capitalize;
}

.property-value-vertical {
  color: #333;
  font-size: 0.9rem;
  line-height: 1.4;
  word-break: break-word;
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #00973A;
}
}

.detail-group {
  margin-bottom: 1.5rem;
}

.detail-type {
  color: var(--text-light);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.detail-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.properties-list,
.relationships-list {
  margin-bottom: 1.5rem;
}

.properties-list h4,
.relationships-list h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.properties-list table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.properties-list th {
  text-align: left;
  font-weight: 500;
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-light);
  width: 40%;
  word-wrap: break-word;
  word-break: break-word;
}

.properties-list td {
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  max-width: 0;
}

.relationships-list li {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
}

.relationship-item:hover .node-name {
  background-color: rgba(0, 151, 58, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.relationship-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.relationship-item:hover {
  background-color: rgba(0, 151, 58, 0.05);
}

.relationship-inline {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.relationship-type {
  font-size: 0.75rem;
  text-transform: uppercase;
  color: var(--text-light);
  font-weight: 600;
}

.relationship-direction {
  font-size: 1rem;
  color: var(--text-light);
  margin: 0 2px;
}

.relationship-icon {
  width: 8px;
  height: 8px;
  margin: 0 4px 0 8px;
  vertical-align: middle;
  object-fit: contain;
  flex-shrink: 0;
}

.node-name {
  font-weight: 500;
  color: var(--primary-color);
  margin-right: 2px;
}

.node-type-separator {
  margin: 0 2px;
  color: var(--text-light);
}

.node-type {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Mentions section styling */
.mentions-section {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  color: var(--text-light);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Outgoing relationships styling */
.outgoing-section {
  color: #059669; /* Emerald green */
  border-left: 3px solid #059669;
  padding-left: 12px;
  margin-bottom: 0.75rem;
}

.outgoing-item {
  border-left: 2px solid #d1fae5; /* Light emerald */
  background-color: rgba(5, 150, 105, 0.02);
}

.outgoing-item:hover {
  background-color: rgba(5, 150, 105, 0.05);
  border-left-color: #059669;
}

.outgoing-item .relationship-direction {
  color: #059669;
  font-weight: 600;
}

/* Incoming relationships styling */
.incoming-section {
  color: #dc2626; /* Red */
  border-left: 3px solid #dc2626;
  padding-left: 12px;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

.incoming-item {
  border-left: 2px solid #fecaca; /* Light red */
  background-color: rgba(220, 38, 38, 0.02);
}

.incoming-item:hover {
  background-color: rgba(220, 38, 38, 0.05);
  border-left-color: #dc2626;
}

.incoming-item .relationship-direction {
  color: #dc2626;
  font-weight: 600;
}

.mentions-list {
  opacity: 0.8;
}

.mentions-item {
  background-color: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.1);
}

.mentions-item:hover {
  background-color: rgba(0, 151, 58, 0.03);
}

.mentions-item .relationship-type {
  color: #6b7280;
  font-size: 0.7rem;
}

.mentions-item .node-name {
  color: #6b7280;
  font-weight: 400;
}

.mentions-item .node-type {
  color: #9ca3af;
  font-size: 0.8rem;
}

/* Tooltip Styles */
.tooltip-wrapper {
  position: relative;
  display: inline-block;
}

.tooltip-trigger.has-tooltip {
  cursor: help;
  border-bottom: 1px dotted #999;
}

.tooltip-trigger.has-tooltip:hover {
  border-bottom-color: var(--primary-color);
}

.custom-tooltip {
  background-color: #2d3748;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.875rem;
  line-height: 1.4;
  word-wrap: break-word;
  z-index: 1000;
  animation: tooltipFadeIn 0.2s ease-out;
}

.tooltip-content {
  position: relative;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #2d3748;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1);
  }
}

/* React Force Graph 3D Tooltip Overrides */
.graph-tooltip, .scene-tooltip, [class*="tooltip"] {
  max-width: none !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  max-height: 300px !important;
  overflow-y: auto !important;
  line-height: 1.4 !important;
  padding: 8px 12px !important;
  font-size: 12px !important;
  background-color: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  z-index: 10000 !important;
}

.detail-actions {
  margin-top: auto;
  padding-top: 1rem;
}

.action-button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.action-button:hover {
  background-color: var(--primary-dark);
}

.action-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .main-content.with-details {
    width: 68%;
  }

  .details-panel {
    width: 32%;
  }
}

@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    padding: 0.75rem;
  }
  
  .main-nav ul {
    margin-top: 0.5rem;
  }
  
  .main-content.with-details {
    width: 0;
  }
  
  .details-panel {
    width: 100%;
  }
}

.content-wrapper {
  padding: 2rem;
  height: 100%;
  overflow-y: auto;
}

/* Specific styling for chat view to maximize space */
.chat-content-wrapper {
  padding: 0;
  width: 100%;
  max-width: none;
  margin: 0;
  height: 100vh;
  overflow: visible; /* FIXED: Allow scrolling in child containers */
  display: flex;
  flex-direction: column;
}

.content-wrapper h2 {
  margin-bottom: 2rem;
  color: #1a237e;
  font-size: 1.8rem;
}

.analysis-content,
.documentation-content {
  max-width: 800px;
  margin: 0 auto;
}

.analysis-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analysis-section h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.analysis-section ul {
  list-style: none;
  padding: 0;
}

.analysis-section li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.analysis-section li:last-child {
  border-bottom: none;
}

.documentation-content section {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.documentation-content h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.documentation-content p {
  line-height: 1.6;
  color: #34495e;
  margin-bottom: 1rem;
}

.documentation-content ul {
  list-style: disc;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.documentation-content li {
  padding: 0.3rem 0;
  color: #34495e;
}

.documentation-content strong {
  color: #1a237e;
  font-weight: 600;
}

/* Analysis Tools */
.analysis-tools {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tool-button {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.tool-button:hover:not(:disabled) {
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tool-button.active {
  border-color: var(--primary-color);
  background-color: #f0f7ff;
}

.tool-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tool-description {
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.tool-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.target-node-selector,
.centrality-type-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border-radius: 6px;
}

.target-node-selector label,
.centrality-type-selector label {
  font-size: 0.875rem;
  color: #475569;
  white-space: nowrap;
}

.target-node-selector select,
.centrality-type-selector select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  font-size: 0.875rem;
}

.helper-text {
  color: #64748b;
  font-style: italic;
  margin-top: 1rem;
  text-align: center;
}

.error-message {
  color: #ef4444;
  padding: 0.75rem;
  background: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 6px;
  margin-top: 1rem;
  font-size: 0.875rem;
}

/* Analysis Results */
.analysis-section .graph-wrapper {
  height: 500px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
}

/* Legend Component */
.legend-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 16px;
  margin: 16px;
  max-width: 264px;
}

.legend-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.legend-section-title {
  font-size: 1rem;
  font-weight: 500;
  margin: 14px 0 10px 0;
  color: #555;
}

.legend-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.legend-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
  color: white;
  font-weight: 500;
  min-width: 40px;
  min-height: 24px;
  white-space: nowrap;
  text-align: center;
  line-height: 1;
}

.all-nodes, .all-relationships {
  background-color: #94a3b8;
}

.relationship-badge {
  background-color: #00973A; /* 360T green */
}

.relationship-badge[key="USES"] {
  background-color: #3b82f6; /* blue */
}

.relationship-badge[key="CONTAINS"] {
  background-color: #ec4899; /* pink */
}

.relationship-badge[key="NAVIGATES_TO"] {
  background-color: #8b5cf6; /* purple */
}

.relationship-badge[key="VALIDATES"] {
  background-color: #f59e0b; /* amber */
}

.relationship-badge[key="REQUIRES"] {
  background-color: #ef4444; /* red */
}

.relationship-badge[key="CONFIGURES_IN"] {
  background-color: #06b6d4; /* cyan */
}

.relationship-badge[key="DISPLAYS"] {
  background-color: #f97316; /* orange */
}

.legend-summary {
  font-size: 0.8rem;
  color: #666;
  margin-top: 12px;
  border-top: 1px solid #eee;
  padding-top: 8px;
}

/* Graph Layout */
.graph-layout {
  display: flex;
  flex: 1;
  height: calc(100vh - 180px);
  overflow: hidden;
}

.graph-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.legend-wrapper {
  width: 280px;
  overflow-y: auto;
  border-left: 1px solid var(--border-color);
  background-color: #f8fafc;
  box-sizing: border-box;
}

/* Color Picker Modal */
.color-picker-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.color-picker-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 350px;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.modal-content {
  padding: 20px;
}

.color-picker-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.color-picker-row label {
  flex: 0 0 100px;
  font-weight: 500;
  color: #4a5568;
}

.color-picker-row input[type="color"] {
  width: 50px;
  height: 30px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
}

.color-picker-row input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.size-value {
  font-size: 0.9rem;
  color: #718096;
  width: 40px;
  text-align: right;
}

.node-preview {
  margin: 20px auto;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.modal-footer {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e2e8f0;
}

.apply-button, .cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.apply-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.apply-button:hover {
  background-color: var(--primary-dark);
}

.cancel-button {
  background-color: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.cancel-button:hover {
  background-color: #f7fafc;
}

/* Legend badge click interaction */
.legend-badge {
  cursor: pointer;
  transition: all 0.2s;
}

.legend-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.legend-hint {
  font-size: 0.75rem;
  color: #888;
  font-style: italic;
  margin-top: 4px;
}

/* Filtering functionality styles */
.filtering-stats {
  background: rgba(0, 151, 58, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(0, 151, 58, 0.3);
}

.legend-node-item {
  transition: all 0.2s ease;
  border-radius: 4px;
}

.legend-node-item:hover {
  background-color: rgba(0, 151, 58, 0.05) !important;
}

/* Checkbox styling */
.legend-node-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

.legend-node-item input[type="checkbox"]:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Back button in node details */
.back-button {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;
  padding: 8px 16px;
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  background-color: #cbd5e0;
}

.back-button:before {
  content: "←";
  margin-right: 8px;
  font-size: 1.1rem;
}

/* Panel footer */
.panel-footer {
  margin-top: auto;
  padding: 12px 16px;
  border-top: 1px solid #e2e8f0;
  font-size: 0.8rem;
  color: #718096;
  background-color: #f7fafc;
}

.hint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}

.hint:before {
  content: "⌨";
  margin-right: 8px;
  font-size: 1rem;
}

/* Search input container and suggestions */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background-color: rgba(0, 151, 58, 0.05);
}

.suggestion-item.selected {
  background-color: rgba(0, 151, 58, 0.1);
  border-left: 3px solid #00973A;
}

.suggestion-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-name {
  font-weight: 500;
}

.suggestion-type {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  background-color: #00973A;
  margin-left: 10px;
}

/* Node type colors for suggestion badges */
.suggestion-item.module .suggestion-type {
  background-color: #4f46e5; 
}

.suggestion-item.product .suggestion-type {
  background-color: #059669;
}

.suggestion-item.workflow .suggestion-type {
  background-color: #d97706;
}

.suggestion-item.ui_area .suggestion-type {
  background-color: #7c3aed;
}

.suggestion-item.configurationitem .suggestion-type {
  background-color: #db2777;
}

.suggestion-item.testcase .suggestion-type {
  background-color: #dc2626;
}

/* Error Boundary Styles */
.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-content h2 {
  color: #dc3545;
  margin-bottom: 16px;
}

.error-content p {
  color: #6c757d;
  margin-bottom: 20px;
  line-height: 1.5;
}

.error-reload-btn:hover {
  background-color: #0056b3 !important;
}
