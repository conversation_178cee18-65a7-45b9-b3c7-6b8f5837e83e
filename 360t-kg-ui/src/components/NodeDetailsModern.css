/* Professional Finance NodeDetails Component Styles */
.details-panel-modern {
  position: fixed !important;
  top: 60px !important;
  right: 0 !important;
  width: 28% !important;
  height: calc(100vh - 60px) !important;
  background: var(--360t-white) !important;
  border-left: 1px solid var(--360t-mid-gray) !important;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.08) !important;
  z-index: 1000 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  font-family: var(--360t-font-family-primary) !important;
  font-size: 13px !important;
  transition: width 0.3s ease !important;
}

/* Collapsed Panel State */
.details-panel-modern.collapsed {
  width: 3% !important;
  min-width: 40px !important;
  max-width: 60px !important;
  overflow: hidden !important;
  box-shadow: -1px 0 4px rgba(0, 0, 0, 0.04) !important;
}

.details-panel-modern.collapsed .panel-header-modern {
  flex-direction: column !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 4px !important;
  justify-content: flex-start !important;
}

.details-panel-modern.collapsed .node-icon-container {
  flex-direction: column !important;
  align-items: center !important;
  gap: 4px !important;
}

.details-panel-modern.collapsed .relationship-icon {
  width: 24px !important;
  height: 24px !important;
  margin-bottom: 4px !important;
}

.details-panel-modern.collapsed .panel-header-controls {
  flex-direction: column !important;
  gap: 4px !important;
}

/* Resize Handle */
.resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: #e5e7eb;
  cursor: col-resize;
  z-index: 1001;
  transition: background 0.2s ease;
}

.resize-handle:hover {
  background: #00973a;
}

.resize-handle:active {
  background: #00973a;
}

.details-panel-modern.empty-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--360t-dark-gray);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.02em;
  background: var(--360t-white);
}

/* Professional Header */
.panel-header-modern {
  padding: 12px 16px 10px;
  border-bottom: 1px solid var(--360t-mid-gray);
  background: linear-gradient(135deg, var(--360t-primary) 0%, var(--360t-primary-dark) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.panel-header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.collapse-button-modern {
  background: #e5e7eb;
  border: none;
  width: 22px;
  height: 22px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
  color: #6b7280;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.collapse-button-modern:hover {
  background: #00973a;
  color: white;
  transform: scale(1.1);
}

.node-title-modern {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--360t-white);
  line-height: 1.3;
  max-width: 200px;
  word-wrap: break-word;
  letter-spacing: -0.01em;
}

.detail-type-modern {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
  display: block;
  background: #e5e7eb;
  padding: 1px 4px;
  border-radius: 3px;
  display: inline-block;
}

.category-badge-main {
  font-size: 9px;
  padding: 2px 5px;
  border-radius: 3px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.2);
  color: var(--360t-white);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.close-button-modern {
  background: #e5e7eb;
  border: none;
  width: 22px;
  height: 22px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 13px;
  color: #6b7280;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.close-button-modern:hover {
  background: #d1d5db;
  color: #374151;
}

/* Professional Content Area */
.details-content-modern {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 12px 16px 16px !important;
  scrollbar-width: thin !important;
  scrollbar-color: var(--360t-mid-gray) transparent !important;
  background: var(--360t-white) !important;
}

.details-content-modern::-webkit-scrollbar {
  width: 8px;
}

.details-content-modern::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 4px;
}

.details-content-modern::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  border: 1px solid #9ca3af;
}

.details-content-modern::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Professional Card-Based Sections */
.section-card-modern {
  margin-bottom: 12px !important;
  background: var(--360t-white) !important;
  border-radius: 6px !important;
  border: 1px solid var(--360t-mid-gray) !important;
  overflow: hidden !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
}

.section-card-modern:hover {
  border-color: var(--360t-primary) !important;
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.1) !important;
  transform: translateY(-1px) !important;
  background: var(--360t-light-gray) !important;
}

/* Professional Card Headers */
.section-header-card {
  padding: 8px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 32px;
  position: relative;
}

.section-header-card.clickable {
  cursor: pointer;
  transition: background 0.2s ease;
}

.section-header-card.clickable:hover {
  background: #f3f4f6;
  box-shadow: inset 0 1px 2px rgba(0, 151, 58, 0.1);
}

.section-icon-title {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.section-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.section-title-card {
  margin: 0;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: -0.01em;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.section-count {
  background: #00973a;
  color: white;
  font-size: 9px;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
}

.expand-icon-card {
  font-size: 12px;
  font-weight: 600;
  color: #00973a;
  background: #f0fdf4;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: 1px solid #bbf7d0;
}

.expand-icon-card:hover {
  background: #dcfce7;
  transform: scale(1.05);
}

.expand-icon-card.expanded {
  background: #00973a;
  color: white;
  border-color: #00973a;
}

/* Preview Indicators */
.preview-indicator-card {
  padding: 4px 12px;
  background: #f9fafb;
  border-bottom: 1px solid #d1d5db;
  font-size: 9px;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
  font-style: italic;
}

/* Backward compatibility for old sections */
.section-modern {
  margin-bottom: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #f3f4f6;
  overflow: hidden;
  transition: all 0.15s ease;
}

.section-modern:hover {
  border-color: #e5e7eb;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.section-title-modern {
  margin: 0;
  padding: 8px 12px 6px;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  background: #f9fafb;
  border-bottom: 1px solid #f3f4f6;
}

/* Direct Properties Content - No Wrapper */
.direct-properties-content {
  margin-bottom: 12px;
}

/* Professional Properties Content */
.properties-content-card {
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 0 0 6px 6px;
}

.property-pdf-card {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid #e5e7eb;
}

.pdf-document-link-card {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f0fdf4;
  color: #059669;
  text-decoration: none;
  border-radius: 4px;
  border: 1px solid #bbf7d0;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pdf-document-link-card:hover {
  background: #dcfce7;
  border-color: #86efac;
  text-decoration: none;
  color: #047857;
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.1);
}

.pdf-icon-card,
.external-link-icon {
  font-size: 12px;
  flex-shrink: 0;
}

.pdf-text-card {
  font-weight: 600;
}

/* Professional Labels Container */
.labels-container-card {
  padding: 10px 16px;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  background: #ffffff;
  border-radius: 0 0 6px 6px;
}

.label-badge-card {
  background: #f3f4f6;
  color: #374151;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  letter-spacing: 0.01em;
}

.label-badge-card:hover {
  background: #00973a;
  color: white;
  transform: scale(1.02);
}

/* Professional Show All Button */
.show-all-button-card {
  width: 100%;
  padding: 6px 10px;
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 0 0 6px 6px;
  color: #6b7280;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: 0;
}

.show-all-button-card:hover {
  background: #f0fdf4;
  color: #00973a;
  border-color: #00973a;
  transform: translateY(-1px);
}

.show-all-button-card span {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* Professional Expandable Text Components */
.expandable-text-container {
  margin-bottom: 8px;
  border-radius: 4px;
  overflow: hidden;
  background: white;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.expandable-text-container:hover {
  border-color: #00973a;
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.1);
}

/* Compact Properties Section (backward compatibility) */
.properties-section {
  margin-top: 8px;
}

.properties-content {
  padding: 8px 12px;
}

.property-name-green {
  font-size: 10px;
  font-weight: 600;
  color: #374151;
  text-transform: capitalize;
  padding: 4px 8px;
  letter-spacing: 0.01em;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.property-value-expandable {
  position: relative;
  background: #ffffff;
}

.text-content {
  font-size: 13px;
  line-height: 1.6;
  color: #374151;
  padding: 8px 10px;
  background: #ffffff;
  border: none;
  margin: 0;
  word-wrap: break-word;
  font-weight: 400;
  letter-spacing: 0.01em;
}

/* Bullet point text content styling */
.text-content-bulleted {
  font-size: 13px;
  line-height: 1.6;
  color: #374151;
  padding: 8px 10px;
  background: #ffffff;
  border: none;
  margin: 0;
  word-wrap: break-word;
  font-weight: 400;
  letter-spacing: 0.01em;
}

.sentence-bullet-list {
  margin: 0;
  padding-left: 16px;
  list-style-type: none;
  position: relative;
}

.sentence-bullet-item {
  position: relative;
  padding-left: 12px;
  margin-bottom: 8px;
  line-height: 1.6;
  font-size: 13px;
  color: #374151;
  word-wrap: break-word;
}

.sentence-bullet-item:before {
  content: "•";
  color: #00973a;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 14px;
  line-height: 1.6;
}

.sentence-bullet-item:last-child {
  margin-bottom: 4px;
}

/* Professional Inline expand/collapse button */
.expand-text-inline {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #059669;
  font-size: 9px;
  font-weight: 600;
  cursor: pointer;
  padding: 1px 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin-left: 4px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  box-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
}

.expand-text-inline:hover {
  background: #dcfce7;
  color: #047857;
  transform: scale(1.02);
  box-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
}

/* Professional Inline [...more] button */
.expand-text-more {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  color: #6b7280;
  font-size: 9px;
  font-weight: 500;
  cursor: pointer;
  padding: 1px 4px;
  margin-left: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
  text-decoration: none;
  font-style: normal;
  display: inline-flex;
  align-items: center;
  gap: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.expand-text-more:hover {
  background: #00973a;
  color: white;
  border-color: #00973a;
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.2);
}

/* Compact PDF Link */
.property-pdf-modern {
  margin-top: 6px;
}

.pdf-document-link-modern {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 6px;
  background: #f3f4f6;
  color: #374151;
  text-decoration: none;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.15s ease;
}

.pdf-document-link-modern:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  text-decoration: none;
  color: #111827;
}

.pdf-icon-modern {
  height: 12px;
  width: 12px;
}

/* Compact Labels Section */
.labels-section {
  margin-bottom: 12px;
}

.labels-container {
  padding: 6px 12px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.label-badge {
  background: #e0f2fe;
  color: #0277bd;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid #b3e5fc;
}

/* Ultra-Compact Collapsible Sections */
.collapsible-section {
  margin-bottom: 4px;
  background: white;
  border-radius: 4px;
  border: 1px solid #f3f4f6;
  overflow: hidden;
}

.section-header {
  padding: 3px 8px;
  background: #f9fafb;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.15s ease;
}

.section-header:hover {
  background: #f3f4f6;
}

.section-header:hover .expand-icon {
  background: #dbeafe;
  border-color: #93c5fd;
  color: #1d4ed8;
}

.section-title {
  margin: 0;
  font-size: 9px; /* Further reduced for maximum space efficiency */
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 4px;
}

.expand-icon {
  font-size: 10px;
  transition: transform 0.15s ease;
  color: #3b82f6;
  font-weight: 700;
  background: #eff6ff;
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid #dbeafe;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.preview-indicator {
  font-size: 8px;
  color: #6b7280;
  font-weight: 500;
}

.section-content,
.section-preview {
  padding: 0;
}

.show-all-button {
  width: 100%;
  padding: 6px 8px;
  background: #eff6ff;
  border: none;
  border-top: 1px solid #dbeafe;
  color: #1d4ed8;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.15s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.show-all-button:hover {
  background: #dbeafe;
  color: #1e40af;
}

/* Professional Relationship Items - Compact Layout */
.relationship-item-modern {
  margin: 3px 6px;
  padding: 6px 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  display: flex;
  align-items: center;
  min-height: 28px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.relationship-item-modern:hover {
  background: #f9fafb;
  border-color: #00973a;
  transform: translateX(2px) translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.08);
}

.relationship-content {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 4px;
}

/* Enhanced Left side - Relationship type */
.relationship-type-container {
  flex: 0 0 40%;
  display: flex;
  align-items: center;
  min-width: 0;
}

.relationship-type-compact {
  font-size: 9px !important;
  font-weight: 500 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif !important;
  color: #374151 !important;
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid #d1d5db;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  transition: all 0.2s ease;
  text-align: center;
  letter-spacing: 0.01em !important;
}

.relationship-type-compact:hover {
  background: #00973a !important;
  color: white !important;
  transform: scale(1.02);
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.2);
}

/* Enhanced Center - Direction arrow button */
.relationship-direction-container {
  flex: 0 0 12%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.relationship-direction {
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  padding: 3px 4px;
  border-radius: 50%;
  border: 1px solid;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 20px;
  height: 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* Professional Outgoing arrow - Muted red */
.relationship-direction.outgoing {
  color: #dc2626;
  border-color: #fca5a5;
  background: #fef2f2;
}

.relationship-direction.outgoing:hover {
  background: #fee2e2 !important;
  border-color: #f87171 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2) !important;
}

/* Professional Incoming arrow - Muted green */
.relationship-direction.incoming {
  color: #059669;
  border-color: #86efac;
  background: #f0fdf4;
}

.relationship-direction.incoming:hover {
  background: #dcfce7 !important;
  border-color: #4ade80 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2) !important;
}

/* Professional Right side - Target node button */
.relationship-target {
  flex: 0 0 48%;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 0;
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 3px 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.relationship-target:hover {
  background: #f0fdf4;
  border-color: #00973a;
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.1);
}

.node-name-modern {
  font-size: 11px !important;
  color: #374151 !important;
  font-weight: 600 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1;
  min-width: 0;
  letter-spacing: -0.01em !important;
}

.category-badge-small {
  font-size: 7px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif !important;
  padding: 1px 3px;
  border-radius: 6px;
  font-weight: 500 !important;
  text-transform: uppercase;
  letter-spacing: 0.2px !important;
  flex-shrink: 0;
  margin-left: 1px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}





.back-button-modern {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.back-button-modern:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Professional Footer */
.panel-footer-modern {
  padding: 8px 16px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  text-align: center;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.04);
}

.hint-modern {
  margin: 0;
  font-size: 10px;
  color: #6b7280;
  font-style: italic;
  font-weight: 400;
  letter-spacing: 0.01em;
}

/* Professional Error Messages */
.error-message-modern {
  background: #fef2f2;
  color: #dc2626;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #fecaca;
  margin: 8px 16px;
  font-size: 11px;
  font-weight: 500;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 2px rgba(220, 38, 38, 0.08);
}

/* Professional Error Item Styling */
.error-item {
  padding: 4px 8px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  color: #dc2626;
  font-size: 10px;
  font-weight: 400;
  margin: 2px 0;
  text-align: center;
  font-style: italic;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 2px rgba(220, 38, 38, 0.06);
}

/* Enhanced Text Formatting for Relationship Terms */
.relationship-term {
  font-weight: 600;
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 1px 2px;
  margin: 0 1px;
  cursor: default;
  letter-spacing: 0.01em;
}

.relationship-term.relationship-type {
  color: #00973a;
  background: rgba(0, 151, 58, 0.1);
  border: 1px solid rgba(0, 151, 58, 0.2);
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.95em;
}

.relationship-term.relationship-type:hover {
  background: rgba(0, 151, 58, 0.2);
  border-color: rgba(0, 151, 58, 0.4);
  transform: scale(1.02);
}



.relationship-term.node-link {
  color: #00973A;
  font-weight: 700;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 0 1px;
}

.relationship-term.node-link:hover {
  color: #007d30;
  text-decoration: underline;
  text-decoration-color: #007d30;
  transform: scale(1.01);
}

/* Enhanced bullet points with formatted terms */
.sentence-bullet-item .relationship-term {
  display: inline;
  margin: 0;
  padding: 0 1px;
}

/* Tooltip styling for relationship terms */
.relationship-term[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 400;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

/* Professional Loading State - NodeDetailsModern specific */
.details-panel-modern .loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: #6b7280;
  background: #f9fafb;
  font-weight: 400;
  letter-spacing: 0.01em;
}

.details-panel-modern .loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(229, 231, 235, 0.3);
  border-top: 2px solid #00973a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .details-panel-modern {
    width: 32%;
  }
}

@media (max-width: 768px) {
  .details-panel-modern {
    width: 100%;
    height: 100vh;
  }
  
  .node-title-modern {
    font-size: 1.1rem;
    max-width: 180px;
  }
  
  .detail-actions-modern {
    flex-direction: column;
  }
}

/* RelationshipDetails Modern Styles */
.connection-label-modern {
  color: #00973a;
  font-size: 0.85rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.node-connection-modern {
  margin-bottom: 16px;
}

.relationship-node-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
}

.relationship-node-modern:hover {
  background: #e9ecef;
  border-color: #00973a;
}

.relationship-node-modern.clickable:hover {
  background: #f0f8f0;
}

.node-content-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.arrow-modern {
  color: #00973a;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.node-name-modern {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.node-category-badge-modern {
  font-size: 10px;
  padding: 3px 6px;
  border-radius: 3px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  flex-shrink: 0;
}

.node-type-modern {
  font-size: 0.75rem;
  color: #6c757d;
  flex-shrink: 0;
}