import React, { useState, useEffect } from 'react';
import { useSettings } from '../hooks/useSettings';
// import PerformanceMetrics from './PerformanceMetrics'; // Removed during revert
import './GraphitiConfigPanel.css';

const GraphitiConfigPanel = ({ isOpen, onClose }) => {
  const { settings, set } = useSettings();
  const [ollamaModels, setOllamaModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [validationState, setValidationState] = useState({});
  const [connectionStatus, setConnectionStatus] = useState({
    ollama: null, // null, 'testing', 'success', 'error'
    lastTest: null
  });
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);

  const graphitiSettings = settings.graphiti || {};

  // Validation rules
  const validateSetting = (key, value) => {
    switch (key) {
      case 'diversityFactor':
        return value >= 0 && value <= 1 ? 'valid' : 'invalid';
      case 'edgeCount':
        return value >= 1 && value <= 20 ? 'valid' : 'warning';
      case 'nodeCount':
        return value >= 1 && value <= 10 ? 'valid' : 'warning';
      case 'temperature':
        return value >= 0 && value <= 1 ? 'valid' : 'invalid';
      case 'timeout':
        return value >= 30 && value <= 600 ? 'valid' : 'warning';
      case 'ollamaUrl':
        try {
          new URL(value);
          return 'valid';
        } catch {
          return 'invalid';
        }
      case 'ollamaModel':
        return value && value.trim() ? 'valid' : 'warning';
      default:
        return 'valid';
    }
  };

  // Update validation state when settings change
  useEffect(() => {
    const newValidationState = {};
    Object.entries(graphitiSettings).forEach(([key, value]) => {
      newValidationState[key] = validateSetting(key, value);
    });
    setValidationState(newValidationState);
  }, [graphitiSettings]);

  // Test Ollama connection
  const testOllamaConnection = async (url) => {
    if (!url) return;
    
    setConnectionStatus(prev => ({ ...prev, ollama: 'testing' }));
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${url}/api/tags`, {
        signal: controller.signal,
        method: 'GET'
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        setConnectionStatus({
          ollama: 'success',
          lastTest: new Date().toLocaleTimeString()
        });
      } else {
        setConnectionStatus({
          ollama: 'error',
          lastTest: new Date().toLocaleTimeString()
        });
      }
    } catch (error) {
      setConnectionStatus({
        ollama: 'error',
        lastTest: new Date().toLocaleTimeString()
      });
    }
  };

  // Auto-test Ollama connection when URL changes
  useEffect(() => {
    if (graphitiSettings.llmProvider === 'ollama' && graphitiSettings.ollamaUrl) {
      const timeoutId = setTimeout(() => {
        testOllamaConnection(graphitiSettings.ollamaUrl);
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [graphitiSettings.ollamaUrl, graphitiSettings.llmProvider]);

  // Available Graphiti search types from the documentation
  const searchTypes = [
    { value: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER', label: 'Hybrid Search with Cross-Encoder (Precision)' },
    { value: 'COMBINED_HYBRID_SEARCH_MMR', label: 'Hybrid Search with MMR (Diversity)' },
    { value: 'COMBINED_HYBRID_SEARCH_RRF', label: 'Hybrid Search with RRF (Relevance)' },
    // Removed VECTOR_SEARCH_ONLY and BM25_SEARCH_ONLY as they're not supported by the Python script
  ];

  const llmProviders = [
    { value: 'ollama', label: 'Ollama (Local)' },
    { value: 'openai', label: 'OpenAI' },
    { value: 'google', label: 'Google Gemini' },
  ];

  // Configuration presets with modern SVG icons
  const configPresets = {
    performance: {
      name: 'Performance',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/>
        </svg>
      ),
      description: 'Fast responses with minimal resources',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_MMR',
        edgeCount: 3,
        nodeCount: 1,
        temperature: 0.1,
        timeout: 60,
        diversityFactor: 0.2
      }
    },
    balanced: {
      name: 'Balanced',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2l3 7h7l-5.5 4 2 7-6.5-5-6.5 5 2-7L2 9h7z"/>
        </svg>
      ),
      description: 'Good balance of quality and speed (default)',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_MMR',
        edgeCount: 6,
        nodeCount: 2,
        temperature: 0.3,
        timeout: 180,
        diversityFactor: 0.3
      }
    },
    quality: {
      name: 'High Quality',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M6 3h12l4 6-10 13L2 9l4-6z"/>
        </svg>
      ),
      description: 'Maximum detail and comprehensiveness',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_RRF',
        edgeCount: 15,
        nodeCount: 5,
        temperature: 0.5,
        timeout: 300,
        diversityFactor: 0.4
      }
    },
    research: {
      name: 'Research',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M9 11a3 3 0 1 0 6 0a3 3 0 0 0-6 0"/>
          <path d="M17.5 17.5L22 22"/>
          <path d="M15 7a3 3 0 1 0-6 0"/>
        </svg>
      ),
      description: 'Maximum diversity for exploration',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_MMR',
        edgeCount: 20,
        nodeCount: 10,
        temperature: 0.7,
        timeout: 600,
        diversityFactor: 0.8
      }
    },
    debug: {
      name: 'Debug',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M8 6h8"/>
          <path d="M6 12h12"/>
          <path d="M8 18h8"/>
        </svg>
      ),
      description: 'Single focused result for testing',
      settings: {
        searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        edgeCount: 1,
        nodeCount: 1,
        temperature: 0.0,
        timeout: 30,
        diversityFactor: 0.1
      }
    }
  };

  // Fetch available Ollama models when component mounts or when Ollama is selected
  useEffect(() => {
    if (graphitiSettings.llmProvider === 'ollama') {
      fetchOllamaModels();
    }
  }, [graphitiSettings.llmProvider]);

  const fetchOllamaModels = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ollama/models');
      if (response.ok) {
        const models = await response.json();
        // Filter out embedding models
        const chatModels = models.filter(model => 
          !model.name.includes('embed') && 
          !model.name.includes('embedding') &&
          !model.name.includes('nomic')
        );
        setOllamaModels(chatModels);
      } else {
        console.warn('Failed to fetch Ollama models');
        setOllamaModels([{ name: 'qwen3:30b-a3b-q8_0' }, { name: 'gemma3' }]); // Fallback models
      }
    } catch (error) {
      console.warn('Error fetching Ollama models:', error);
      setOllamaModels([{ name: 'qwen3:30b-a3b-q8_0' }, { name: 'gemma3' }]); // Fallback models
    } finally {
      setLoading(false);
    }
  };

  const handleSetting = (key, value) => {
    set(`graphiti.${key}`, value);
    
    // Immediate validation feedback
    setValidationState(prev => ({
      ...prev,
      [key]: validateSetting(key, value)
    }));
  };

  // Get status indicator for a setting
  const getValidationIcon = (key) => {
    const status = validationState[key];
    switch (status) {
      case 'valid': 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#00973a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginLeft: '4px', display: 'inline-block', verticalAlign: 'middle'}}>
            <polyline points="20 6 9 17 4 12"/>
          </svg>
        );
      case 'warning': 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginLeft: '4px', display: 'inline-block', verticalAlign: 'middle'}}>
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
            <line x1="12" y1="9" x2="12" y2="13"/>
            <line x1="12" y1="17" x2="12.01" y2="17"/>
          </svg>
        );
      case 'invalid': 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#ef4444" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginLeft: '4px', display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
        );
      default: return '';
    }
  };

  // Get validation class for styling
  const getValidationClass = (key) => {
    const status = validationState[key];
    return status ? `validation-${status}` : '';
  };

  // Get connection status icon
  const getConnectionIcon = () => {
    switch (connectionStatus.ollama) {
      case 'testing': 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{display: 'inline-block', verticalAlign: 'middle', animation: 'spin 1s linear infinite'}}>
            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
            <path d="M21 3v5h-5"/>
            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
            <path d="M8 16H3v5"/>
          </svg>
        );
      case 'success': 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="#00973a" style={{display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
      case 'error': 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="#ef4444" style={{display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
      default: 
        return (
          <svg width="12" height="12" viewBox="0 0 24 24" fill="#9ca3af" style={{display: 'inline-block', verticalAlign: 'middle'}}>
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
    }
  };

  // Overall configuration health
  const getOverallHealth = () => {
    const validationValues = Object.values(validationState);
    if (validationValues.includes('invalid')) return 'error';
    if (validationValues.includes('warning')) return 'warning';
    return 'healthy';
  };

  // Apply a configuration preset
  const applyPreset = (presetKey) => {
    const preset = configPresets[presetKey];
    if (!preset) return;
    
    Object.entries(preset.settings).forEach(([key, value]) => {
      handleSetting(key, value);
    });
  };

  // Detect current preset (if any)
  const getCurrentPreset = () => {
    for (const [key, preset] of Object.entries(configPresets)) {
      const isMatch = Object.entries(preset.settings).every(([settingKey, settingValue]) => {
        const currentValue = graphitiSettings[settingKey];
        return currentValue === settingValue;
      });
      if (isMatch) return key;
    }
    return 'custom';
  };

  // Quick settings toggles with SVG icons
  const quickToggles = [
    {
      key: 'enableFallback',
      label: 'Fallback',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
          <path d="M21 3v5h-5"/>
          <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
          <path d="M8 16H3v5"/>
        </svg>
      ),
      description: 'Enable fallback to simple search'
    },
    {
      key: 'enableCaching',
      label: 'Cache',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
      ),
      description: 'Enable response caching'
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="graphiti-config-overlay">
      <div className="graphiti-config-panel">
        <div className="config-header">
          <div className="header-content">
            <h3>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px', display: 'inline-block', verticalAlign: 'middle'}}>
                <circle cx="11" cy="11" r="8"/>
                <path d="21 21l-4.35-4.35"/>
              </svg>
              Graphiti Search Configuration
            </h3>
            <div className="config-health">
              <span className={`health-indicator health-${getOverallHealth()}`}>
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" style={{display: 'inline-block', verticalAlign: 'middle'}}>
                  {getOverallHealth() === 'healthy' ? (
                    <circle cx="12" cy="12" r="10" fill="#00973a"/>
                  ) : getOverallHealth() === 'warning' ? (
                    <circle cx="12" cy="12" r="10" fill="#f59e0b"/>
                  ) : (
                    <circle cx="12" cy="12" r="10" fill="#ef4444"/>
                  )}
                </svg>
              </span>
              <span className="health-text">
                {getOverallHealth() === 'healthy' ? 'Optimal' : 
                 getOverallHealth() === 'warning' ? 'Needs Attention' : 'Invalid Settings'}
              </span>
            </div>
          </div>
          <button className="close-button" onClick={onClose}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <div className="config-content">
          {/* Configuration Presets */}
          <div className="config-section">
            <label className="config-label">
              Quick Presets
              <span className="current-preset">
                (Current: {configPresets[getCurrentPreset()]?.name || (
                  <span style={{display: 'inline-flex', alignItems: 'center', gap: '2px'}}>
                    <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                    Custom
                  </span>
                )})
              </span>
            </label>
            <div className="preset-grid">
              {Object.entries(configPresets).map(([key, preset]) => (
                <button
                  key={key}
                  onClick={() => applyPreset(key)}
                  className={`preset-button ${getCurrentPreset() === key ? 'active' : ''}`}
                  title={preset.description}
                >
                  <div style={{display: 'flex', alignItems: 'center', gap: '6px', marginBottom: '4px'}}>
                    {preset.icon}
                    <span className="preset-name">{preset.name}</span>
                  </div>
                  <span className="preset-desc">{preset.description}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Quick Toggles */}
          <div className="config-section">
            <label className="config-label">Quick Settings</label>
            <div className="quick-toggles">
              {quickToggles.map(toggle => (
                <button
                  key={toggle.key}
                  onClick={() => handleSetting(toggle.key, !graphitiSettings[toggle.key])}
                  className={`quick-toggle ${graphitiSettings[toggle.key] !== false ? 'active' : ''}`}
                  title={toggle.description}
                >
                  <span className="toggle-icon">{toggle.icon}</span>
                  <span className="toggle-label">{toggle.label}</span>
                  <span className="toggle-status">
                    {graphitiSettings[toggle.key] !== false ? (
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="20 6 9 17 4 12"/>
                      </svg>
                    ) : (
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="6" y="4" width="4" height="16"/>
                        <rect x="14" y="4" width="4" height="16"/>
                      </svg>
                    )}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Search Type Selection */}
          <div className="config-section">
            <label className="config-label">
              Search Type {getValidationIcon('searchType')}
            </label>
            <select
              value={graphitiSettings.searchType || 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER'}
              onChange={(e) => handleSetting('searchType', e.target.value)}
              className={`config-select ${getValidationClass('searchType')}`}
            >
              {searchTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            <p className="config-hint">
              Cross-Encoder optimizes for precision, MMR for diversity, RRF for relevance
            </p>
          </div>

          {/* LLM Provider Selection */}
          <div className="config-section">
            <label className="config-label">LLM Provider</label>
            <div className="radio-group">
              {llmProviders.map(provider => (
                <label key={provider.value} className="radio-label">
                  <input
                    type="radio"
                    name="llmProvider"
                    value={provider.value}
                    checked={graphitiSettings.llmProvider === provider.value}
                    onChange={(e) => handleSetting('llmProvider', e.target.value)}
                  />
                  <span>{provider.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Ollama Model Selection (Response Generation) */}
          {graphitiSettings.llmProvider === 'ollama' && (
            <div className="config-section">
              <label className="config-label">
                Ollama Model (Response) {getValidationIcon('ollamaModel')}
              </label>
              <select
                value={graphitiSettings.ollamaModel || 'qwen3:30b-a3b-q8_0'}
                onChange={(e) => handleSetting('ollamaModel', e.target.value)}
                className={`config-select ${getValidationClass('ollamaModel')}`}
                disabled={loading}
              >
                {loading ? (
                  <option>Loading models...</option>
                ) : ollamaModels.length > 0 ? (
                  ollamaModels.map(model => (
                    <option key={model.name} value={model.name}>
                      {model.name}
                    </option>
                  ))
                ) : (
                  <option value="qwen3:30b-a3b-q8_0">qwen3:30b-a3b-q8_0 (default)</option>
                )}
              </select>
              <p className="config-hint">Model used for final answer generation</p>
              {loading && <p className="config-hint">Fetching available models...</p>}
            </div>
          )}

          {/* Graphiti Model Selection (Internal Operations) */}
          {graphitiSettings.llmProvider === 'ollama' && (
            <div className="config-section">
              <label className="config-label">
                Graphiti Model (Internal) {getValidationIcon('graphitiModel')}
              </label>
              <select
                value={graphitiSettings.graphitiModel || 'gemma3:latest'}
                onChange={(e) => handleSetting('graphitiModel', e.target.value)}
                className={`config-select ${getValidationClass('graphitiModel')}`}
                disabled={loading}
              >
                {loading ? (
                  <option>Loading models...</option>
                ) : ollamaModels.length > 0 ? (
                  ollamaModels.map(model => (
                    <option key={model.name} value={model.name}>
                      {model.name}
                    </option>
                  ))
                ) : (
                  <option value="gemma3:latest">gemma3:latest (default)</option>
                )}
              </select>
              <p className="config-hint">Model used for knowledge graph operations and search</p>
              {loading && <p className="config-hint">Fetching available models...</p>}
            </div>
          )}

          {/* Diversity Factor (MMR only) */}
          {graphitiSettings.searchType === 'COMBINED_HYBRID_SEARCH_MMR' && (
            <div className="config-section">
              <label className="config-label">
                Diversity Factor: {graphitiSettings.diversityFactor || 0.3} {getValidationIcon('diversityFactor')}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={graphitiSettings.diversityFactor || 0.3}
                onChange={(e) => handleSetting('diversityFactor', parseFloat(e.target.value))}
                className={`config-slider ${getValidationClass('diversityFactor')}`}
              />
              <div className="slider-labels">
                <span>Relevance</span>
                <span>Diversity</span>
              </div>
              <p className="config-hint">
                Balance between answer relevance (0.0) and result diversity (1.0)
              </p>
            </div>
          )}

          {/* Result Count Configuration */}
          <div className="config-section">
            <label className="config-label">Search Results</label>
            <div className="number-inputs">
              <div className="number-input-group">
                <label>Edges: {getValidationIcon('edgeCount')}</label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={graphitiSettings.edgeCount || 6}
                  onChange={(e) => handleSetting('edgeCount', parseInt(e.target.value))}
                  className={`config-number ${getValidationClass('edgeCount')}`}
                />
              </div>
              <div className="number-input-group">
                <label>Nodes: {getValidationIcon('nodeCount')}</label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={graphitiSettings.nodeCount || 2}
                  onChange={(e) => handleSetting('nodeCount', parseInt(e.target.value))}
                  className={`config-number ${getValidationClass('nodeCount')}`}
                />
              </div>
            </div>
            <p className="config-hint">
              Edges provide specific facts, nodes provide entity summaries
            </p>
          </div>

          {/* LLM Parameters */}
          <div className="config-section">
            <label className="config-label">
              LLM Temperature: {graphitiSettings.temperature || 0.3} {getValidationIcon('temperature')}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={graphitiSettings.temperature || 0.3}
              onChange={(e) => handleSetting('temperature', parseFloat(e.target.value))}
              className={`config-slider ${getValidationClass('temperature')}`}
            />
            <div className="slider-labels">
              <span>Focused</span>
              <span>Creative</span>
            </div>
            <p className="config-hint">
              Controls response creativity: lower = more focused, higher = more creative
            </p>
          </div>

          {/* Ollama URL Configuration */}
          {graphitiSettings.llmProvider === 'ollama' && (
            <div className="config-section">
              <label className="config-label">
                Ollama URL {getValidationIcon('ollamaUrl')}
                <span className="connection-status">
                  {getConnectionIcon()}
                  {connectionStatus.lastTest && (
                    <span className="test-time">Last test: {connectionStatus.lastTest}</span>
                  )}
                </span>
              </label>
              <div className="url-input-group">
                <input
                  type="url"
                  value={graphitiSettings.ollamaUrl || 'http://localhost:11434'}
                  onChange={(e) => handleSetting('ollamaUrl', e.target.value)}
                  className={`config-input ${getValidationClass('ollamaUrl')}`}
                  placeholder="http://localhost:11434"
                />
                <button
                  type="button"
                  onClick={() => testOllamaConnection(graphitiSettings.ollamaUrl)}
                  className="test-connection-btn"
                  disabled={connectionStatus.ollama === 'testing'}
                >
                  {connectionStatus.ollama === 'testing' ? 'Testing...' : 'Test'}
                </button>
              </div>
              <p className="config-hint">
                URL of your Ollama server (default: http://localhost:11434)
                {connectionStatus.ollama === 'success' && (
                  <span className="success-hint"> ✅ Connection successful</span>
                )}
                {connectionStatus.ollama === 'error' && (
                  <span className="error-hint"> ❌ Connection failed</span>
                )}
              </p>
            </div>
          )}

          {/* Advanced Settings */}
          <div className="config-section">
            <label className="config-label">Advanced Settings</label>
            <div className="number-inputs">
              <div className="number-input-group">
                <label>Timeout (seconds): {getValidationIcon('timeout')}</label>
                <input
                  type="number"
                  min="30"
                  max="600"
                  value={graphitiSettings.timeout || 180}
                  onChange={(e) => handleSetting('timeout', parseInt(e.target.value))}
                  className={`config-number ${getValidationClass('timeout')}`}
                />
              </div>
            </div>
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={graphitiSettings.enableFallback !== false}
                  onChange={(e) => handleSetting('enableFallback', e.target.checked)}
                />
                <span>Enable fallback to simple search</span>
              </label>
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={graphitiSettings.enableCaching !== false}
                  onChange={(e) => handleSetting('enableCaching', e.target.checked)}
                />
                <span>Enable response caching</span>
              </label>
            </div>
          </div>
        </div>

        <div className="config-footer">
          <div className="config-actions">
            {/* <button 
              className="metrics-button"
              onClick={() => setShowPerformanceMetrics(true)}
              title="View performance metrics and optimization recommendations"
            >
              📊 Performance
            </button> */}
            <button 
              className="reset-button"
              onClick={() => {
                // Reset to defaults
                const defaultGraphiti = {
                  searchType: 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
                  diversityFactor: 0.3,
                  edgeCount: 6,
                  nodeCount: 2,
                  llmProvider: 'ollama',
                  ollamaModel: 'qwen3:30b-a3b-q8_0',
                  graphitiModel: 'gemma3:latest',
                  ollamaUrl: 'http://localhost:11434',
                  temperature: 0.3,
                  timeout: 180,
                  enableFallback: true,
                  enableCaching: true
                };
                Object.entries(defaultGraphiti).forEach(([key, value]) => {
                  handleSetting(key, value);
                });
              }}
            >
              Reset to Defaults
            </button>
            <button className="apply-button" onClick={onClose}>
              Apply Settings
            </button>
          </div>
          <div className="config-status-enhanced">
            <div className="status-row">
              <span className="status-label">Configuration:</span>
              <span className={`status-value status-${getOverallHealth()}`}>
                {graphitiSettings.searchType?.replace(/_/g, ' ')} • {graphitiSettings.llmProvider}
                {graphitiSettings.llmProvider === 'ollama' ? ` (${graphitiSettings.ollamaModel})` : ''} • 
                {graphitiSettings.edgeCount || 6}E/{graphitiSettings.nodeCount || 2}N
              </span>
            </div>
            {graphitiSettings.llmProvider === 'ollama' && (
              <div className="status-row">
                <span className="status-label">Connection:</span>
                <span className={`status-value connection-${connectionStatus.ollama || 'unknown'}`}>
                  {connectionStatus.ollama === 'success' ? (
                    <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#00973a" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="20 6 9 17 4 12"/>
                      </svg>
                      Connected
                    </span>
                  ) : connectionStatus.ollama === 'error' ? (
                    <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#ef4444" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                      </svg>
                      Failed
                    </span>
                  ) : connectionStatus.ollama === 'testing' ? (
                    <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{animation: 'spin 1s linear infinite'}}>
                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                        <path d="M21 3v5h-5"/>
                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                        <path d="M8 16H3v5"/>
                      </svg>
                      Testing...
                    </span>
                  ) : (
                    <span style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="#9ca3af">
                        <circle cx="12" cy="12" r="10"/>
                      </svg>
                      Not tested
                    </span>
                  )}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Performance Metrics Panel - Removed during revert */}
      {/* <PerformanceMetrics 
        isOpen={showPerformanceMetrics}
        onClose={() => setShowPerformanceMetrics(false)}
      /> */}
    </div>
  );
};

export default GraphitiConfigPanel;