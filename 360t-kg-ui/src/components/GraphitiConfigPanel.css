.graphiti-config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.graphiti-config-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-health {
  display: flex;
  align-items: center;
  gap: 8px;
}

.health-indicator {
  font-size: 14px;
}

.health-text {
  font-size: 12px;
  font-weight: 500;
}

.health-text.health-healthy {
  color: #059669;
}

.health-text.health-warning {
  color: #d97706;
}

.health-text.health-error {
  color: #dc2626;
}

.config-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.config-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.config-section {
  margin-bottom: 24px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.config-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  transition: border-color 0.2s;
}

.config-select:focus {
  outline: none;
  border-color: #00973a;
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.config-select:disabled {
  background: #f3f4f6;
  color: #6b7280;
}

/* Custom Radio button styles */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  transition: all 0.2s ease;
  position: relative;
}

.radio-label:hover {
  background: #f9fafb;
  border-color: #00973a;
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.1);
}

.radio-label input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 0;
  height: 0;
}

/* Custom radio button appearance */
.radio-label::before {
  content: '';
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  background: #ffffff;
  transition: all 0.2s ease;
  flex-shrink: 0;
  position: relative;
}

.radio-label:hover::before {
  border-color: #00973a;
}

/* Checked state */
.radio-label input[type="radio"]:checked + span {
  color: #00973a;
  font-weight: 600;
}

.radio-label:has(input[type="radio"]:checked) {
  background: #f0fdf4;
  border-color: #00973a;
  box-shadow: 0 0 0 1px rgba(0, 151, 58, 0.1);
}

.radio-label:has(input[type="radio"]:checked)::before {
  border-color: #00973a;
  background: #00973a;
  box-shadow: inset 0 0 0 3px #ffffff;
}

.radio-label span {
  font-size: 14px;
  color: #374151;
  transition: all 0.2s ease;
}

.number-inputs {
  display: flex;
  gap: 16px;
}

.number-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.number-input-group label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
}

.config-number {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  transition: border-color 0.2s;
}

.config-number:focus {
  outline: none;
  border-color: #00973a;
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.config-hint {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

/* Slider Styles */
.config-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
  margin: 8px 0;
}

.config-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #00973a;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.config-slider::-webkit-slider-thumb:hover {
  background: #007d30;
  transform: scale(1.1);
}

.config-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #00973a;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.config-slider::-moz-range-thumb:hover {
  background: #007d30;
  transform: scale(1.1);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #6b7280;
  margin-top: 4px;
}

/* Input Styles */
.config-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  transition: border-color 0.2s;
}

.config-input:focus {
  outline: none;
  border-color: #00973a;
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

/* Checkbox Styles */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 0;
  font-size: 14px;
  color: #374151;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #00973a;
  cursor: pointer;
}

/* Button Styles */
.config-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.reset-button, .apply-button, .metrics-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid;
}

.metrics-button {
  background: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

.metrics-button:hover {
  background: #d97706;
  border-color: #d97706;
}

.reset-button {
  background: #f3f4f6;
  border-color: #d1d5db;
  color: #374151;
}

.reset-button:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.apply-button {
  background: #00973a;
  border-color: #00973a;
  color: white;
  flex: 1;
}

.apply-button:hover {
  background: #007d30;
  border-color: #007d30;
}

.config-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.config-status {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  font-style: italic;
}

/* Validation states */
.validation-valid {
  border-color: #00973a !important;
}

.validation-warning {
  border-color: #f59e0b !important;
}

.validation-invalid {
  border-color: #ef4444 !important;
}

.validation-valid:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

.validation-warning:focus {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1) !important;
}

.validation-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Connection status */
.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
  font-size: 12px;
  color: #6b7280;
}

.test-time {
  font-size: 11px;
  color: #9ca3af;
}

/* URL input with test button */
.url-input-group {
  display: flex;
  gap: 8px;
  align-items: stretch;
}

.url-input-group .config-input {
  flex: 1;
}

.test-connection-btn {
  padding: 10px 16px;
  background: #00973a;
  color: white;
  border: 1px solid #00973a;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.test-connection-btn:hover:not(:disabled) {
  background: #007d30;
  border-color: #007d30;
}

.test-connection-btn:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
}

/* Enhanced status hints */
.success-hint {
  color: #059669;
  font-weight: 500;
}

.error-hint {
  color: #dc2626;
  font-weight: 500;
}

/* Enhanced status footer */
.config-status-enhanced {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.status-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.status-label {
  font-weight: 600;
  color: #374151;
  min-width: 80px;
}

.status-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f3f4f6;
}

.status-healthy {
  color: #059669;
  background: #ecfdf5;
}

.status-warning {
  color: #d97706;
  background: #fffbeb;
}

.status-error {
  color: #dc2626;
  background: #fef2f2;
}

.connection-success {
  color: #059669;
  background: #ecfdf5;
}

.connection-error {
  color: #dc2626;
  background: #fef2f2;
}

.connection-testing {
  color: #2563eb;
  background: #eff6ff;
}

.connection-unknown {
  color: #6b7280;
  background: #f9fafb;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .graphiti-config-panel {
    background: #1f2937;
    color: #f9fafb;
  }

  .config-header {
    background: #111827;
    border-color: #374151;
  }

  .config-header h3 {
    color: #f9fafb;
  }

  .close-button {
    color: #9ca3af;
  }

  .close-button:hover {
    background: #374151;
    color: #d1d5db;
  }

  .config-label {
    color: #d1d5db;
  }

  .config-select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .config-select:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .config-select:disabled {
    background: #4b5563;
    color: #9ca3af;
  }

  .radio-label {
    background: #374151;
    border-color: #4b5563;
  }

  .radio-label:hover {
    background: #4b5563;
    border-color: #00973a;
    box-shadow: 0 2px 4px rgba(0, 151, 58, 0.1);
  }

  .radio-label::before {
    background: #374151;
    border-color: #6b7280;
  }

  .radio-label:hover::before {
    border-color: #00973a;
  }

  .radio-label:has(input[type="radio"]:checked) {
    background: #064e3b;
    border-color: #00973a;
    box-shadow: 0 0 0 1px rgba(0, 151, 58, 0.1);
  }

  .radio-label:has(input[type="radio"]:checked)::before {
    border-color: #00973a;
    background: #00973a;
    box-shadow: inset 0 0 0 3px #374151;
  }

  .radio-label input[type="radio"]:checked + span {
    color: #00973a;
  }

  .radio-label span {
    color: #d1d5db;
  }

  .number-input-group label {
    color: #9ca3af;
  }

  .config-number {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .config-number:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .config-hint {
    color: #9ca3af;
  }

  .config-footer {
    background: #111827;
    border-color: #374151;
  }

  .config-status {
    color: #9ca3af;
  }

  /* Dark mode validation states */
  .validation-valid {
    border-color: #00973a !important;
  }

  .validation-warning {
    border-color: #f59e0b !important;
  }

  .validation-invalid {
    border-color: #ef4444 !important;
  }

  /* Dark mode connection status */
  .connection-status {
    color: #9ca3af;
  }

  .test-time {
    color: #6b7280;
  }

  /* Dark mode test button */
  .test-connection-btn {
    background: #3b82f6;
    border-color: #3b82f6;
  }

  .test-connection-btn:hover:not(:disabled) {
    background: #2563eb;
    border-color: #2563eb;
  }

  .test-connection-btn:disabled {
    background: #6b7280;
    border-color: #6b7280;
  }

  /* Dark mode enhanced status */
  .status-label {
    color: #d1d5db;
  }

  .status-value {
    background: #374151;
    color: #d1d5db;
  }

  .status-healthy {
    color: #00973a;
    background: #064e3b;
  }

  .status-warning {
    color: #f59e0b;
    background: #451a03;
  }

  .status-error {
    color: #ef4444;
    background: #450a0a;
  }

  .connection-success {
    color: #00973a;
    background: #064e3b;
  }

  .connection-error {
    color: #ef4444;
    background: #450a0a;
  }

  .connection-testing {
    color: #60a5fa;
    background: #1e3a8a;
  }

  .connection-unknown {
    color: #9ca3af;
    background: #374151;
  }

  /* Dark mode header content */
  .header-content h3 {
    color: #f9fafb;
  }

  .health-text {
    color: #d1d5db;
  }

  /* Dark mode slider */
  .config-slider {
    background: #4b5563;
  }

  .config-slider::-webkit-slider-thumb {
    background: #60a5fa;
  }

  .config-slider::-webkit-slider-thumb:hover {
    background: #3b82f6;
  }

  .config-slider::-moz-range-thumb {
    background: #60a5fa;
  }

  .config-slider::-moz-range-thumb:hover {
    background: #3b82f6;
  }

  .slider-labels {
    color: #9ca3af;
  }

  /* Dark mode inputs */
  .config-input {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .config-input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  /* Dark mode checkboxes */
  .checkbox-label {
    color: #d1d5db;
  }

  .checkbox-label input[type="checkbox"] {
    accent-color: #60a5fa;
  }

  /* Dark mode buttons */
  .metrics-button {
    background: #f59e0b;
    border-color: #f59e0b;
  }

  .metrics-button:hover {
    background: #d97706;
    border-color: #d97706;
  }

  .reset-button {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;
  }

  .reset-button:hover {
    background: #4b5563;
    border-color: #6b7280;
  }

  .apply-button {
    background: #3b82f6;
    border-color: #3b82f6;
  }

  .apply-button:hover {
    background: #2563eb;
    border-color: #2563eb;
  }

  /* Dark mode presets */
  .current-preset {
    color: #9ca3af;
  }

  .preset-button {
    background: #374151;
    border-color: #4b5563;
  }

  .preset-button:hover {
    border-color: #60a5fa;
  }

  .preset-button.active {
    border-color: #60a5fa;
    background: #1e3a8a;
  }

  .preset-name {
    color: #d1d5db;
  }

  .preset-desc {
    color: #9ca3af;
  }

  .preset-button.active .preset-name {
    color: #60a5fa;
  }

  .preset-button.active .preset-desc {
    color: #93c5fd;
  }

  /* Dark mode quick toggles */
  .quick-toggle {
    background: #374151;
    border-color: #4b5563;
  }

  .quick-toggle:hover {
    border-color: #6b7280;
    background: #4b5563;
  }

  .quick-toggle.active {
    border-color: #00973a;
    background: #064e3b;
  }

  .toggle-label {
    color: #d1d5db;
  }

  .quick-toggle.active .toggle-label {
    color: #00973a;
  }
}

/* Configuration Presets */
.current-preset {
  font-weight: 400;
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.preset-button {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.preset-button:hover {
  border-color: #00973a;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.preset-button.active {
  border-color: #00973a;
  background: #f0fdf4;
  box-shadow: 0 0 0 1px rgba(0, 151, 58, 0.1);
}

.preset-name {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.preset-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.3;
}

.preset-button.active .preset-name {
  color: #00973a;
}

.preset-button.active .preset-desc {
  color: #00973a;
}

/* Quick Toggles */
.quick-toggles {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
}

.quick-toggle:hover {
  border-color: #9ca3af;
  background: #f9fafb;
}

.quick-toggle.active {
  border-color: #00973a;
  background: #ecfdf5;
}

.toggle-icon {
  font-size: 14px;
}

.toggle-label {
  font-weight: 500;
  color: #374151;
}

.toggle-status {
  font-size: 12px;
}

.quick-toggle.active .toggle-label {
  color: #059669;
}

/* Animation for spinning icons */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}