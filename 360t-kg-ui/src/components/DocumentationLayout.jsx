import React, { useState, useEffect } from 'react';
import DocumentationSidebar from './DocumentationSidebar';
import DocumentationReader from './DocumentationReader';
import '../styles/DocumentationLayout.css';

const DocumentationLayout = ({ 
  expandedDoc, 
  onDocSelect, 
  docContent, 
  docLoading, 
  docError, 
  updateURL 
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setSidebarCollapsed(true);
      }
    };

    // Set initial state based on screen size
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Toggle sidebar with Ctrl/Cmd + B
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        setSidebarCollapsed(prev => !prev);
      }
      
      // Close documentation with Escape
      if (event.key === 'Escape' && expandedDoc) {
        handleCloseDoc();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [expandedDoc]);

  const handleToggleSidebar = () => {
    setSidebarCollapsed(prev => !prev);
  };

  const handleDocSelect = (docKey) => {
    onDocSelect(docKey);
    updateURL({ view: 'documentation', expandedDoc: docKey });
    
    // On mobile, collapse sidebar after selection
    if (window.innerWidth <= 768) {
      setSidebarCollapsed(true);
    }
  };

  const handleCloseDoc = () => {
    onDocSelect(null);
    updateURL({ view: 'documentation' });
  };

  return (
    <div className="documentation-layout">
      {/* Overlay for mobile when sidebar is open */}
      {!sidebarCollapsed && window.innerWidth <= 768 && (
        <div 
          className="sidebar-overlay"
          onClick={() => setSidebarCollapsed(true)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <DocumentationSidebar
        expandedDoc={expandedDoc}
        onDocSelect={handleDocSelect}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={handleToggleSidebar}
      />

      {/* Main Content Area */}
      <div className="documentation-main">
        <DocumentationReader
          expandedDoc={expandedDoc}
          docContent={docContent}
          docLoading={docLoading}
          docError={docError}
          onClose={handleCloseDoc}
        />
      </div>

      {/* Keyboard shortcuts help (hidden, for screen readers) */}
      <div className="sr-only" aria-live="polite">
        <p>Keyboard shortcuts: Press Ctrl+B to toggle sidebar, Escape to close documentation</p>
      </div>
    </div>
  );
};

export default DocumentationLayout;
