import React, { useState, useEffect, useRef } from 'react';
import '../styles/DocumentationReader.css';

const DocumentationReader = ({
  expandedDoc,
  docContent,
  docLoading,
  docError,
  onClose
}) => {
  const [tableOfContents, setTableOfContents] = useState([]);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [tocCollapsed, setTocCollapsed] = useState(false);
  const contentRef = useRef(null);
  const readerRef = useRef(null);

  // Generate table of contents from headings
  useEffect(() => {
    if (docContent && contentRef.current) {
      const headings = contentRef.current.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const toc = Array.from(headings).map((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;
        return {
          id,
          text: heading.textContent,
          level: parseInt(heading.tagName.charAt(1)),
          element: heading
        };
      });
      setTableOfContents(toc);
    }
  }, [docContent]);

  // Handle scroll for back-to-top button
  useEffect(() => {
    const handleScroll = () => {
      if (readerRef.current) {
        const scrollTop = readerRef.current.scrollTop;
        setShowBackToTop(scrollTop > 300);
      }
    };

    const readerElement = readerRef.current;
    if (readerElement) {
      readerElement.addEventListener('scroll', handleScroll);
      return () => readerElement.removeEventListener('scroll', handleScroll);
    }
  }, []);

  const scrollToHeading = (headingId) => {
    const element = document.getElementById(headingId);
    if (element && readerRef.current) {
      const offsetTop = element.offsetTop - 80; // Account for header
      readerRef.current.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  };

  const scrollToTop = () => {
    if (readerRef.current) {
      readerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  const formatDocTitle = (docKey) => {
    if (!docKey) return '';
    return docKey
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  if (!expandedDoc) {
    return (
      <div className="documentation-reader empty">
        <div className="empty-state">
          <div className="empty-icon">📚</div>
          <h2>Welcome to Documentation</h2>
          <p>Select a topic from the sidebar to get started</p>
        </div>
      </div>
    );
  }

  return (
    <div className="documentation-reader">
      {/* Header with breadcrumb and close button */}
      <div className="reader-header">
        <div className="breadcrumb">
          <span className="breadcrumb-item">Documentation</span>
          <span className="breadcrumb-separator">›</span>
          <span className="breadcrumb-item current">{formatDocTitle(expandedDoc)}</span>
        </div>
        <button 
          className="close-button"
          onClick={onClose}
          aria-label="Close documentation"
        >
          ×
        </button>
      </div>

      {/* Table of Contents */}
      {tableOfContents.length > 0 && (
        <div className={`table-of-contents ${tocCollapsed ? 'collapsed' : ''}`}>
          <div className="toc-header">
            <h3>Contents</h3>
            <button
              className="toc-toggle"
              onClick={() => setTocCollapsed(!tocCollapsed)}
              aria-label={tocCollapsed ? 'Expand table of contents' : 'Collapse table of contents'}
            >
              {tocCollapsed ? '▼' : '▲'}
            </button>
          </div>
          {!tocCollapsed && (
            <nav className="toc-nav">
              {tableOfContents.slice(0, 8).map((item) => (
                <button
                  key={item.id}
                  className={`toc-item level-${item.level}`}
                  onClick={() => scrollToHeading(item.id)}
                >
                  {item.text}
                </button>
              ))}
              {tableOfContents.length > 8 && (
                <div className="toc-more">
                  +{tableOfContents.length - 8} more sections
                </div>
              )}
            </nav>
          )}
        </div>
      )}

      {/* Content Area */}
      <div className="reader-content" ref={readerRef}>
        {docLoading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading documentation...</p>
          </div>
        ) : docError ? (
          <div className="error-state">
            <div className="error-icon">⚠️</div>
            <h3>Error Loading Documentation</h3>
            <p>{docError}</p>
            <button className="retry-button" onClick={() => window.location.reload()}>
              Try Again
            </button>
          </div>
        ) : (
          <div 
            className="content-body markdown-body"
            ref={contentRef}
            dangerouslySetInnerHTML={{ __html: docContent }}
          />
        )}
      </div>

      {/* Back to Top Button */}
      {showBackToTop && (
        <button 
          className="back-to-top"
          onClick={scrollToTop}
          aria-label="Back to top"
        >
          ↑
        </button>
      )}
    </div>
  );
};

export default DocumentationReader;
