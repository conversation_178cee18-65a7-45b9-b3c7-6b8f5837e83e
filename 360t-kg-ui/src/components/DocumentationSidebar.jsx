import React from 'react';
import '../styles/DocumentationSidebar.css';

const DocumentationSidebar = ({ 
  expandedDoc, 
  onDocSelect, 
  isCollapsed, 
  onToggleCollapse 
}) => {
  const documentationCategories = {
    'getting-started': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/><polyline points="10,17 15,12 10,7"/><line x1="15" y1="12" x2="3" y2="12"/></svg>, 
      title: 'Getting Started', 
      desc: 'Quick introduction and setup guide' 
    },
    'user-guide': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/></svg>, 
      title: 'User Guide', 
      desc: 'Complete guide for using the Knowledge Graph' 
    },
    'data-model': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/></svg>, 
      title: 'Data Model', 
      desc: 'Understanding the graph structure' 
    },
    'api-reference': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/><path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/><path d="M13 12h3a2 2 0 0 1 2 2v1"/><path d="M11 12H8a2 2 0 0 0-2 2v1"/></svg>, 
      title: 'API Reference', 
      desc: 'API endpoints and usage' 
    },
    'analytics-guide': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><line x1="18" y1="20" x2="18" y2="10"/><line x1="12" y1="20" x2="12" y2="4"/><line x1="6" y1="20" x2="6" y2="14"/></svg>, 
      title: 'Analytics Guide', 
      desc: 'Using analysis tools and features' 
    },
    'query-guide': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><circle cx="11" cy="11" r="8"/><path d="M21 21l-4.35-4.35"/></svg>, 
      title: 'Query Guide', 
      desc: 'Writing and executing queries' 
    },
    'visualization': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>, 
      title: 'Visualization', 
      desc: 'Graph visualization features' 
    },
    'development': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><polyline points="16,18 22,12 16,6"/><polyline points="8,6 2,12 8,18"/></svg>, 
      title: 'Development', 
      desc: 'Developer documentation' 
    },
    'administration': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><circle cx="12" cy="12" r="3"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/></svg>, 
      title: 'Administration', 
      desc: 'System administration guide' 
    },
    'troubleshooting': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>, 
      title: 'Troubleshooting', 
      desc: 'Common issues and solutions' 
    },
    'monitoring-guide': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>, 
      title: 'Monitoring', 
      desc: 'System monitoring guide' 
    },
    'validation-guide': { 
      icon: <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M9 12l2 2 4-4"/><path d="M20 6L9 17l-5-5"/></svg>, 
      title: 'Validation', 
      desc: 'Data validation guidelines' 
    }
  };

  return (
    <div className={`documentation-sidebar ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <button 
          className="sidebar-toggle"
          onClick={onToggleCollapse}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? '→' : '←'}
        </button>
        {!isCollapsed && (
          <h2 className="sidebar-title">Documentation</h2>
        )}
      </div>

      <nav className="sidebar-nav" role="navigation" aria-label="Documentation navigation">
        {Object.entries(documentationCategories).map(([key, { icon, title, desc }]) => (
          <button
            key={key}
            className={`nav-item ${expandedDoc === key ? 'active' : ''}`}
            onClick={() => onDocSelect(key)}
            aria-current={expandedDoc === key ? 'page' : undefined}
            title={isCollapsed ? `${title}: ${desc}` : undefined}
          >
            <span className="nav-icon" aria-hidden="true">{icon}</span>
            {!isCollapsed && (
              <div className="nav-content">
                <span className="nav-title">{title}</span>
                <span className="nav-desc">{desc}</span>
              </div>
            )}
          </button>
        ))}
      </nav>

      {!isCollapsed && (
        <div className="sidebar-footer">
          <div className="sidebar-info">
            <span className="info-text">360T Knowledge Graph</span>
            <span className="info-version">v2.0</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentationSidebar;
