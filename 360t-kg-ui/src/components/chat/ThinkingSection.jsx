import React, { useState } from 'react';
import './ThinkingSection.css';

/**
 * ThinkingSection component for displaying collapsible LLM thinking process
 * Features:
 * - Collapsible/expandable thinking content
 * - Default collapsed state with "Show Thinking" toggle
 * - Distinct visual styling (different background, italic text)
 * - Proper markdown support for thinking content
 * - Accessible keyboard navigation
 */
const ThinkingSection = ({ thinkingContent }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Don't render if no thinking content
  if (!thinkingContent || typeof thinkingContent !== 'string' || thinkingContent.trim() === '') {
    return null;
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleExpanded();
    }
  };

  return (
    <div className="thinking-section">
      <button
        className={`thinking-toggle ${isExpanded ? 'expanded' : ''}`}
        onClick={toggleExpanded}
        onKeyDown={handleKeyDown}
        aria-expanded={isExpanded}
        aria-controls="thinking-content"
        type="button"
      >
        <div className="thinking-header">
          <div className="thinking-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z"/>
              <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z"/>
            </svg>
          </div>
          <span className="thinking-label">
            {isExpanded ? 'Hide Thinking Process' : 'Show Thinking Process'}
          </span>
          <div className="thinking-arrow">
            {isExpanded ? '−' : '+'}
          </div>
        </div>
      </button>
      
      {isExpanded && (
        <div 
          id="thinking-content"
          className="thinking-content"
          role="region"
          aria-label="LLM thinking process"
        >
          <div className="thinking-text">
            {thinkingContent.split('\n').map((line, index) => (
              <p key={index} className="thinking-line">
                {line.trim() === '' ? '\u00A0' : line}
              </p>
            ))}
          </div>
          <div className="thinking-footer">
            <small className="thinking-note">
              This shows the AI's reasoning process before generating the final answer.
            </small>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThinkingSection;