/* Structured Response Component - Modern Green/Grey/Black Design */

.structured-response {
  display: flex;
  flex-direction: column;
  gap: 20px; /* Increased spacing for better separation */
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.5; /* Improved readability */
  max-width: 100%;
  width: 100%;
  background: #FFFFFF; /* Clean white background */
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  color: #1F2937; /* Professional dark grey text */
  overflow-x: hidden; /* Prevent horizontal overflow */
  box-sizing: border-box; /* Include padding and border in width calculation */
}

/* Error State */
.structured-response.error {
  padding: 1rem;
  border: 1px solid #ef4444;
  border-radius: 8px;
  background-color: #fef2f2;
}

.error-message h4 {
  color: #dc2626;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.error-message p {
  color: #7f1d1d;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.error-message details {
  margin-top: 1rem;
}

.error-message summary {
  cursor: pointer;
  color: #dc2626;
  font-weight: 500;
}

.error-message pre {
  background: #f3f4f6;
  padding: 0.75rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

/* Primary Answer - Clean White Design */
.response-answer {
  font-size: 18px;
  line-height: 1.6; /* Improved readability */
  color: #1F2937; /* Dark grey text */
  font-weight: 500;
  padding: 32px;
  background: #FFFFFF; /* Clean white background */
  border-radius: 12px;
  border: 1px solid #E5E7EB; /* Light grey border */
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow-x: auto; /* Handle horizontal overflow with scrolling */
  margin-bottom: 0; /* Remove margin for consistent spacing */
  max-width: 100%;
  min-width: 0; /* Allow flex items to shrink */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Category Badges */
.response-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  margin: 0.25rem 0;
}

/* Explanation Section - Clean Professional Design */
.response-explanation {
  padding: 24px;
  background: #F9FAFB; /* Clean light grey background */
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  font-size: 16px;
  line-height: 1.5;
  color: #1F2937;
}

.explanation-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #16A34A; /* Professional green for headers */
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #E5E7EB;
  cursor: pointer;
}

.explanation-title:hover {
  color: #15803D; /* Darker green on hover */
}

.explanation-section {
  margin-bottom: 1.5rem;
}

.explanation-section:last-child {
  margin-bottom: 0;
}

.explanation-section-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.explanation-content {
  margin-bottom: 1rem;
}

.explanation-entities {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.explanation-entities .entity-refs-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  margin-right: 0.5rem;
}

.explanation-entities .entity-ref-button {
  display: inline-flex;
  align-items: center;
  gap: var(--360t-space-1);
  margin: var(--360t-space-1) var(--360t-space-2) var(--360t-space-1) 0;
  padding: var(--360t-space-2) var(--360t-space-3);
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  color: #065f46;
  border: 1px solid #a7f3d0;
  border-radius: var(--360t-radius-md);
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  font-family: var(--360t-font-family-primary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.explanation-entities .entity-ref-button:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: #6ee7b7;
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
  color: #047857;
}

/* Enhanced Button Interactions with Accessibility */
.entity-ref-button:focus,
.entity-ref-button:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.15);
}

.entity-ref-button:active {
  transform: translateY(0);
  box-shadow: var(--360t-shadow-sm);
}

/* Summary elements accessibility */
.explanation-title:focus,
.sources-title:focus,
.entities-title:focus {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Show more button accessibility */
.show-more-entities:focus,
.show-more-entities:focus-visible {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Ripple Effect */
.entity-ref-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.entity-ref-button:active::before {
  width: 100px;
  height: 100px;
}

/* Enhanced Sections */
.response-sections {
  border: 1px solid var(--360t-mid-gray);
  border-radius: var(--360t-radius-lg);
  overflow: hidden;
  background: linear-gradient(135deg, var(--360t-white) 0%, #fefefe 100%);
  box-shadow: var(--360t-shadow-sm);
  transition: all 0.2s ease;
  margin: var(--360t-space-3) 0;
}

.response-sections:hover {
  border-color: var(--360t-primary);
  box-shadow: var(--360t-shadow-md);
  transform: translateY(-1px);
}

.sections-title {
  margin: 0;
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  padding: var(--360t-space-3);
  background: linear-gradient(135deg, var(--360t-light-gray) 0%, #f1f5f9 100%);
  border-bottom: 1px solid var(--360t-mid-gray);
  letter-spacing: -0.01em;
}

.response-section {
  border-bottom: 1px solid #e5e7eb;
}

.response-section:last-child {
  border-bottom: none;
}

.section-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--360t-space-2) var(--360t-space-3);
  background: var(--360t-white);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.section-header:hover {
  background-color: var(--360t-light-gray);
}

.section-header.expanded {
  background-color: #f3f4f6;
}

.section-title {
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
  font-size: var(--360t-text-sm);
}

.section-toggle {
  font-size: 1.2rem;
  color: #6b7280;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.section-content {
  padding: var(--360t-space-3);
  background-color: #fefefe;
  border-top: 1px solid var(--360t-light-gray);
}

.section-entities {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.entity-refs-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  margin-right: 0.5rem;
}

.entity-ref-button {
  display: inline-flex;
  align-items: center;
  gap: var(--360t-space-1);
  margin: var(--360t-space-1) var(--360t-space-2) var(--360t-space-1) 0;
  padding: var(--360t-space-1) var(--360t-space-3);
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border: 1px solid #93c5fd;
  border-radius: var(--360t-radius-md);
  font-size: var(--360t-text-xs);
  font-weight: var(--360t-font-medium);
  font-family: var(--360t-font-family-primary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  letter-spacing: 0.025em;
  position: relative;
  overflow: hidden;
}

.entity-ref-button:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
  border-color: #60a5fa;
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
  color: #1d4ed8;
}

/* Sources Section - Clean Professional Design */
.response-sources {
  padding: 24px;
  background: #F9FAFB; /* Clean light grey background */
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  font-size: 16px;
  line-height: 1.5;
  color: #1F2937;
}

.sources-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #16A34A; /* Professional green for headers */
  display: flex;
  align-items: center;
  cursor: pointer;
  padding-bottom: 12px;
  border-bottom: 1px solid #E5E7EB;
}

.sources-title:hover {
  color: #15803D; /* Darker green on hover */
}


.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem;
}

/* Entities Section - Clean Professional Design */
.response-entities {
  padding: 24px;
  background: #F9FAFB; /* Clean light grey background */
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  font-size: 16px;
  line-height: 1.5;
  color: #1F2937;
}

.entities-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #16A34A; /* Professional green for headers */
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #E5E7EB;
  cursor: pointer;
}

.entities-title:hover {
  color: #15803D; /* Darker green on hover */
}

.show-more-entities {
  margin-top: 0.5rem;
  padding: 0.375rem 0.75rem;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.show-more-entities:hover {
  background-color: #0284c7;
}

/* Follow-up Questions - Clean Professional Design */
.response-followup {
  padding: 24px;
  background: #F9FAFB; /* Clean light grey background */
  border-radius: 12px;
  border: 1px solid #E5E7EB;
}

.followup-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #16A34A; /* Professional green for headers */
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #E5E7EB;
}


/* Follow-up Questions List */
.followup-questions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.followup-question-link {
  display: block;
  width: 100%;
  text-align: left;
  padding: 12px 16px;
  background: #FFFFFF; /* Clean white background */
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  color: #1F2937;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  text-decoration: none;
  font-family: inherit;
}

.followup-question-link:hover {
  background: #16A34A; /* Green background on hover */
  border-color: #15803D;
  color: #FFFFFF; /* White text on hover */
}

.followup-question-link:focus,
.followup-question-link:focus-visible {
  outline: 2px solid #16A34A;
  outline-offset: 2px;
}

/* Subtle Metadata Section */
.response-metadata {
  margin-top: 24px; /* Separated from main content */
  padding: 16px; /* Minimal padding for metadata */
  background-color: #fafbfc; /* Very subtle background */
  border-radius: var(--360t-radius-lg);
  border: 1px solid #e2e8f0; /* Lighter border */
  font-size: 14px; /* Smaller font for metadata */
  color: #64748b; /* Muted text color */
  opacity: 0.8; /* Reduced prominence */
}

.response-metadata summary {
  cursor: pointer;
  font-weight: 500;
  color: #64748b; /* Muted color */
  font-size: 14px; /* Consistent with metadata */
  letter-spacing: 0.02em; /* Slightly spaced for small text */
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 0.75rem;
  margin-top: 0.75rem;
  align-items: start;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  gap: 0.75rem;
  min-height: 2.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.metadata-item .metadata-label {
  flex-shrink: 0;
  min-width: 120px;
  font-weight: 600;
}

.metadata-item .metadata-value {
  flex-grow: 1;
  text-align: right;
  min-width: 0;
  font-weight: 500;
}

.metadata-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.metadata-value {
  font-size: 0.75rem;
  color: #374151;
  font-weight: 600;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.metadata-value.algorithm-name {
  text-transform: capitalize;
  font-weight: 700;
  color: #1e40af;
  cursor: help;
  position: relative;
  padding: 0.125rem 0.25rem;
  background-color: #dbeafe;
  border-radius: 3px;
  border: 1px solid #93c5fd;
  transition: all 0.2s ease;
}

.metadata-value.algorithm-name:hover {
  background-color: #bfdbfe;
  border-color: #60a5fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
}

/* Enhanced Metadata Sections */
.metadata-section {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.metadata-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.metadata-subsection {
  margin-top: 0.75rem;
  padding: 0.5rem;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.metadata-subsection-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #4a5568;
  margin: 0 0 0.375rem 0;
}

.metadata-subsection .metadata-item {
  margin-bottom: 0.25rem;
  padding: 0.25rem 0;
  background: transparent;
  border: none;
}

.metadata-subsection .metadata-item:last-child {
  margin-bottom: 0;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .structured-response {
    gap: 0.5rem;
    padding: 16px; /* Reduced padding on mobile */
  }
  
  .response-answer {
    font-size: 16px; /* Slightly smaller on mobile for better fit */
    padding: 24px 16px; /* Reduced horizontal padding */
    margin-bottom: 16px; /* Reduced margin */
  }
  
  .response-explanation,
  .response-sources,
  .response-entities {
    padding: 16px; /* Reduced padding on mobile */
    margin: 12px 0 16px 0; /* Reduced margins */
  }
  
  .explanation-title,
  .sources-title,
  .entities-title {
    font-size: 15px; /* Slightly smaller headers on mobile */
    margin-bottom: 12px; /* Reduced spacing */
    padding-bottom: 8px; /* Reduced padding */
  }
  
  .sources-grid {
    grid-template-columns: 1fr;
    gap: 0.375rem; /* Tighter spacing on mobile */
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
    gap: 0.375rem;
  }
  
  .section-header {
    padding: 0.375rem 0.5rem;
  }
  
  .section-content {
    padding: 0.5rem;
  }
  
  .response-followup {
    padding: 16px;
    margin: 12px 0;
  }
  
  .response-metadata {
    padding: 12px; /* Reduced padding for metadata on mobile */
    margin-top: 16px;
    font-size: 13px; /* Smaller metadata text on mobile */
  }
  
  /* Make entity reference buttons more touch-friendly */
  .entity-ref-button {
    padding: 8px 12px; /* Larger touch targets */
    margin: 4px 6px 4px 0; /* Better spacing */
    min-height: 36px; /* Minimum touch target size */
  }
  
  /* Adjust follow-up cards for mobile */
  .show-more-entities {
    padding: 8px 16px; /* Larger touch target */
    margin-top: 8px;
    min-height: 40px; /* Better touch target */
  }
}

/* Extra small screens and high zoom levels */
@media (max-width: 480px) {
  .structured-response {
    padding: 12px;
    gap: 12px;
    margin: 0; /* Remove any margins that could cause overflow */
  }
  
  .response-answer {
    font-size: 16px;
    padding: 16px 12px;
    line-height: 1.5;
  }
  
  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup {
    padding: 12px;
    margin: 8px 0;
  }
  
  .explanation-title,
  .sources-title,
  .entities-title,
  .followup-title {
    font-size: 14px;
    margin-bottom: 8px;
    padding-bottom: 6px;
  }
  
  .sources-grid {
    grid-template-columns: 1fr;
  }
  
  .followup-question-link {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .response-metadata {
    padding: 8px;
    margin-top: 12px;
    font-size: 12px;
  }
}

/* Extreme zoom or very constrained viewports */
@media (max-width: 320px) {
  .structured-response {
    padding: 8px;
    gap: 8px;
    border-radius: 8px;
  }
  
  .response-answer {
    font-size: 15px;
    padding: 12px 8px;
  }
  
  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup {
    padding: 8px;
    margin: 4px 0;
    border-radius: 8px;
  }
  
  .explanation-title,
  .sources-title,
  .entities-title,
  .followup-title {
    font-size: 13px;
    margin-bottom: 6px;
    padding-bottom: 4px;
  }
  
  .followup-question-link {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .entity-ref-button {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 32px;
  }
  
  .show-more-entities {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 36px;
  }
  
  .response-metadata {
    padding: 6px;
    font-size: 11px;
  }
  
  .metadata-item {
    padding: 0.25rem;
    min-height: 1.5rem;
  }
  
  .metadata-label,
  .metadata-value {
    font-size: 0.6rem;
  }
}

/* Accessibility: Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .structured-response,
  .response-answer,
  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup,
  .entity-ref-button,
  .show-more-entities {
    transition: none;
    transform: none;
  }
  
  .entity-ref-button::before {
    transition: none;
  }
  
  .response-answer:hover,
  .response-explanation:hover,
  .response-sources:hover,
  .response-entities:hover,
  .response-followup:hover {
    transform: none;
  }
  
  .entity-ref-button:hover {
    transform: none;
  }
}

/* Accessibility: High Contrast Support */
@media (prefers-contrast: high) {
  .structured-response {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
  }
  
  .response-answer,
  .response-explanation,
  .response-sources,
  .response-entities,
  .response-followup {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
  }
  
  .explanation-title,
  .sources-title,
  .entities-title {
    border-bottom: 2px solid var(--360t-text);
  }
  
  .entity-ref-button {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
    color: var(--360t-text);
  }
  
  .show-more-entities {
    border: 2px solid var(--360t-text);
    background: var(--360t-white);
    color: var(--360t-text);
  }
}
