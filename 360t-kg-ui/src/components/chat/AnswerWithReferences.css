/* AnswerWithReferences component styles */
.answer-with-references {
  position: relative;
}

.answer-content {
  margin-bottom: 0.5rem;
}

/* Reference links within the text - modern professional design */
.answer-content .reference-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #64748B, #475569);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 700;
  text-decoration: none;
  cursor: pointer;
  margin: 0 0.25rem;
  border: none;
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 1.5rem;
  min-height: 1.5rem;
}

/* Entity reference links - modern green gradient */
.answer-content .reference-link.entity {
  background: linear-gradient(135deg, #00973A, #059669);
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.2);
}

.answer-content .reference-link.entity:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 151, 58, 0.25);
}

/* Source reference links - modern grey gradient hover */
.answer-content .reference-link.source:hover,
.answer-content .reference-link:hover {
  background: linear-gradient(135deg, #475569, #334155);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.answer-content .reference-link:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* References summary section */
.references-summary {
  margin-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
  padding-top: 0.5rem;
}

.references-details {
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
}

.references-summary-header {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.references-summary-header:hover {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
}

.references-details[open] .references-summary-header {
  border-bottom: 1px solid #d1d5db;
}

.references-list {
  padding: 0.5rem;
  background: #ffffff;
  max-height: 250px;
  overflow-y: auto;
}

.reference-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.375rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.reference-item:last-child {
  border-bottom: none;
}

.reference-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.375rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 2.25rem;
  height: 2rem;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* Entity reference numbers - modern green gradient */
.reference-number.entity {
  background: linear-gradient(135deg, #00973A, #059669);
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.2);
}

.reference-number.entity:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 151, 58, 0.25);
}

/* Source reference numbers - blue gradient hover */
.reference-number.source:hover,
.reference-number:hover {
  background: linear-gradient(135deg, #2563EB, #1D4ED8);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.25);
}

.reference-number:active {
  transform: translateY(0);
}

.reference-preview {
  flex: 1;
  min-width: 0;
}

.reference-content {
  display: block;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.reference-content.entity-ref {
  color: #065f46;
  font-weight: 500;
}

.reference-category {
  color: #059669;
  font-size: 0.75rem;
  font-weight: 600;
}

.reference-title {
  color: #6b7280;
  font-size: 0.75rem;
  font-style: italic;
}

.reference-missing {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.875rem;
}

/* Reference modal styles */
.reference-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.reference-modal {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.reference-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.reference-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.reference-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
  line-height: 1;
  transition: color 0.2s ease;
}

.reference-modal-close:hover {
  color: #374151;
}

.reference-modal-content {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.source-details {
  color: #374151;
}

.source-details p {
  margin: 0 0 0.75rem 0;
}

.source-details strong {
  color: #111827;
  font-weight: 600;
}

.source-content {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 0.5rem 0 1rem 0;
  white-space: pre-wrap;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #374151;
  max-height: 300px;
  overflow-y: auto;
}

.source-details a {
  color: #3b82f6;
  text-decoration: none;
  word-break: break-all;
}

.source-details a:hover {
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 640px) {
  .reference-modal {
    margin: 0.5rem;
    max-height: 90vh;
  }
  
  .reference-modal-header,
  .reference-modal-content {
    padding: 1rem;
  }
  
  .references-list {
    max-height: 200px;
  }
  
  .reference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .reference-number {
    align-self: flex-start;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .references-summary-header {
    background: linear-gradient(135deg, #1f2937, #111827);
    color: #f9fafb;
  }
  
  .references-summary-header:hover {
    background: linear-gradient(135deg, #374151, #1f2937);
  }
  
  .references-list {
    background: #1f2937;
  }
  
  .reference-content {
    color: #e5e7eb;
  }
  
  .reference-title {
    color: #9ca3af;
  }
  
  .reference-modal {
    background: #1f2937;
  }
  
  .reference-modal-header {
    background: linear-gradient(135deg, #374151, #1f2937);
    border-bottom-color: #374151;
  }
  
  .reference-modal-header h3 {
    color: #f9fafb;
  }
  
  .reference-modal-close {
    color: #9ca3af;
  }
  
  .reference-modal-close:hover {
    color: #e5e7eb;
  }
  
  .source-details {
    color: #e5e7eb;
  }
  
  .source-details strong {
    color: #f9fafb;
  }
  
  .source-content {
    background: #111827;
    border-color: #374151;
    color: #e5e7eb;
  }
}