/* ThinkingSection.css */

.thinking-section {
  margin: 1rem 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #fefefe;
  overflow: hidden;
}

/* Toggle Button */
.thinking-toggle {
  width: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: block;
}

.thinking-toggle:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.thinking-toggle.expanded {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  border-bottom: 1px solid #e2e8f0;
}

.thinking-toggle:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: -2px;
}

/* Header Layout */
.thinking-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.thinking-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #0ea5e9;
}

.thinking-icon svg {
  width: 20px;
  height: 20px;
  color: inherit;
}

.thinking-label {
  flex: 1;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.thinking-arrow {
  font-size: 1.2rem;
  font-weight: bold;
  color: #6b7280;
  flex-shrink: 0;
  min-width: 20px;
  text-align: center;
  transition: transform 0.2s ease;
}

.thinking-toggle.expanded .thinking-arrow {
  transform: scale(1.1);
  color: #0ea5e9;
}

/* Content Area */
.thinking-content {
  padding: 1rem;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-top: 1px solid #f1f5f9;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

/* Thinking Text */
.thinking-text {
  font-style: italic;
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.9rem;
}

.thinking-line {
  margin: 0 0 0.5rem 0;
  word-wrap: break-word;
}

.thinking-line:last-child {
  margin-bottom: 0;
}

/* Footer */
.thinking-footer {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f5f9;
}

.thinking-note {
  color: #6b7280;
  font-style: normal;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.thinking-note::before {
  content: "Thinking";
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .thinking-toggle {
    padding: 0.5rem 0.75rem;
  }
  
  .thinking-content {
    padding: 0.75rem;
  }
  
  .thinking-label {
    font-size: 0.85rem;
  }
  
  .thinking-text {
    font-size: 0.85rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .thinking-section {
    border: 2px solid #000;
  }
  
  .thinking-toggle {
    background: #fff;
    border-bottom: 1px solid #000;
  }
  
  .thinking-toggle:hover {
    background: #f0f0f0;
  }
  
  .thinking-content {
    background: #fff;
    border-top: 1px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .thinking-toggle,
  .thinking-arrow {
    transition: none;
  }
  
  .thinking-content {
    animation: none;
  }
}