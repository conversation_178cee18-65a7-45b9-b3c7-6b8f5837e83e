import React, { useState, useRef, useEffect } from 'react';
import Markdown<PERSON>enderer from './MarkdownRenderer';
import MessageReferences from './MessageReferences';
import StructuredResponse from './chat/StructuredResponse';
import ResponseErrorBoundary from './ResponseErrorBoundary';
import { useChatStore } from '../stores/chatStore';
import { parseAndValidateResponse, logValidationDebug } from '../utils/responseValidator';
import '../styles/ChatViewModern.css';

/**
 * Modern ChatView component with contemporary messaging UI patterns
 * Features:
 * - Modern message bubble design with proper spacing and typography
 * - Enhanced message status indicators (sending, sent, delivered, error states)
 * - Typing indicators and loading states
 * - Better integration with node references and graph context
 * - Improved input area with rich text support hints
 * - Message timestamps and conversation flow improvements
 * - Responsive design for mobile and tablet
 * - Accessibility improvements for screen readers
 * - Professional 360T platform styling
 */
function ChatView({
  placeholder = "Ask me anything about the knowledge graph...",
  onNodeSelect
}) {
  // Get state and actions from Zustand store
  const {
    conversations,
    currentConversation,
    messages,
    isLoading,
    error,
    sendMessage,
    sendStreamingMessage,
    createConversation,
    loadConversations,
    loadConversation,
    loadMessages
  } = useChatStore();

  // Local state for input and UI feedback
  const [inputMessage, setInputMessage] = useState('');
  const [useStreaming, setUseStreaming] = useState(false);
  const [fallbackMessages, setFallbackMessages] = useState(new Set());
  const [isCreatingConversation, setIsCreatingConversation] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [showTypingIndicator, setShowTypingIndicator] = useState(false);
  const [inputFocused, setInputFocused] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const textareaRef = useRef(null);

  // Load conversations on mount with error handling
  useEffect(() => {
    const initializeConversations = async () => {
      try {
        console.log('🔄 Loading conversations on ChatView mount...');
        await loadConversations();
        console.log('✅ Conversations loaded successfully:', conversations.length, 'conversations');
        if (conversations.length > 0) {
          console.log('📋 First conversation:', conversations[0]);
        }
      } catch (error) {
        console.error('❌ Failed to load conversations:', error);
        // The error is already handled by the store, just log it here
      }
    };
    
    initializeConversations();
  }, [loadConversations]);

  // Debug effect to monitor conversations changes
  useEffect(() => {
    console.log('🔍 Conversations changed:', {
      count: conversations.length,
      currentConversation: currentConversation?.id,
      conversationTitles: conversations.map(c => ({ id: c.id, title: c.title, name: c.name }))
    });
  }, [conversations, currentConversation]);

  // Auto-focus the input when the conversation changes
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [currentConversation?.id]);

  // Auto-focus when conversation changes
  useEffect(() => {
    if (currentConversation?.id) {
      loadMessages(currentConversation.id);
    }
  }, [currentConversation?.id, loadMessages]);

  // Auto-scroll to latest message
  const scrollToBottom = () => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Ensure conversation exists before sending message
  const ensureConversationExists = async () => {
    if (!currentConversation) {
      console.log('No current conversation, creating new one...');
      try {
        await createConversation('New Chat', 'Chat conversation');
      } catch (error) {
        console.error('Failed to create conversation:', error);
        throw new Error('Failed to create conversation: ' + error.message);
      }
    }
  };

  // Handle form submission with enhanced UI feedback
  const handleSubmit = async (e) => {
    e.preventDefault();
    const trimmedMessage = inputMessage.trim();
    
    if (!trimmedMessage || isLoading || isSending) return;
    
    try {
      setIsSending(true);
      setShowTypingIndicator(true);
      
      // Ensure we have a conversation before sending
      await ensureConversationExists();
      
      // Send message using either streaming or regular method based on checkbox
      if (useStreaming) {
        await sendStreamingMessage(trimmedMessage);
      } else {
        await sendMessage(trimmedMessage);
      }
      
      // Clear input
      setInputMessage('');
      
      // Auto-resize textarea
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSending(false);
      setShowTypingIndicator(false);
    }
  };

  // Handle input key press (Enter to send, Shift+Enter for new line)
  const handleKeyDown = async (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      
      const trimmedMessage = inputMessage.trim();
      if (!trimmedMessage || isLoading || isSending) return;

      try {
        setIsSending(true);
        setShowTypingIndicator(true);
        
        // Ensure we have a conversation before sending
        await ensureConversationExists();
        
        // Send message using either streaming or regular method based on checkbox
        if (useStreaming) {
          await sendStreamingMessage(trimmedMessage);
        } else {
          await sendMessage(trimmedMessage);
        }
        setInputMessage('');
        
        // Auto-resize textarea
        if (textareaRef.current) {
          textareaRef.current.style.height = 'auto';
        }
      } catch (error) {
        console.error('Failed to send message:', error);
      } finally {
        setIsSending(false);
        setShowTypingIndicator(false);
      }
    }
  };

  // Format timestamp with relative time
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Auto-resize textarea
  const handleInputChange = (e) => {
    setInputMessage(e.target.value);
    
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  // Get message status for UI indicators
  const getMessageStatus = (message, index) => {
    if (message.role === 'user') {
      if (index === messages.length - 1 && (isLoading || isSending)) {
        return 'sending';
      }
      return 'sent';
    }
    return null;
  };

  // Check if message content is a v2.0 structured response with enhanced validation
  const parseStructuredResponse = (content) => {
    const validationResult = parseAndValidateResponse(content);
    
    // Log debug information in development
    if (process.env.NODE_ENV === 'development') {
      logValidationDebug(validationResult, 'ChatView');
    }
    
    return validationResult;
  };

  // Legacy function for backward compatibility
  const isStructuredResponse = (content) => {
    const result = parseStructuredResponse(content);
    return result.success;
  };

  // Handle fallback to plain text rendering
  const handleFallbackToPlainText = (_, messageIndex) => {
    const newFallbackMessages = new Set(fallbackMessages);
    newFallbackMessages.add(messageIndex);
    setFallbackMessages(newFallbackMessages);
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Falling back to plain text for message:', messageIndex);
    }
  };

  return (
    <div className="chat-view-modern" role="main" aria-label="Chat interface">
      <div className="chat-header-modern">
        <div className="chat-header-content">
          <div className="conversation-info">
            <h2 className="conversation-title">
              {currentConversation?.title || currentConversation?.name || 'New Chat'}
            </h2>
            <div className="conversation-meta">
              <span className="message-count" aria-label={`${messages.length} messages`}>
                {messages.length} messages
              </span>
              {conversations.length > 0 && (
                <span className="conversation-indicator" aria-hidden="true">•</span>
              )}
              <span className="online-status" aria-label="Assistant online">AI Assistant</span>
            </div>
          </div>
          <div className="chat-controls-modern">
            <select 
              className="conversation-selector-modern" 
              value={currentConversation?.id || ''} 
              onChange={(e) => {
                if (e.target.value) {
                  console.log('🔄 Loading conversation:', e.target.value);
                  loadConversation(e.target.value);
                }
              }}
              disabled={isLoading}
              aria-label="Select conversation"
            >
              <option value="">
                {conversations.length === 0 
                  ? (isLoading ? "Loading conversations..." : "No conversations yet") 
                  : "Switch conversation..."
                }
              </option>
              {conversations
                .sort((a, b) => new Date(b.created_at || b.updated_at) - new Date(a.created_at || a.updated_at))
                .map(c => (
                  <option key={c.id} value={c.id}>
                    {c.title || c.name || `Conversation ${c.id.slice(0, 8)}`}
                  </option>
                ))}
            </select>
            <button 
              onClick={async () => {
                if (isCreatingConversation) return;
                
                setIsCreatingConversation(true);
                try {
                  console.log('🔄 Creating new conversation...');
                  await createConversation('New Chat', 'Chat conversation');
                  console.log('✅ New conversation created successfully');
                } catch (error) {
                  console.error('❌ Failed to create new conversation:', error);
                } finally {
                  setIsCreatingConversation(false);
                }
              }} 
              title="Start new conversation" 
              className="new-chat-button-modern"
              disabled={isLoading || isCreatingConversation}
              aria-label="Start new conversation"
            >
              {isCreatingConversation ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="loading-spinner" aria-hidden="true">
                  <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
                  <path d="M12 5v14m7-7H5"/>
                </svg>
              )}
              <span className="sr-only">New conversation</span>
            </button>
            <div className="streaming-toggle-modern">
              <label className="toggle-switch" aria-label="Enable streaming responses">
                <input 
                  type="checkbox" 
                  checked={useStreaming} 
                  onChange={(e) => setUseStreaming(e.target.checked)}
                  disabled={isLoading}
                  className="toggle-input"
                />
                <span className="toggle-slider" aria-hidden="true"></span>
                <span className="toggle-label">Stream</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="chat-error-modern" role="alert" aria-live="polite">
          <div className="error-icon" aria-hidden="true">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
          </div>
          <div className="error-content">
            <span className="error-title">Something went wrong</span>
            <span className="error-message">{error}</span>
          </div>
        </div>
      )}

      {/* Messages Container */}
      <div className="messages-container-modern" role="log" aria-live="polite" aria-label="Chat messages">
        {messages.length === 0 ? (
          <div className="empty-state-modern">
            <div className="empty-state-illustration" aria-hidden="true">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"/>
              </svg>
            </div>
            <h3 className="empty-state-title">Welcome to 360T Knowledge Graph Chat</h3>
            <p className="empty-state-description">
              Ask me anything about the knowledge graph, system components, or relationships between entities. 
              I can help you explore and understand the platform architecture.
            </p>
            <div className="empty-state-suggestions">
              <button 
                className="suggestion-chip"
                onClick={() => setInputMessage('Show me the main system components')}
                type="button"
              >
                Show me the main system components
              </button>
              <button 
                className="suggestion-chip"
                onClick={() => setInputMessage('How are modules connected?')}
                type="button"
              >
                How are modules connected?
              </button>
              <button 
                className="suggestion-chip"
                onClick={() => setInputMessage('Explain the data flow')}
                type="button"
              >
                Explain the data flow
              </button>
            </div>
          </div>
        ) : (
          <div className="messages-list-modern">
            {messages.map((message, index) => {
              const messageStatus = getMessageStatus(message, index);
              return (
                <div key={message.id || index} className={`message-bubble ${message.role}`} role="article">
                  <div className="message-avatar" aria-hidden="true">
                    {message.role === 'user' ? (
                      <div className="user-avatar">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L18 4L16.5 5.5C15.1 4.3 13.2 3.8 11.2 4.3C9.2 4.8 7.5 6.1 6.7 7.9C5.9 9.7 6.1 11.8 7.3 13.4L3 17.7V21H6.3L10.6 16.7C11.8 17.5 13.3 17.8 14.7 17.5C16.1 17.2 17.3 16.3 18 15C18.7 13.7 18.9 12.2 18.5 10.8L21 9Z"/>
                        </svg>
                      </div>
                    ) : (
                      <div className="assistant-avatar">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7A1,1 0 0,1 12,8H11V10A1,1 0 0,1 10,11H9V12H22A1,1 0 0,1 23,13V19A1,1 0 0,1 22,20H2A1,1 0 0,1 1,19V13A1,1 0 0,1 2,12H7V9A1,1 0 0,1 8,8H9V7H10V5.73C9.4,5.39 9,4.74 9,4A2,2 0 0,1 12,2M12,4.5A0.5,0.5 0 0,0 11.5,4A0.5,0.5 0 0,0 12,3.5A0.5,0.5 0 0,0 12.5,4A0.5,0.5 0 0,0 12,4.5Z"/>
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="message-content-modern">
                    <div className="message-bubble-content">
                      <div className="message-text-modern">
                        {message.role === 'assistant' ? (
                          isStructuredResponse(message.content) && !fallbackMessages.has(index) ? (
                            <ResponseErrorBoundary
                              messageContent={message.content}
                              onFallbackToPlainText={() => handleFallbackToPlainText(message.content, index)}
                            >
                              <StructuredResponse
                                response={JSON.parse(message.content)}
                                onNodeSelect={(selectedNode) => {
                                  console.log('🔍 ChatView onNodeSelect - DEBUGGING NODE SELECTION:');
                                  console.log('1. Selected node received from StructuredResponse:', selectedNode);
                                  console.log('2. onNodeSelect prop exists:', typeof onNodeSelect === 'function');
                                  
                                  if (onNodeSelect) {
                                    console.log('3. Calling parent onNodeSelect with selectedNode:', selectedNode);
                                    onNodeSelect(selectedNode);
                                    console.log('4. Parent onNodeSelect call completed');
                                  } else {
                                    console.error('❌ ChatView: onNodeSelect prop is missing or not a function');
                                  }
                                }}
                                onSendMessage={sendMessage}
                                showDebugInfo={true}
                                legacySourceDocuments={message.source_documents || []}
                                legacySourceNodes={message.source_nodes || []}
                              />
                            </ResponseErrorBoundary>
                          ) : (
                            <MarkdownRenderer
                              content={message.content}
                              onSendMessage={sendMessage}
                            />
                          )
                        ) : (
                          message.content
                        )}
                      </div>
                    </div>
                    <div className="message-meta">
                      <span className="message-timestamp-modern">
                        {formatTimestamp(message.created_at)}
                      </span>
                      {messageStatus && (
                        <div className="message-status" aria-label={`Message ${messageStatus}`}>
                          {messageStatus === 'sending' ? (
                            <div className="status-sending" aria-hidden="true">
                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="status-icon loading-spinner">
                                <path d="M21 12a9 9 0 11-6.219-8.56"/>
                              </svg>
                            </div>
                          ) : messageStatus === 'sent' ? (
                            <div className="status-sent" aria-hidden="true">
                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="status-icon">
                                <polyline points="20,6 9,17 4,12"/>
                              </svg>
                            </div>
                          ) : null}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {/* Typing Indicator */}
            {showTypingIndicator && (
              <div className="message-bubble assistant typing" role="status" aria-label="Assistant is typing">
                <div className="message-avatar" aria-hidden="true">
                  <div className="assistant-avatar">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7A1,1 0 0,1 12,8H11V10A1,1 0 0,1 10,11H9V12H22A1,1 0 0,1 23,13V19A1,1 0 0,1 22,20H2A1,1 0 0,1 1,19V13A1,1 0 0,1 2,12H7V9A1,1 0 0,1 8,8H9V7H10V5.73C9.4,5.39 9,4.74 9,4A2,2 0 0,1 12,2M12,4.5A0.5,0.5 0 0,0 11.5,4A0.5,0.5 0 0,0 12,3.5A0.5,0.5 0 0,0 12.5,4A0.5,0.5 0 0,0 12,4.5Z"/>
                    </svg>
                  </div>
                </div>
                <div className="message-content-modern">
                  <div className="message-bubble-content">
                    <div className="typing-indicator-modern">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input Form */}
      <div className="chat-input-container-modern">
        <form onSubmit={handleSubmit} className="chat-form-modern">
          <div className={`input-wrapper-modern ${inputFocused ? 'focused' : ''}`}>
            <div className="input-field-wrapper">
              <textarea
                ref={(el) => {
                  inputRef.current = el;
                  textareaRef.current = el;
                }}
                value={inputMessage}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onFocus={() => setInputFocused(true)}
                onBlur={() => setInputFocused(false)}
                placeholder={placeholder}
                rows="1"
                className="chat-input-modern"
                disabled={isLoading || isSending}
                aria-label="Type your message here"
                aria-describedby="input-hint"
              />
              <div className="input-hint" id="input-hint">
                <span className="hint-text">Press Enter to send, Shift+Enter for new line</span>
                <div className="input-actions">
                  {inputMessage.trim() && (
                    <button
                      type="button"
                      className="clear-input-button"
                      onClick={() => {
                        setInputMessage('');
                        if (textareaRef.current) {
                          textareaRef.current.style.height = 'auto';
                          textareaRef.current.focus();
                        }
                      }}
                      aria-label="Clear input"
                      title="Clear input"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            </div>
            <button 
              type="submit" 
              className={`send-button-modern ${(!inputMessage.trim() || isLoading || isSending) ? 'disabled' : 'enabled'}`}
              disabled={!inputMessage.trim() || isLoading || isSending}
              title={isSending ? 'Sending...' : 'Send message'}
              aria-label={isSending ? 'Sending message' : 'Send message'}
            >
              {isSending ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="loading-spinner" aria-hidden="true">
                  <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true">
                  <path d="m22 2-7 20-4-9-9-4z"/>
                  <path d="M22 2 11 13"/>
                </svg>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ChatView;