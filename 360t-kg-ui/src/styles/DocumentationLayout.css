/* Documentation Layout Styles */
.documentation-layout {
  display: flex;
  height: 100%;
  width: 100%;
  position: relative;
  background: #f8fafc;
}

.documentation-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevents flex item from overflowing */
  position: relative;
}

/* Mobile Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .documentation-layout {
    position: relative;
  }

  .sidebar-overlay {
    display: block;
  }

  .documentation-main {
    width: 100%;
  }
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Smooth transitions for layout changes */
.documentation-layout * {
  transition: width 0.3s ease, transform 0.3s ease;
}

/* Print Styles */
@media print {
  .documentation-layout {
    display: block;
  }

  .documentation-sidebar {
    display: none;
  }

  .documentation-main {
    width: 100%;
    max-width: none;
  }

  .reader-header {
    border-bottom: 2px solid #000;
    background: white;
  }

  .table-of-contents {
    display: none;
  }

  .back-to-top {
    display: none;
  }

  .content-body {
    max-width: none;
    padding: 1rem 0;
  }

  .content-body h1,
  .content-body h2,
  .content-body h3,
  .content-body h4,
  .content-body h5,
  .content-body h6 {
    color: #000;
    page-break-after: avoid;
  }

  .content-body p,
  .content-body li {
    color: #000;
    line-height: 1.4;
  }

  .content-body pre {
    background: #f5f5f5;
    color: #000;
    border: 1px solid #ddd;
  }

  .content-body blockquote {
    border-left: 4px solid #000;
    color: #333;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .documentation-layout {
    background: white;
  }

  .documentation-sidebar {
    border-right: 2px solid #000;
  }

  .sidebar-header {
    background: white;
    border-bottom: 2px solid #000;
  }

  .nav-item {
    border-left: 3px solid transparent;
  }

  .nav-item:hover {
    background: #f0f0f0;
    border-left-color: #000;
  }

  .nav-item.active {
    background: #e0e0e0;
    border-left-color: #000;
  }

  .reader-header {
    background: white;
    border-bottom: 2px solid #000;
  }

  .table-of-contents {
    background: white;
    border-bottom: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .documentation-layout *,
  .nav-item,
  .sidebar-toggle,
  .close-button,
  .toc-item,
  .back-to-top {
    transition: none;
    animation: none;
  }

  .loading-spinner {
    animation: none;
    border: 3px solid #ccc;
    border-top: 3px solid #000;
  }
}

/* Focus Management */
.documentation-layout:focus-within {
  outline: none;
}

/* Ensure proper stacking context */
.documentation-layout {
  z-index: 1;
}

.documentation-sidebar {
  z-index: 10;
}

.sidebar-overlay {
  z-index: 9;
}

.documentation-main {
  z-index: 2;
}

.back-to-top {
  z-index: 100;
}
