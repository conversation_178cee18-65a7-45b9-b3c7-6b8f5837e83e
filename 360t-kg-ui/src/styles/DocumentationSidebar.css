/* Documentation Sidebar Styles */
.documentation-sidebar {
  width: 280px;
  height: 100%;
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.documentation-sidebar.collapsed {
  width: 60px;
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  min-height: 60px;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.sidebar-toggle:hover {
  background: #e2e8f0;
  color: #334155;
}

.sidebar-title {
  margin: 0;
  margin-left: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  white-space: nowrap;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.nav-item:hover {
  background: #f1f5f9;
  border-left-color: #cbd5e1;
}

.nav-item.active {
  background: #f0f9ff;
  border-left-color: #0ea5e9;
  color: #0c4a6e;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #0ea5e9;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: #64748b;
  transition: color 0.2s ease;
}

.nav-icon svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
}

.nav-item:hover .nav-icon {
  color: #334155;
}

.nav-item.active .nav-icon {
  color: #0ea5e9;
}

.collapsed .nav-icon {
  margin-right: 0;
}

.nav-content {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.nav-title {
  font-weight: 500;
  color: #334155;
  font-size: 0.95rem;
  line-height: 1.3;
  margin-bottom: 0.125rem;
}

.nav-desc {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-item.active .nav-title {
  color: #0c4a6e;
  font-weight: 600;
}

.nav-item.active .nav-desc {
  color: #0369a1;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.sidebar-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.info-text {
  font-size: 0.85rem;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.info-version {
  font-size: 0.75rem;
  color: #94a3b8;
  background: #e2e8f0;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .documentation-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1000;
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
  }

  .documentation-sidebar.collapsed {
    transform: translateX(-100%);
    width: 280px;
  }
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus States for Accessibility */
.sidebar-toggle:focus,
.nav-item:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: -2px;
}

/* Animation for smooth transitions */
.nav-item {
  transform: translateX(0);
}

.nav-item:hover {
  transform: translateX(2px);
}

.nav-item.active {
  transform: translateX(4px);
}
