/* 360T Corporate Theme */
:root {
  --360t-primary: #00973A;      /* 360T green */
  --360t-primary-dark: #007d30;
  --360t-secondary: #005920;    /* Dark green (was Deutsche Börse blue) */
  --360t-text: #333333;
  --360t-light-gray: #f5f5f5;
  --360t-mid-gray: #e2e8f0;
  --360t-dark-gray: #4a5568;
  --360t-border: #ddd;
  --360t-white: #ffffff;
  --360t-shadow: rgba(0, 0, 0, 0.1);
  
  /* Typography Design Tokens */
  --360t-font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
  --360t-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Font Sizes */
  --360t-text-xs: 0.75rem;    /* 12px */
  --360t-text-sm: 0.875rem;   /* 14px */
  --360t-text-base: 1rem;     /* 16px */
  --360t-text-lg: 1.125rem;   /* 18px */
  --360t-text-xl: 1.25rem;    /* 20px */
  --360t-text-2xl: 1.5rem;    /* 24px */
  --360t-text-3xl: 1.875rem;  /* 30px */
  
  /* Line Heights */
  --360t-leading-tight: 1.25;
  --360t-leading-snug: 1.375;
  --360t-leading-normal: 1.5;
  --360t-leading-relaxed: 1.625;
  
  /* Font Weights */
  --360t-font-light: 300;
  --360t-font-normal: 400;
  --360t-font-medium: 500;
  --360t-font-semibold: 600;
  --360t-font-bold: 700;
  
  /* Spacing Design Tokens - 8px Base System */
  --360t-space-0: 0;         /* 0px */
  --360t-space-1: 0.25rem;   /* 4px */
  --360t-space-2: 0.5rem;    /* 8px */
  --360t-space-3: 0.75rem;   /* 12px */
  --360t-space-4: 1rem;      /* 16px */
  --360t-space-5: 1.25rem;   /* 20px */
  --360t-space-6: 1.5rem;    /* 24px */
  --360t-space-7: 1.75rem;   /* 28px */
  --360t-space-8: 2rem;      /* 32px */
  --360t-space-9: 2.25rem;   /* 36px */
  --360t-space-10: 2.5rem;   /* 40px */
  --360t-space-12: 3rem;     /* 48px */
  --360t-space-16: 4rem;     /* 64px */
  --360t-space-20: 5rem;     /* 80px */
  
  /* Border Radius Design Tokens */
  --360t-radius-sm: 4px;
  --360t-radius-md: 6px;
  --360t-radius-lg: 8px;
  --360t-radius-xl: 12px;
  
  /* Shadow Design Tokens */
  --360t-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --360t-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --360t-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --360t-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Layout Design Tokens */
  --header-height: 96px;  /* Realistic header height based on actual content requirements (90px nav + 6px buffer) */
  
  /* Override existing variables */
  --primary-color: var(--360t-primary);
  --primary-dark: var(--360t-primary-dark);
  --secondary-color: var(--360t-secondary);
  --text-color: var(--360t-text);
  --border-color: var(--360t-border);
}

/* Typography System */
body {
  font-family: var(--360t-font-family-primary);
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  font-weight: var(--360t-font-normal);
  color: var(--360t-text);
  background-color: var(--360t-light-gray);
}

/* Heading System */
h1 {
  font-size: var(--360t-text-3xl);
  line-height: var(--360t-leading-tight);
  font-weight: var(--360t-font-bold);
  color: var(--360t-text);
  margin: 0 0 var(--360t-space-6) 0;
}

h2 {
  font-size: var(--360t-text-2xl);
  line-height: var(--360t-leading-tight);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  margin: 0 0 var(--360t-space-5) 0;
}

h3 {
  font-size: var(--360t-text-xl);
  line-height: var(--360t-leading-snug);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  margin: 0 0 var(--360t-space-4) 0;
}

h4 {
  font-size: var(--360t-text-lg);
  line-height: var(--360t-leading-snug);
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
  margin: 0 0 var(--360t-space-3) 0;
}

h5 {
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
  margin: 0 0 var(--360t-space-2) 0;
}

h6 {
  font-size: var(--360t-text-sm);
  line-height: var(--360t-leading-normal);
  font-weight: var(--360t-font-medium);
  color: var(--360t-dark-gray);
  margin: 0 0 var(--360t-space-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Header */
.app-header {
  background-color: var(--360t-white);
  box-shadow: 0 2px 4px var(--360t-shadow);
  padding: 0.75rem 1.5rem;
}

.app-logo {
  height: 40px;
  display: flex;
  align-items: center;
}

.app-logo img {
  height: 100%;
}

.main-nav ul {
  margin-left: 2rem;
}

.nav-button {
  color: var(--360t-dark-gray);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s, color 0.2s;
}

.nav-button:hover {
  background-color: rgba(0, 151, 58, 0.1);
}

.nav-button.active {
  color: var(--360t-primary);
  background-color: rgba(0, 151, 58, 0.1);
}

/* Main Content */
.content-wrapper {
  padding: 1.5rem;
}

.content-wrapper h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--360t-mid-gray);
}

/* Search Form */
.search-wrapper {
  background-color: var(--360t-white);
  border-bottom: 1px solid var(--360t-border);
  padding: 1.25rem;
}

.search-input {
  border: 1px solid var(--360t-mid-gray);
  border-radius: 4px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.search-input:focus {
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.search-button {
  background-color: var(--360t-primary);
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-button:hover {
  background-color: var(--360t-primary-dark);
}

/* Panel Styling */
.details-panel {
  background-color: var(--360t-white);
  border-left: 1px solid var(--360t-border);
  box-shadow: -2px 0 10px var(--360t-shadow);
}

.panel-header {
  background-color: var(--360t-white);
  border-bottom: 1px solid var(--360t-border);
}

.panel-header h2 {
  font-size: 1.25rem;
  color: var(--360t-text);
}

.panel-footer {
  background-color: var(--360t-light-gray);
  border-top: 1px solid var(--360t-border);
}

/* Buttons */
.action-button {
  background-color: var(--360t-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: var(--360t-primary-dark);
}

.back-button {
  background-color: var(--360t-light-gray);
  color: var(--360t-dark-gray);
  border: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: var(--360t-mid-gray);
}

/* Legend */
.legend-container {
  background-color: var(--360t-white);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--360t-shadow);
}

.legend-title {
  color: var(--360t-text);
  border-bottom: 1px solid var(--360t-mid-gray);
}

/* Color picker modal */
.color-picker-modal {
  background-color: var(--360t-white);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--360t-shadow);
}

.modal-header {
  border-bottom: 1px solid var(--360t-mid-gray);
}

.apply-button {
  background-color: var(--360t-primary);
  color: white;
}

.apply-button:hover {
  background-color: var(--360t-primary-dark);
}

.cancel-button {
  background-color: var(--360t-white);
  border: 1px solid var(--360t-mid-gray);
}

/* Analysis tools */
.tool-button {
  background-color: var(--360t-white);
  border: 1px solid var(--360t-mid-gray);
  border-radius: 4px;
  padding: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.tool-button:hover {
  border-color: var(--360t-primary);
  box-shadow: 0 2px 8px var(--360t-shadow);
}

.tool-button.active {
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 2px rgba(0, 151, 58, 0.2);
}

/* Loading and errors */
.loading-spinner {
  border-top-color: var(--360t-primary);
}

.error-message {
  background-color: #fff5f5;
  border: 1px solid #feb2b2;
}

.retry-button {
  background-color: var(--360t-primary);
  color: white;
} 