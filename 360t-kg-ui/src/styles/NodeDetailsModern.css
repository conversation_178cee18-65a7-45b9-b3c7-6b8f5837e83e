/* Modern Node Details Panel Styles */

/* Design Tokens - 360T Design System */
:root {
  /* Layout - Now uses global header height variable */
  /* --app-header-height: removed - using global --header-height instead */
  /* Colors */
  --360t-primary: #00973A;
  --360t-primary-dark: #007d30;
  --360t-primary-light: #10b981;
  --360t-white: #ffffff;
  --360t-gray-50: #f8fafc;
  --360t-gray-100: #f1f5f9;
  --360t-gray-200: #e2e8f0;
  --360t-gray-300: #cbd5e1;
  --360t-gray-400: #94a3b8;
  --360t-gray-500: #64748b;
  --360t-gray-600: #475569;
  --360t-gray-700: #334155;
  --360t-gray-800: #1e293b;
  --360t-gray-900: #0f172a;
  
  --360t-error: #dc2626;
  --360t-error-light: #fef2f2;
  --360t-error-border: #fecaca;
  
  --360t-success: #059669;
  --360t-success-light: #f0fdf4;
  --360t-success-border: #bbf7d0;
  
  /* Typography */
  --360t-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
  --360t-font-size-xs: 0.75rem;      /* 12px */
  --360t-font-size-sm: 0.875rem;     /* 14px */
  --360t-font-size-base: 1rem;       /* 16px */
  --360t-font-size-lg: 1.125rem;     /* 18px */
  --360t-font-size-xl: 1.25rem;      /* 20px */
  
  --360t-font-weight-normal: 400;
  --360t-font-weight-medium: 500;
  --360t-font-weight-semibold: 600;
  --360t-font-weight-bold: 700;
  
  --360t-line-height-tight: 1.25;
  --360t-line-height-normal: 1.5;
  --360t-line-height-relaxed: 1.625;
  
  /* Spacing */
  --360t-space-1: 0.25rem;    /* 4px */
  --360t-space-2: 0.5rem;     /* 8px */
  --360t-space-3: 0.75rem;    /* 12px */
  --360t-space-4: 1rem;       /* 16px */
  --360t-space-5: 1.25rem;    /* 20px */
  --360t-space-6: 1.5rem;     /* 24px */
  --360t-space-8: 2rem;       /* 32px */
  --360t-space-10: 2.5rem;    /* 40px */
  --360t-space-12: 3rem;      /* 48px */
  
  /* Border Radius */
  --360t-radius-sm: 0.25rem;   /* 4px */
  --360t-radius-md: 0.5rem;    /* 8px */
  --360t-radius-lg: 0.75rem;   /* 12px */
  --360t-radius-xl: 1rem;      /* 16px */
  
  /* Shadows */
  --360t-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --360t-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --360t-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --360t-shadow-primary: 0 2px 8px rgba(0, 151, 58, 0.2);
  
  /* Transitions */
  --360t-transition-fast: 0.15s ease;
  --360t-transition-normal: 0.2s ease;
  --360t-transition-slow: 0.3s ease;
  --360t-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
}

.node-details-modern {
  position: fixed;
  top: var(--header-height, 96px); /* Uses global header height */
  right: 0;
  width: var(--panel-width, 28%);
  height: calc(100vh - var(--header-height, 56px));
  background-color: var(--360t-white);
  box-shadow: var(--360t-shadow-lg);
  border-left: 2px solid var(--360t-gray-200);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 95; /* below app header (100), above canvas/tooling */
  transition: width var(--360t-transition-slow) var(--360t-easing-smooth);
  font-family: var(--360t-font-family);
  font-size: var(--360t-font-size-sm);
  line-height: var(--360t-line-height-normal);
  color: var(--360t-gray-800);
}

.node-details-modern.collapsed {
  width: 48px;
}

/* Ensure the header remains visible and clickable when collapsed */
.node-details-modern.collapsed .node-details-header {
  position: fixed;
  right: 0;
  top: var(--header-height, 96px);
  width: 48px;
  height: 48px;
  min-height: 48px;
  padding: 8px;
  border-left: 2px solid var(--360t-gray-200);
  border-bottom: 1px solid var(--360t-gray-200);
  background: var(--360t-white);
  z-index: 99;
}

/* Show only the expand button while collapsed */
.node-details-modern.collapsed .node-header-left,
.node-details-modern.collapsed .node-meta {
  display: none !important;
}

.node-details-modern.collapsed .node-actions {
  margin-left: 0;
}

.node-details-modern.collapsed .icon-btn {
  width: 36px;
  height: 36px;
}

/* Header Section */
.node-details-header {
  position: sticky; /* ensure header is always visible within panel */
  top: 0;
  z-index: 1;
  display: grid;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  column-gap: 16px;
  row-gap: 4px;
  padding: 10px 16px;
  background: var(--360t-white);
  color: var(--360t-gray-800);
  border-bottom: 1px solid var(--360t-gray-200);
  box-shadow: none;
  flex-shrink: 0;
  min-height: 60px;
}

.node-details-header.collapsed {
  padding: 8px;
  justify-content: center;
  cursor: default;
}

.node-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
  grid-column: 1 / 2;
  min-width: 0;
}

.node-header-left.collapsed {
  display: none;
}

.node-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: grid;
  place-items: center;
  background: var(--360t-gray-100);
  border: 1px solid var(--360t-gray-200);
}
.node-icon--surface {
  background: var(--360t-gray-100);
}

.node-icon-image {
  width: 24px !important;
  height: 24px !important;
  object-fit: contain;
  max-width: 24px !important;
  max-height: 24px !important;
  display: block;
  flex-shrink: 0;
  margin: 0 auto;
}

/* Category badge for node header */
.category-badge {
  display: inline-block;
  padding: var(--360t-space-1) var(--360t-space-2);
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: var(--360t-radius-sm);
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
  align-self: flex-start;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
}

/* Section card layout */
.section-card-modern {
  margin-bottom: var(--360t-space-4);
  background: var(--360t-white);
  border-radius: var(--360t-radius-lg);
  border: 1px solid var(--360t-gray-200);
  overflow: hidden;
  transition: all var(--360t-transition-normal) var(--360t-easing-smooth);
  box-shadow: var(--360t-shadow-sm);
}

.section-card-modern:hover {
  border-color: var(--360t-primary);
  box-shadow: 0 4px 12px rgba(0, 151, 58, 0.12);
  transform: translateY(-2px);
}

/* Section headers */
.section-header-card {
  padding: var(--360t-space-3) var(--360t-space-4);
  background: var(--360t-gray-50);
  border-bottom: 1px solid var(--360t-gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 48px;
  position: relative;
}

.section-header-card.clickable {
  cursor: pointer;
  transition: background-color var(--360t-transition-normal);
}

.section-header-card.clickable:hover {
  background: var(--360t-gray-100);
  box-shadow: inset 0 1px 2px rgba(0, 151, 58, 0.1);
}
.section-header-card[aria-expanded="true"],
.section-header-card.expanded {
  background: var(--360t-success-light);
}

.section-icon-title {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  min-width: 0;
}

.section-title-card {
  margin: 0;
  font-size: var(--360t-font-size-sm);
  font-weight: var(--360t-font-weight-semibold);
  color: var(--360t-gray-700);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: -0.01em;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  flex-shrink: 0;
}

.section-count {
  background: var(--360t-primary);
  color: var(--360t-white);
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-medium);
  padding: var(--360t-space-1) var(--360t-space-3);
  border-radius: 999px;
  min-width: 20px;
  text-align: center;
}

.expand-icon-card {
  font-size: var(--360t-font-size-sm);
  font-weight: var(--360t-font-weight-semibold);
  color: var(--360t-primary);
  background: var(--360t-success-light);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--360t-transition-normal);
  border: 1px solid var(--360t-success-border);
}

.expand-icon-card:hover {
  background: var(--360t-success-light);
  transform: scale(1.05);
}

.expand-icon-card.expanded {
  background: var(--360t-primary);
  color: var(--360t-white);
  border-color: var(--360t-primary);
}

/* Preview indicators */
.preview-indicator-card {
  padding: 0.375rem 1rem;
  background: var(--360t-gray-50);
  border-bottom: 1px solid var(--360t-gray-200);
  font-size: 0.75rem;
  color: var(--360t-gray-500);
  font-weight: 500;
  text-align: center;
  font-style: italic;
}

.node-title-wrap {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 0;
}
.node-title {
  margin: 0;
  font-size: var(--360t-font-size-lg);
  font-weight: var(--360t-font-weight-semibold);
  color: var(--360t-gray-900);
  max-width: 100%;
}
.node-title--singleline {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Container for badges to handle spacing */
/* Chips/meta area */
.node-meta {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  grid-column: 2 / 3;
  min-width: 0;
}

.chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 56px;
  overflow: hidden;
}

/* Header relation-style badge to match relationship pills */
.relation-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 2px 10px;
  height: 24px;
  border-radius: 999px;
  font: 600 12px/1 var(--360t-font-family);
  color: var(--360t-white);
  background: var(--360t-primary);
  border: 1px solid var(--360t-primary);
  box-shadow: var(--360t-shadow-sm);
  white-space: nowrap;
}

.relation-badge__text {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Variant for header usage (slightly bolder like in relation list) */
.relation-badge--header {
  letter-spacing: 0.02em;
}

/* legacy header badges no longer used in new header */
.node-type-badge { display: none; }
.category-badge { display: none; }

.node-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  grid-column: 3 / 4;
  justify-self: end;
}

.icon-btn {
  width: 32px;
  height: 32px;
  display: grid;
  place-items: center;
  border-radius: 6px;
  color: var(--360t-gray-600);
  background: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--360t-transition-normal) var(--360t-easing-smooth);
}
.icon-btn:hover {
  color: var(--360t-primary-dark);
  background: var(--360t-gray-50);
  border-color: var(--360t-gray-200);
}

.control-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.control-button:active {
  transform: translateY(0);
}

/* Content Section */
.node-details-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  scroll-behavior: smooth;
  background-color: var(--360t-white);
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.node-details-content::-webkit-scrollbar {
  width: 6px;
}

.node-details-content::-webkit-scrollbar-track {
  background: var(--360t-gray-100);
}

.node-details-content::-webkit-scrollbar-thumb {
  background: var(--360t-gray-200);
  border-radius: 3px;
}

.node-details-content::-webkit-scrollbar-thumb:hover {
  background: var(--360t-gray-600);
}

/* Content Sections */
.content-section {
  padding: var(--360t-space-5) var(--360t-space-6);
  border-bottom: 1px solid var(--360t-gray-200);
  background-color: var(--360t-white);
  margin-bottom: 0;
  flex-shrink: 0;
}

.content-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.content-section:first-child {
  padding-top: var(--360t-space-6);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--360t-space-4);
}

.section-title {
  font-size: var(--360t-font-size-base);
  font-weight: var(--360t-font-weight-semibold);
  color: var(--360t-gray-800);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  font-family: var(--360t-font-family);
  line-height: var(--360t-line-height-tight);
}

.section-icon {
  color: var(--360t-primary);
  flex-shrink: 0;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--360t-gray-500);
  cursor: pointer;
  border-radius: var(--360t-radius-sm);
  transition: all var(--360t-transition-normal) var(--360t-easing-smooth);
}

.expand-button:hover {
  background-color: var(--360t-gray-100);
  color: var(--360t-primary);
}

.expand-icon {
  transition: transform var(--360t-transition-normal) var(--360t-easing-smooth);
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Text Content */
.text-content {
  font-size: var(--360t-font-size-sm);
  line-height: 1.65; /* improved readability */
  color: var(--360t-gray-800);
  margin: 0;
  max-width: 72ch; /* limit measure for long paragraphs */
}

.text-content.truncated {
  display: -webkit-box;
  -webkit-line-clamp: 4; /* show a bit more before expand */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.show-more-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  margin-top: var(--360t-space-2);
  padding: var(--360t-space-1) var(--360t-space-3);
  background: var(--360t-gray-50);
  border: 1px solid var(--360t-primary);
  color: var(--360t-primary);
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-medium);
  border-radius: var(--360t-radius-md);
  cursor: pointer;
  transition: all var(--360t-transition-normal) var(--360t-easing-smooth);
}

.show-more-button:hover {
  background-color: var(--360t-primary);
  color: var(--360t-white);
}

/* Property Grid */
.property-grid {
  display: grid;
  gap: var(--360t-space-4);
}

.property-item {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-1);
}

.property-label {
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-semibold);
  color: var(--360t-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.property-value {
  font-size: var(--360t-font-size-sm);
  color: var(--360t-gray-800);
  line-height: var(--360t-line-height-normal);
  word-break: break-word;
}

/* Show all button */
.show-all-button-card {
  width: 100%;
  padding: var(--360t-space-2) var(--360t-space-3);
  background: var(--360t-gray-50);
  border: 1px solid var(--360t-gray-200);
  border-radius: 0 0 var(--360t-radius-md) var(--360t-radius-md);
  color: var(--360t-gray-500);
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-medium);
  cursor: pointer;
  transition: all var(--360t-transition-normal);
  text-transform: none;
  letter-spacing: 0;
}

.show-all-button-card:hover {
  background: var(--360t-success-light);
  color: var(--360t-primary);
  border-color: var(--360t-primary);
  transform: translateY(-1px);
}

.show-all-button-card span {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--360t-space-1);
}

/* Labels container */
.labels-container-card {
  padding: var(--360t-space-3) var(--360t-space-4);
  display: flex;
  gap: var(--360t-space-2);
  flex-wrap: wrap;
  background: var(--360t-white);
  border-radius: 0 0 var(--360t-radius-md) var(--360t-radius-md);
}

.label-badge-card {
  background: var(--360t-gray-100);
  color: var(--360t-gray-700);
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-medium);
  padding: var(--360t-space-1) var(--360t-space-2);
  border-radius: 999px;
  border: 1px solid var(--360t-gray-200);
  transition: all var(--360t-transition-normal);
  box-shadow: var(--360t-shadow-sm);
  letter-spacing: 0.01em;
}

.label-badge-card:hover {
  background: var(--360t-primary);
  color: var(--360t-white);
  transform: scale(1.02);
}

/* Relationship Lists */
.relationship-list {
  display: flex;
  flex-direction: column;
  gap: var(--360t-space-3);
}

.relationship-item {
  display: flex;
  align-items: center;
  gap: var(--360t-space-3);
  padding: var(--360t-space-3);
  background-color: var(--360t-gray-100);
  border-radius: var(--360t-radius-lg);
  border: 1px solid var(--360t-gray-200);
  transition: all var(--360t-transition-normal) var(--360t-easing-smooth);
  cursor: pointer;
}

/* Modern relationship item */
.relationship-item-modern {
  margin: 0.25rem 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--360t-gray-200);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--360t-white);
  display: flex;
  align-items: center;
  min-height: 36px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.relationship-item-modern:hover {
  background: var(--360t-gray-50);
  border-color: var(--360t-primary);
  transform: translateX(2px) translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 151, 58, 0.08);
}

.relationship-content {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 0.375rem;
}

/* Relationship type container */
.relationship-type-container {
  flex: 0 0 40%;
  display: flex;
  align-items: center;
  min-width: 0;
}

.relationship-type-compact {
  font-size: 0.75rem;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
  color: var(--360t-gray-700);
  background: var(--360t-gray-50);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid var(--360t-gray-200);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  transition: all 0.2s ease;
  text-align: center;
  letter-spacing: 0.01em;
}

.relationship-type-compact:hover {
  background: var(--360t-primary);
  color: white;
  transform: scale(1.02);
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.2);
}

/* Relationship direction */
.relationship-direction-container {
  flex: 0 0 12%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.relationship-direction {
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
  padding: 0.25rem 0.375rem;
  border-radius: 50%;
  border: 1px solid;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 24px;
  height: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

.relationship-direction.outgoing {
  color: var(--360t-error);
  border-color: var(--360t-error-border);
  background: var(--360t-error-light);
}

.relationship-direction.outgoing:hover {
  background: var(--360t-error-light);
  border-color: var(--360t-error-border);
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.relationship-direction.incoming {
  color: var(--360t-success);
  border-color: var(--360t-success-border);
  background: var(--360t-success-light);
}

.relationship-direction.incoming:hover {
  background: var(--360t-success-light);
  border-color: var(--360t-success-border);
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}

.relationship-item:hover {
  background-color: var(--360t-white);
  border-color: var(--360t-primary);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.relationship-icon {
  font-size: var(--360t-font-size-base);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--360t-white);
  border-radius: var(--360t-radius-md);
}

.relationship-content {
  flex: 1;
  min-width: 0;
}

.relationship-type {
  font-size: var(--360t-font-size-xs);
  font-weight: var(--360t-font-weight-semibold);
  color: var(--360t-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--360t-space-1);
}

.relationship-target {
  font-size: var(--360t-font-size-sm);
  font-weight: var(--360t-font-weight-medium);
  color: var(--360t-gray-800);
  line-height: var(--360t-line-height-tight);
  flex: 0 0 48%;
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  min-width: 0;
  background: var(--360t-gray-50);
  border: 1px solid var(--360t-gray-200);
  border-radius: var(--360t-radius-md);
  padding: var(--360t-space-1) var(--360t-space-2);
  cursor: pointer;
  transition: all var(--360t-transition-normal);
  overflow: hidden;
  box-shadow: var(--360t-shadow-sm);
}

.relationship-target:hover {
  background: var(--360t-success-light);
  border-color: var(--360t-primary);
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0, 151, 58, 0.1);
}

.node-name-modern {
  font-size: var(--360t-font-size-sm);
  color: var(--360t-gray-700);
  font-weight: var(--360t-font-weight-semibold);
  font-family: var(--360t-font-family);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 1;
  min-width: 0;
  letter-spacing: -0.01em;
}

.category-badge-small {
  font-size: var(--360t-font-size-xs);
  font-family: var(--360t-font-family);
  padding: var(--360t-space-1) var(--360t-space-1);
  border-radius: var(--360t-radius-md);
  font-weight: var(--360t-font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.2px;
  flex-shrink: 0;
  margin-left: var(--360t-space-1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: var(--360t-shadow-sm);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--360t-gray-100) 25%, var(--360t-gray-200) 50%, var(--360t-gray-100) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--360t-radius-sm);
}

.skeleton-text {
  height: 16px;
  margin-bottom: var(--360t-space-2);
}

.skeleton-text.large {
  height: 20px;
}

.skeleton-text.small {
  height: 12px;
  width: 60%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Panel footer */
.panel-footer-modern {
  padding: var(--360t-space-3) var(--360t-space-4);
  background: var(--360t-gray-50);
  border-top: 1px solid var(--360t-gray-200);
  text-align: center;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.04);
}

.hint-modern {
  margin: 0;
  font-size: var(--360t-font-size-xs);
  color: var(--360t-gray-500);
  font-style: italic;
  font-weight: var(--360t-font-weight-normal);
  letter-spacing: 0.01em;
}

/* Error message modern */
.error-message-modern {
  background: var(--360t-error-light);
  color: var(--360t-error);
  padding: var(--360t-space-3) var(--360t-space-4);
  border-radius: var(--360t-radius-md);
  border: 1px solid var(--360t-error-border);
  margin: var(--360t-space-3) var(--360t-space-4);
  font-size: var(--360t-font-size-sm);
  font-weight: var(--360t-font-weight-medium);
  letter-spacing: 0.01em;
  box-shadow: 0 1px 2px rgba(220, 38, 38, 0.08);
}

/* Error States */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--360t-space-2);
  padding: var(--360t-space-4);
  background-color: var(--360t-error-light);
  border: 1px solid var(--360t-error-border);
  border-radius: var(--360t-radius-lg);
  color: var(--360t-error);
}

.error-icon {
  color: var(--360t-error);
  flex-shrink: 0;
}

.retry-button {
  padding: var(--360t-space-2) var(--360t-space-4);
  background-color: var(--360t-error);
  color: var(--360t-white);
  border: none;
  border-radius: var(--360t-radius-md);
  font-size: var(--360t-font-size-sm);
  font-weight: var(--360t-font-weight-medium);
  cursor: pointer;
  transition: all var(--360t-transition-normal) var(--360t-easing-smooth);
}

.retry-button:hover {
  background-color: var(--360t-error);
}

/* Resize Handle */
.resize-handle {
  position: absolute;
  left: -4px;
  top: 0;
  bottom: 0;
  width: 8px;
  cursor: ew-resize;
  background: transparent;
  transition: background-color var(--360t-transition-normal) var(--360t-easing-smooth);
}

.resize-handle:hover {
  background-color: var(--360t-primary);
}

.resize-handle.resizing {
  background-color: var(--360t-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .node-details-modern {
    width: var(--panel-width, 35%);
  }
}

@media (max-width: 768px) {
  .node-details-modern {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    top: var(--header-height, 96px);
    height: calc(100vh - var(--header-height, 56px));
    border-left: none;
    border-top: 2px solid var(--360t-gray-200);
    z-index: 98;
  }
  
  .node-details-modern.collapsed {
    width: 100%;
    height: 48px;
    top: auto;
    bottom: 0;
  }

  /* On small screens, keep the collapsed header as a floating pill at bottom-right for easy access */
  .node-details-modern.collapsed .node-details-header {
    position: fixed;
    right: 12px;
    bottom: 12px;
    top: auto;
    width: 44px;
    height: 44px;
    min-height: 44px;
    border: 1px solid var(--360t-gray-200);
    border-radius: 12px;
    box-shadow: var(--360t-shadow-md);
  }
  
  .content-section {
    padding: 1rem;
  }
  
  .node-details-header {
    padding: 1rem;
  }
}

/* Accessibility */
.control-button:focus-visible,
.expand-button:focus-visible,
.show-more-button:focus-visible,
.retry-button:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .node-details-modern {
    border-left-width: 3px;
  }
  
  .relationship-item {
    border-width: 2px;
  }
  
  .section-title {
    font-weight: var(--360t-font-weight-bold);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .node-details-modern,
  .control-button,
  .expand-icon,
  .relationship-item,
  .show-more-button {
    transition: none;
  }
  
  .loading-skeleton {
    animation: none;
  }
}
