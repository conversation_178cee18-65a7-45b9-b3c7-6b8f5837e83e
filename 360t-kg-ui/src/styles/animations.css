/* Animation Utilities and Micro-interactions */

/* Custom Easing Functions */
:root {
  --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --easing-sharp: cubic-bezier(0.4, 0, 0.6, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Duration Tokens */
  --duration-instant: 100ms;
  --duration-quick: 200ms;
  --duration-moderate: 300ms;
  --duration-slow: 500ms;
  --duration-extra-slow: 700ms;
}

/* Base Animation Classes */
.animate-fade-in {
  animation: fadeIn var(--duration-moderate) var(--easing-standard) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--duration-moderate) var(--easing-standard) forwards;
}

.animate-slide-in-right {
  animation: slideInRight var(--duration-moderate) var(--easing-decelerate) forwards;
}

.animate-slide-out-right {
  animation: slideOutRight var(--duration-moderate) var(--easing-accelerate) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-quick) var(--easing-bounce) forwards;
}

.animate-bounce {
  animation: bounce var(--duration-slow) var(--easing-bounce);
}

/* Keyframe Definitions */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Interactive Element Animations */
.interactive-hover {
  transition: all var(--duration-quick) var(--easing-standard);
}

.interactive-hover:hover {
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-lg);
}

.interactive-press {
  transition: all var(--duration-instant) var(--easing-sharp);
}

.interactive-press:active {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--360t-shadow-sm);
}

/* Button Micro-interactions */
.button-modern {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-quick) var(--easing-standard);
}

.button-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left var(--duration-moderate) var(--easing-standard);
}

.button-modern:hover::before {
  left: 100%;
}

.button-modern:hover {
  transform: translateY(-1px);
  box-shadow: var(--360t-shadow-md);
}

.button-modern:active {
  transform: translateY(0);
  box-shadow: var(--360t-shadow-sm);
}

/* Card Hover Effects */
.card-hover {
  transition: all var(--duration-quick) var(--easing-standard);
  transform: translateZ(0); /* Create stacking context */
}

.card-hover:hover {
  transform: translateY(-2px) translateZ(0);
  box-shadow: var(--360t-shadow-xl);
}

/* Expandable Content */
.expandable-content {
  overflow: hidden;
  transition: max-height var(--duration-moderate) var(--easing-decelerate);
}

.expandable-content.collapsed {
  max-height: 0;
}

.expandable-content.expanded {
  max-height: 500px; /* Adjust based on content */
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--360t-light-gray) 25%,
    var(--360t-mid-gray) 50%,
    var(--360t-light-gray) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.loading-pulse {
  animation: pulse 1.5s infinite;
}

.loading-spin {
  animation: spin 1s linear infinite;
}

/* Focus States */
.focus-ring:focus-visible {
  outline: 2px solid var(--360t-primary);
  outline-offset: 2px;
  transition: outline-offset var(--duration-quick) var(--easing-standard);
}

.focus-ring:focus-visible:hover {
  outline-offset: 4px;
}

/* Stagger Animation for Lists */
.stagger-animation > * {
  animation: fadeInUp var(--duration-moderate) var(--easing-decelerate) both;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0ms; }
.stagger-animation > *:nth-child(2) { animation-delay: 50ms; }
.stagger-animation > *:nth-child(3) { animation-delay: 100ms; }
.stagger-animation > *:nth-child(4) { animation-delay: 150ms; }
.stagger-animation > *:nth-child(5) { animation-delay: 200ms; }
.stagger-animation > *:nth-child(n+6) { animation-delay: 250ms; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Panel Slide Animations */
.panel-slide-in {
  animation: panelSlideIn var(--duration-moderate) var(--easing-decelerate) forwards;
}

.panel-slide-out {
  animation: panelSlideOut var(--duration-moderate) var(--easing-accelerate) forwards;
}

@keyframes panelSlideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes panelSlideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

/* Notification Animations */
.notification-enter {
  animation: notificationEnter var(--duration-moderate) var(--easing-bounce) forwards;
}

.notification-exit {
  animation: notificationExit var(--duration-quick) var(--easing-accelerate) forwards;
}

@keyframes notificationEnter {
  from {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes notificationExit {
  from {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
}

/* Graph Node Animations */
.graph-node-enter {
  animation: graphNodeEnter var(--duration-slow) var(--easing-bounce) forwards;
}

.graph-node-highlight {
  animation: graphNodeHighlight var(--duration-moderate) var(--easing-standard) forwards;
}

@keyframes graphNodeEnter {
  from {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes graphNodeHighlight {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.2);
  }
}

/* Accessibility and Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .loading-shimmer,
  .loading-pulse,
  .loading-spin {
    animation: none;
  }
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  .interactive-hover:hover,
  .button-modern:hover,
  .card-hover:hover {
    border: 2px solid currentColor;
  }
}

/* Utility Classes */
.no-animation {
  animation: none !important;
  transition: none !important;
}

.animation-paused {
  animation-play-state: paused;
}

.animation-running {
  animation-play-state: running;
}