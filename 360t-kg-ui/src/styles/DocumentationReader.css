/* Documentation Reader Styles */
.documentation-reader {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  position: relative;
}

.documentation-reader.empty {
  justify-content: center;
  align-items: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h2 {
  color: #334155;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-state p {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.5;
}

/* Reader Header */
.reader-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  min-height: 60px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.breadcrumb-item {
  color: #64748b;
  font-weight: 500;
}

.breadcrumb-item.current {
  color: #334155;
  font-weight: 600;
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: #94a3b8;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: #64748b;
  transition: all 0.2s ease;
  line-height: 1;
}

.close-button:hover {
  background: #e2e8f0;
  color: #334155;
}

/* Table of Contents */
.table-of-contents {
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.table-of-contents.collapsed {
  border-bottom: 1px solid #e2e8f0;
}

.toc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  user-select: none;
}

.table-of-contents h3 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #334155;
}

.toc-toggle {
  background: none;
  border: none;
  font-size: 0.75rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 3px;
  transition: all 0.2s ease;
  line-height: 1;
  min-width: 20px;
  text-align: center;
}

.toc-toggle:hover {
  background: #e2e8f0;
  color: #334155;
}

.toc-nav {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  padding: 0 1.5rem 0.75rem 1.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.toc-more {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: #94a3b8;
  font-style: italic;
  text-align: center;
  margin-top: 0.25rem;
}

.toc-item {
  background: none;
  border: none;
  text-align: left;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  color: #64748b;
  transition: all 0.2s ease;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.toc-item:hover {
  background: #e2e8f0;
  color: #334155;
}

.toc-item.level-1 {
  font-weight: 600;
  padding-left: 0.5rem;
  font-size: 0.85rem;
}

.toc-item.level-2 {
  padding-left: 1rem;
  font-size: 0.8rem;
}

.toc-item.level-3 {
  padding-left: 1.5rem;
  font-size: 0.75rem;
}

.toc-item.level-4,
.toc-item.level-5,
.toc-item.level-6 {
  padding-left: 2rem;
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Content Area */
.reader-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.content-body {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  line-height: 1.7;
  color: #334155;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #0ea5e9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  color: #64748b;
  font-size: 1rem;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.error-state h3 {
  color: #dc2626;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.error-state p {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.retry-button {
  background: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.retry-button:hover {
  background: #0284c7;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 48px;
  height: 48px;
  background: #0ea5e9;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.25rem;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
  transition: all 0.2s ease;
  z-index: 100;
}

.back-to-top:hover {
  background: #0284c7;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
}

/* Markdown Content Styling */
.content-body h1,
.content-body h2,
.content-body h3,
.content-body h4,
.content-body h5,
.content-body h6 {
  color: #1e293b;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.content-body h1 {
  font-size: 2.25rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
  margin-top: 0;
}

.content-body h2 {
  font-size: 1.875rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.375rem;
}

.content-body h3 {
  font-size: 1.5rem;
}

.content-body h4 {
  font-size: 1.25rem;
}

.content-body h5,
.content-body h6 {
  font-size: 1.125rem;
}

.content-body p {
  margin-bottom: 1.25rem;
  line-height: 1.7;
}

.content-body ul,
.content-body ol {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}

.content-body li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.content-body code {
  background: #f1f5f9;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #dc2626;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.content-body pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 1.25rem;
}

.content-body pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.content-body blockquote {
  border-left: 4px solid #0ea5e9;
  padding-left: 1rem;
  margin: 1.25rem 0;
  color: #64748b;
  font-style: italic;
}

.content-body table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.25rem;
}

.content-body th,
.content-body td {
  border: 1px solid #e2e8f0;
  padding: 0.75rem;
  text-align: left;
}

.content-body th {
  background: #f8fafc;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .reader-header {
    padding: 0.75rem 1rem;
  }

  .toc-header {
    padding: 0.5rem 1rem;
  }

  .toc-nav {
    padding: 0 1rem 0.5rem 1rem;
    max-height: 150px;
  }

  .content-body {
    padding: 1.5rem 1rem;
  }

  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 44px;
    height: 44px;
  }
}

/* Scrollbar Styling */
.reader-content::-webkit-scrollbar {
  width: 8px;
}

.reader-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.reader-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.reader-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus States for Accessibility */
.close-button:focus,
.toc-item:focus,
.toc-toggle:focus,
.retry-button:focus,
.back-to-top:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}
