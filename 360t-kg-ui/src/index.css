:root {
  /* Color System */
  --primary-color: #00973A;
  --primary-light: #4abb7d;
  --primary-dark: #007d30;
  --secondary-color: #64748b;
  --background-color: #f8fafc;
  --card-bg: #ffffff;
  --text-color: #1e293b;
  --text-secondary: #475569;
  --text-light: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --success-color: #00973a;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #00973A;
  
  /* Typography System */
  --font-family-primary: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace;
  
  /* Enhanced Border Radius System */
  --border-radius-xs: 0.125rem;  /* 2px */
  --border-radius-sm: 0.25rem;   /* 4px */
  --border-radius: 0.375rem;     /* 6px */
  --border-radius-md: 0.5rem;    /* 8px */
  --border-radius-lg: 0.75rem;   /* 12px */
  --border-radius-xl: 1rem;      /* 16px */
  --border-radius-full: 9999px;
  
  /* Enhanced Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.025);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Transition System */
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
}

body {
  font-family: var(--font-family-primary);
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem; /* 36px */
  font-weight: 700;
  line-height: 1.2;
}

h2 {
  font-size: 1.875rem; /* 30px */
  font-weight: 700;
  line-height: 1.2;
}

h3 {
  font-size: 1.5rem; /* 24px */
  font-weight: 600;
  line-height: 1.3;
}

h4 {
  font-size: 1.25rem; /* 20px */
  font-weight: 600;
  line-height: 1.3;
}

h5 {
  font-size: 1.125rem; /* 18px */
  font-weight: 500;
  line-height: 1.4;
}

h6 {
  font-size: 1rem; /* 16px */
  font-weight: 500;
  line-height: 1.4;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Basic components */
button {
  background: none;
  border: none;
  cursor: pointer;
}

/* Enhanced Button System */
.button, button.primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-lg);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  transition: var(--transition);
  background-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.button:hover, button.primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.button:active, button.primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.button:disabled, button.primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.button:disabled:hover, button.primary:disabled:hover {
  background-color: var(--primary-color);
  transform: none;
  box-shadow: none;
}

/* Button Variants */
.button-secondary {
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.button-secondary:hover {
  background-color: var(--background-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.button-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.button-outline:hover {
  background-color: var(--primary-color);
  color: white;
}

.button-ghost {
  background-color: transparent;
  color: var(--text-secondary);
  box-shadow: none;
}

.button-ghost:hover {
  background-color: var(--background-color);
  color: var(--text-color);
}

/* Button Sizes */
.button-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.button-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Enhanced Input System */
input, textarea, select {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  outline: none;
  transition: var(--transition);
  box-shadow: var(--shadow-xs);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1), var(--shadow-sm);
}

input:hover, textarea:hover, select:hover {
  border-color: var(--text-light);
}

input::placeholder, textarea::placeholder {
  color: var(--text-muted);
}

input:disabled, textarea:disabled, select:disabled {
  background-color: var(--background-color);
  border-color: var(--border-light);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
