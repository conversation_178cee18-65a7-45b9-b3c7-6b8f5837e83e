/**
 * Consolidated graph node and relationship defaults
 * This file serves as the single source of truth for all graph visualization configurations
 * Consolidates duplicate configurations from UnifiedGraphWrapper.jsx and Unified3DGraphWrapper.jsx
 */

// Technical node type colors - unified from 2D and 3D graph wrappers
export const TECHNICAL_TYPE_COLORS = {
  'Module': '#4f46e5',           // Deep indigo - consistent across 2D/3D
  'Product': '#059669',          // Deep emerald - consistent across 2D/3D  
  'Workflow': '#dc2626',         // Deep red - 2D version (3D was #d97706)
  'UI_Area': '#7c3aed',          // Deep violet - consistent across 2D/3D
  'ConfigurationItem': '#ea580c', // Deep orange - 2D version (3D was #db2777)
  'TestCase': '#0891b2',         // Deep cyan - 2D version (3D was #dc2626)  
  'Document': '#be123c',         // Deep rose - 2D version (3D was #f59e0b)
  'Default': '#6b7280'           // Gray - standardized (was #4b5563 in 3D)
};

// 2D node sizes - varied sizes for visual hierarchy
export const DEFAULT_2D_NODE_SIZES = {
  'Module': 25,
  'Product': 30,
  'Workflow': 20,
  'UI_Area': 15,
  'ConfigurationItem': 18,
  'TestCase': 22,
  'Document': 28,
  'Default': 20
};

// 3D node sizes - uniform sizing for better 3D performance
export const DEFAULT_3D_NODE_SIZES = {
  'Module': 1,
  'Product': 1,
  'Workflow': 1,
  'UI_Area': 1,
  'ConfigurationItem': 1,
  'TestCase': 1,
  'Document': 1,
  'Default': 1
};

// Business category colors for Legend.jsx (separate from technical types)
export const BUSINESS_CATEGORY_COLORS = {
  'MMC': '#e91e63',    // Bright pink - Market Making
  'SEP': '#9c27b0',    // Bright purple - Settlement
  'BA-PWD': '#6b7280', // Dark gray - Business App Password
  'ECN': '#6b7280',    // Dark gray - Electronic Communication Network
  'FUT': '#6b7280',    // Dark gray - Futures
  'BA-DD': '#6b7280',  // Dark gray - Business App Due Diligence
  'PS-MAP': '#6b7280', // Dark gray - Position Mapping
  'ISIN': '#6b7280',   // Dark gray - International Securities ID
  'BA-SP': '#6b7280',  // Dark gray - Business App Special Processing
  'Default': '#6b7280' // Neutral gray
};

// Business category sizes for Legend.jsx
export const BUSINESS_CATEGORY_SIZES = {
  'MMC': 20,
  'SEP': 18,
  'BA-PWD': 18,
  'ECN': 18,
  'FUT': 18,
  'BA-DD': 18,
  'PS-MAP': 18,
  'ISIN': 18,
  'BA-SP': 18,
  'Default': 16
};

// Default relationship colors - for edge styling
export const DEFAULT_RELATIONSHIP_COLORS = {
  'USES': '#8b5cf6',           // Purple
  'CONTAINS': '#06b6d4',       // Cyan  
  'DEPENDS_ON': '#f59e0b',     // Amber
  'IMPLEMENTS': '#10b981',     // Emerald
  'CONNECTS_TO': '#ef4444',    // Red
  'REFERENCES': '#6366f1',     // Indigo
  'SIMILAR_TO': '#ec4899',     // Pink
  'Default': '#9ca3af'         // Gray
};

// 3D node shapes - for 3D rendering variety
export const DEFAULT_3D_NODE_SHAPES = {
  'Module': 'sphere',
  'Product': 'box',
  'Workflow': 'cone',
  'UI_Area': 'cylinder',
  'ConfigurationItem': 'octahedron',
  'TestCase': 'tetrahedron',
  'Document': 'plane',
  'Default': 'sphere'
};

// Backward compatibility exports
export const DEFAULT_NODE_COLORS = TECHNICAL_TYPE_COLORS;
export const DEFAULT_NODE_SIZES = DEFAULT_2D_NODE_SIZES;

/**
 * Get node color by type with fallback
 * @param {string} nodeType - Node type or category
 * @param {Object} customColors - Custom color overrides
 * @returns {string} Hex color code
 */
export const getNodeColor = (nodeType, customColors = {}) => {
  if (customColors[nodeType]) return customColors[nodeType];
  if (TECHNICAL_TYPE_COLORS[nodeType]) return TECHNICAL_TYPE_COLORS[nodeType];
  if (BUSINESS_CATEGORY_COLORS[nodeType]) return BUSINESS_CATEGORY_COLORS[nodeType];
  return TECHNICAL_TYPE_COLORS.Default;
};

/**
 * Get node size by type with fallback  
 * @param {string} nodeType - Node type or category
 * @param {boolean} is3D - Whether this is for 3D rendering
 * @param {Object} customSizes - Custom size overrides
 * @returns {number} Node size
 */
export const getNodeSize = (nodeType, is3D = false, customSizes = {}) => {
  if (customSizes[nodeType]) return customSizes[nodeType];
  
  const sizeMap = is3D ? DEFAULT_3D_NODE_SIZES : DEFAULT_2D_NODE_SIZES;
  if (sizeMap[nodeType]) return sizeMap[nodeType];
  
  // Fallback to business categories
  if (BUSINESS_CATEGORY_SIZES[nodeType]) return BUSINESS_CATEGORY_SIZES[nodeType];
  
  return sizeMap.Default;
};

/**
 * Get 3D node shape by type with fallback
 * @param {string} nodeType - Node type
 * @returns {string} Shape name for 3D rendering
 */
export const getNodeShape = (nodeType) => {
  return DEFAULT_3D_NODE_SHAPES[nodeType] || DEFAULT_3D_NODE_SHAPES.Default;
};

/**
 * Get relationship color by type with fallback
 * @param {string} relationshipType - Relationship type
 * @param {Object} customColors - Custom color overrides
 * @returns {string} Hex color code
 */
export const getRelationshipColor = (relationshipType, customColors = {}) => {
  if (customColors[relationshipType]) return customColors[relationshipType];
  return DEFAULT_RELATIONSHIP_COLORS[relationshipType] || DEFAULT_RELATIONSHIP_COLORS.Default;
};