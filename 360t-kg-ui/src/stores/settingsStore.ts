import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { DEFAULT_SETTINGS } from '../services/settingsService.js';

// TypeScript interfaces for settings structure
export interface NodeColors {
  [key: string]: string;
}

export interface NodeSizes {
  [key: string]: number;
}

export interface NodeShapes {
  [key: string]: string;
}

export interface RelationshipColors {
  [key: string]: string;
}

export interface RelationshipLineStyles {
  [key: string]: string;
}

export interface UISettings {
  showLegend: boolean;
  legendPosition: 'left' | 'right' | 'top' | 'bottom';
  showTooltips: boolean;
  showNodeLabels: boolean;
  showRelationshipLabels: boolean;
  theme: 'light' | 'dark';
  compactMode: boolean;
}

export interface PhysicsSettings {
  enabled: boolean;
  strength: number;
  centralGravity: number;
  springLength: number;
  springConstant: number;
  damping: number;
}

export interface ZoomSettings {
  min: number;
  max: number;
  default: number;
  current: number;
}

export interface CenterSettings {
  x: number;
  y: number;
}

export interface LayoutSettings {
  physics: PhysicsSettings;
  zoom: ZoomSettings;
  center: CenterSettings;
}

export interface SearchSettings {
  history: string[];
  maxHistoryItems: number;
  caseSensitive: boolean;
  useRegex: boolean;
  highlightResults: boolean;
}

export interface PerformanceSettings {
  maxNodes2D: number;
  maxNodes3D: number;
  maxRelationships: number;
  enableClustering: boolean;
  clusterThreshold: number;
  animationDuration: number;
}

export interface ExportSettings {
  includeLayout: boolean;
  includeColors: boolean;
  includeFilters: boolean;
  format: 'json' | 'csv' | 'graphml';
}

export interface GraphitiSettings {
  searchType: string;
  diversityFactor: number;
  edgeCount: number;
  nodeCount: number;
  llmProvider: string;
  ollamaModel: string;
  ollamaUrl: string;
  temperature: number;
  timeout: number;
  enableFallback: boolean;
  enableCaching: boolean;
  logLevel: string;
}

export interface SettingsState {
  version: string;
  nodeColors: NodeColors;
  nodeSizes: NodeSizes;
  nodeShapes: NodeShapes;
  relationshipColors: RelationshipColors;
  relationshipLineStyles: RelationshipLineStyles;
  ui: UISettings;
  layout: LayoutSettings;
  search: SearchSettings;
  performance: PerformanceSettings;
  export: ExportSettings;
  graphiti: GraphitiSettings;
}

export interface SettingsStore extends SettingsState {
  // Getters
  get: (path: string) => any;
  getAll: () => SettingsState;
  
  // Setters
  set: (path: string, value: any) => void;
  update: (newSettings: Partial<SettingsState>) => void;
  reset: (section?: keyof SettingsState) => void;
  
  // Import/Export
  export: (includeDefaults?: boolean) => any;
  import: (settingsData: any) => boolean;
  
  // Migration
  migrateFromLegacy: (legacyData: any) => SettingsState;
  
  // Validation
  validate: (settings: any) => boolean;
}

// Storage keys (matching legacy service)
const STORAGE_KEYS = {
  MAIN_SETTINGS: 'kg-visualizer-settings',
  BACKUP_SETTINGS: 'kg-visualizer-settings-backup',
};

// Migration utilities
const migrateLegacySettings = (legacyData: any): Partial<SettingsState> => {
  const migrated: Partial<SettingsState> = {};
  
  if (!legacyData || typeof legacyData !== 'object') {
    return migrated;
  }
  
  // Migrate node appearance settings
  if (legacyData.nodeColors) migrated.nodeColors = legacyData.nodeColors;
  if (legacyData.nodeSizes) migrated.nodeSizes = legacyData.nodeSizes;
  if (legacyData.nodeShapes) migrated.nodeShapes = legacyData.nodeShapes;
  
  // Migrate relationship settings
  if (legacyData.relationshipColors) migrated.relationshipColors = legacyData.relationshipColors;
  if (legacyData.relationshipLineStyles) migrated.relationshipLineStyles = legacyData.relationshipLineStyles;
  
  // Migrate UI settings
  if (legacyData.ui) migrated.ui = { ...DEFAULT_SETTINGS.ui, ...legacyData.ui };
  
  // Migrate layout settings
  if (legacyData.layout) migrated.layout = { ...DEFAULT_SETTINGS.layout, ...legacyData.layout };
  
  // Migrate search settings
  if (legacyData.search) migrated.search = { ...DEFAULT_SETTINGS.search, ...legacyData.search };
  
  // Migrate performance settings
  if (legacyData.performance) migrated.performance = { ...DEFAULT_SETTINGS.performance, ...legacyData.performance };
  
  // Migrate export settings
  if (legacyData.export) migrated.export = { ...DEFAULT_SETTINGS.export, ...legacyData.export };
  
  // Migrate graphiti settings
  if (legacyData.graphiti) migrated.graphiti = { ...DEFAULT_SETTINGS.graphiti, ...legacyData.graphiti };
  
  return migrated;
};

// Validation utilities
const validateSettings = (settings: any): boolean => {
  if (!settings || typeof settings !== 'object') {
    return false;
  }
  
  // Basic structure validation
  const requiredSections = ['nodeColors', 'nodeSizes', 'nodeShapes', 'relationshipColors'];
  for (const section of requiredSections) {
    if (settings[section] && typeof settings[section] !== 'object') {
      return false;
    }
  }
  
  return true;
};

// Deep merge utility
const deepMerge = (target: any, source: any): any => {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
};

// Nested value utilities
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

const setNestedValue = (obj: any, path: string, value: any): void => {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  if (lastKey) {
    target[lastKey] = value;
  }
};

// Create the Zustand store
export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      // Initial state from DEFAULT_SETTINGS
      ...DEFAULT_SETTINGS,
      
      // Getters
      get: (path: string) => getNestedValue(get(), path),
      
      getAll: () => {
        const state = get();
        return {
          version: state.version,
          nodeColors: state.nodeColors,
          nodeSizes: state.nodeSizes,
          nodeShapes: state.nodeShapes,
          relationshipColors: state.relationshipColors,
          relationshipLineStyles: state.relationshipLineStyles,
          ui: state.ui,
          layout: state.layout,
          search: state.search,
          performance: state.performance,
          export: state.export,
          graphiti: state.graphiti,
        };
      },
      
      // Setters
      set: (path: string, value: any) => {
        set((state) => {
          const newState = { ...state };
          setNestedValue(newState, path, value);
          return newState;
        });
      },
      
      update: (newSettings: Partial<SettingsState>) => {
        set((state) => deepMerge(state, newSettings));
      },
      
      reset: (section?: keyof SettingsState) => {
        set((state) => {
          if (section) {
            return {
              ...state,
              [section]: DEFAULT_SETTINGS[section],
            };
          }
          return { ...DEFAULT_SETTINGS };
        });
      },
      
      // Import/Export
      export: (includeDefaults = false) => {
        const state = get();
        if (!includeDefaults) {
          // Only export non-default values
          const differences: any = {};
          const findDifferences = (current: any, defaults: any, path = '') => {
            for (const key in current) {
              const currentPath = path ? `${path}.${key}` : key;
              
              if (current[key] && typeof current[key] === 'object' && !Array.isArray(current[key])) {
                if (defaults[key] && typeof defaults[key] === 'object') {
                  findDifferences(current[key], defaults[key], currentPath);
                } else {
                  setNestedValue(differences, currentPath, current[key]);
                }
              } else if (current[key] !== defaults[key]) {
                setNestedValue(differences, currentPath, current[key]);
              }
            }
          };
          
          findDifferences(state, DEFAULT_SETTINGS);
          return differences;
        }
        
        return {
          ...state,
          exportedAt: new Date().toISOString(),
          exportedBy: 'kg-visualizer',
        };
      },
      
      import: (settingsData: any): boolean => {
        try {
          if (!validateSettings(settingsData)) {
            throw new Error('Invalid settings data');
          }
          
          set((state) => deepMerge(state, settingsData));
          return true;
        } catch (error) {
          console.error('Failed to import settings:', error);
          return false;
        }