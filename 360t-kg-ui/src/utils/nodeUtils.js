/**
 * Consolidated node utility functions
 * This file centralizes all duplicate node processing logic from graph components
 * Provides consistent behavior across 2D, 3D, and legacy graph implementations
 */

import {
  TECHNICAL_TYPE_COLORS,
  DEFAULT_2D_NODE_SIZES,
  DEFAULT_3D_NODE_SHAPES,
  DEFAULT_RELATIONSHIP_COLORS,
  getNodeColor as getNodeColorFromDefaults,
  getNodeSize as getNodeSizeFromDefaults,
  getNodeShape as getNodeShapeFromDefaults,
  getRelationshipColor as getRelationshipColorFromDefaults
} from '../constants/graphDefaults.js';

/**
 * Extract node type with intelligent priority fallback
 * Priority: component category > properties.category > labels[0] > 'Unknown'
 * @param {Object} node - Node data object
 * @returns {string} Node type
 */
export const getNodeType = (node) => {
  try {
    // Priority 1: Use component category from Neo4j properties
    if (node?.properties?.category) {
      return node.properties.category;
    }
    
    // Priority 2: Use business category from properties  
    if (node?.properties?.business_category) {
      return node.properties.business_category;
    }
    
    // Priority 3: Fallback to existing node type logic
    if (node?.labels && Array.isArray(node.labels) && node.labels.length > 0) {
      return node.labels[0];
    }
    
    // Priority 4: Use node type property
    if (node?.type) {
      return node.type;
    }
    
    // Priority 5: Use element_type for compatibility
    if (node?.properties?.element_type) {
      return node.properties.element_type;
    }
    
    return 'Unknown';
  } catch (error) {
    console.warn('Error determining node type:', error);
    return 'Unknown';
  }
};

/**
 * Extract display name from node with smart cleaning
 * Handles various name formats and removes verbose prefixes
 * @param {Object} node - Node data object
 * @returns {string} Clean display name
 */
export const getNodeName = (node) => {
  try {
    // Priority order for name extraction
    const nameFields = [
      'name',
      'title', 
      'display_name',
      'properties.name',
      'properties.title',
      'properties.display_name',
      'label'
    ];
    
    let name = null;
    
    // Extract name using priority order
    for (const field of nameFields) {
      if (field.includes('.')) {
        const [obj, prop] = field.split('.');
        name = node?.[obj]?.[prop];
      } else {
        name = node?.[field];
      }
      
      if (name && typeof name === 'string' && name.trim()) {
        break;
      }
    }
    
    if (!name) {
      return `Node ${node.id || 'Unknown'}`;
    }
    
    // Clean the name
    return cleanNodeName(name);
  } catch (error) {
    console.warn('Error extracting node name:', error);
    return `Node ${node.id || 'Unknown'}`;
  }
};

/**
 * Clean node name by removing verbose prefixes and formatting
 * @param {string} name - Raw node name
 * @returns {string} Cleaned name
 */
export const cleanNodeName = (name) => {
  if (!name || typeof name !== 'string') return name;
  
  // Remove common verbose prefixes
  const prefixesToRemove = [
    'Component: ',
    'Module: ',
    'Product: ',
    'Workflow: ',
    'Document: ',
    'Entity: ',
    'Node: '
  ];
  
  let cleaned = name.trim();
  
  for (const prefix of prefixesToRemove) {
    if (cleaned.startsWith(prefix)) {
      cleaned = cleaned.substring(prefix.length);
      break;
    }
  }
  
  return cleaned;
};

/**
 * Get node color with configuration override support
 * @param {Object} node - Node data object
 * @param {Object} customConfig - Custom color configuration
 * @returns {string} Hex color code
 */
export const getNodeColor = (node, customConfig = {}) => {
  try {
    const nodeType = getNodeType(node);
    
    // Check custom configuration first
    if (customConfig.nodeColors && customConfig.nodeColors[nodeType]) {
      return customConfig.nodeColors[nodeType];
    }
    
    // Use centralized color defaults
    return getNodeColorFromDefaults(nodeType);
  } catch (error) {
    console.warn('Error getting node color:', error);
    return TECHNICAL_TYPE_COLORS.Default;
  }
};

/**
 * Get node size with configuration override support  
 * @param {Object} node - Node data object
 * @param {boolean} is3D - Whether this is for 3D rendering
 * @param {Object} customConfig - Custom size configuration
 * @returns {number} Node size
 */
export const getNodeSize = (node, is3D = false, customConfig = {}) => {
  try {
    const nodeType = getNodeType(node);
    
    // Check custom configuration first
    if (customConfig.nodeSizes && customConfig.nodeSizes[nodeType]) {
      return customConfig.nodeSizes[nodeType];
    }
    
    // Use centralized size defaults
    return getNodeSizeFromDefaults(nodeType, is3D);
  } catch (error) {
    console.warn('Error getting node size:', error);
    return is3D ? 1 : DEFAULT_2D_NODE_SIZES.Default;
  }
};

/**
 * Get 3D node shape for rendering variety
 * @param {Object} node - Node data object  
 * @returns {string} Shape name for 3D rendering
 */
export const getNodeShape = (node) => {
  try {
    const nodeType = getNodeType(node);
    return getNodeShapeFromDefaults(nodeType);
  } catch (error) {
    console.warn('Error getting node shape:', error);
    return DEFAULT_3D_NODE_SHAPES.Default;
  }
};

/**
 * Get relationship color with configuration support
 * @param {Object} link - Link/relationship data object
 * @param {Object} customConfig - Custom color configuration
 * @returns {string} Hex color code
 */
export const getLinkColor = (link, customConfig = {}) => {
  try {
    const relationshipType = link?.type || link?.relationship || 'Default';
    
    // Check custom configuration first
    if (customConfig.relationshipColors && customConfig.relationshipColors[relationshipType]) {
      return customConfig.relationshipColors[relationshipType];
    }
    
    // Use centralized relationship color defaults
    return getRelationshipColorFromDefaults(relationshipType);
  } catch (error) {
    console.warn('Error getting link color:', error);
    return DEFAULT_RELATIONSHIP_COLORS.Default;
  }
};

/**
 * Get link dash array for different relationship types
 * @param {Object} link - Link data object
 * @returns {string} SVG dash array or null for solid lines
 */
export const getLinkDashArray = (link) => {
  try {
    const relationshipType = link?.type || link?.relationship;
    
    // Different line styles for different relationships
    switch (relationshipType) {
      case 'SIMILAR_TO':
        return '5,5'; // Dashed line
      case 'DEPENDS_ON':  
        return '10,2,2,2'; // Dash-dot pattern
      case 'REFERENCES':
        return '3,3'; // Short dashes
      default:
        return null; // Solid line
    }
  } catch (error) {
    console.warn('Error getting link dash array:', error);
    return null;
  }
};

/**
 * Check if node is a document type
 * @param {Object} node - Node data object
 * @returns {boolean} True if document node
 */
export const isDocumentNode = (node) => {
  const nodeType = getNodeType(node);
  return nodeType === 'Document' || 
         nodeType === 'document' ||
         node?.properties?.type === 'document' ||
         node?.labels?.includes('Document');
};

/**
 * Check if node is an entity type  
 * @param {Object} node - Node data object
 * @returns {boolean} True if entity node
 */
export const isEntityNode = (node) => {
  const nodeType = getNodeType(node);
  return nodeType === 'Entity' ||
         nodeType === 'entity' ||
         node?.properties?.type === 'entity' ||
         node?.labels?.includes('Entity');
};

/**
 * Generate tooltip content for document nodes
 * @param {Object} node - Document node data
 * @returns {string} HTML tooltip content
 */
export const getDocumentTooltipContent = (node) => {
  try {
    const name = getNodeName(node);
    const nodeType = getNodeType(node);
    const properties = node.properties || {};
    
    let tooltip = `<strong>${name}</strong><br/>`;
    tooltip += `<em>Type: ${nodeType}</em><br/>`;
    
    // Add document-specific properties
    if (properties.source) {
      tooltip += `<strong>Source:</strong> ${properties.source}<br/>`;
    }
    
    if (properties.summary) {
      tooltip += `<strong>Summary:</strong> ${properties.summary.substring(0, 100)}...<br/>`;
    }
    
    if (properties.page_count) {
      tooltip += `<strong>Pages:</strong> ${properties.page_count}<br/>`;
    }
    
    return tooltip;
  } catch (error) {
    console.warn('Error generating document tooltip:', error);
    return `<strong>${getNodeName(node)}</strong>`;
  }
};

/**
 * Generate tooltip content for entity nodes
 * @param {Object} node - Entity node data
 * @returns {string} HTML tooltip content  
 */
export const getEntityTooltipContent = (node) => {
  try {
    const name = getNodeName(node);
    const nodeType = getNodeType(node);
    const properties = node.properties || {};
    
    let tooltip = `<strong>${name}</strong><br/>`;
    tooltip += `<em>Type: ${nodeType}</em><br/>`;
    
    // Add entity-specific properties
    if (properties.description) {
      tooltip += `<strong>Description:</strong> ${properties.description.substring(0, 100)}...<br/>`;
    }
    
    if (properties.category) {
      tooltip += `<strong>Category:</strong> ${properties.category}<br/>`;
    }
    
    if (properties.confidence) {
      tooltip += `<strong>Confidence:</strong> ${Math.round(properties.confidence * 100)}%<br/>`;
    }
    
    return tooltip;
  } catch (error) {
    console.warn('Error generating entity tooltip:', error);
    return `<strong>${getNodeName(node)}</strong>`;
  }
};

/**
 * Generate tooltip content for relationships/links
 * @param {Object} link - Link data object
 * @returns {string} HTML tooltip content
 */
export const getRelationshipTooltipContent = (link) => {
  try {
    const relationshipType = link?.type || link?.relationship || 'Unknown';
    const properties = link.properties || {};
    
    let tooltip = `<strong>${relationshipType}</strong><br/>`;
    
    if (properties.confidence) {
      tooltip += `<strong>Confidence:</strong> ${Math.round(properties.confidence * 100)}%<br/>`;
    }
    
    if (properties.weight) {
      tooltip += `<strong>Weight:</strong> ${properties.weight}<br/>`;
    }
    
    if (properties.description) {
      tooltip += `<strong>Description:</strong> ${properties.description.substring(0, 100)}...<br/>`;
    }
    
    return tooltip;
  } catch (error) {
    console.warn('Error generating relationship tooltip:', error);
    return `<strong>${link?.type || 'Relationship'}</strong>`;
  }
};

/**
 * Generate comprehensive node tooltip with summary
 * @param {Object} node - Node data object
 * @returns {string} HTML tooltip content
 */
export const getNodeTooltip = (node) => {
  try {
    if (isDocumentNode(node)) {
      return getDocumentTooltipContent(node);
    }
    
    if (isEntityNode(node)) {
      return getEntityTooltipContent(node);
    }
    
    return getGeneralNodeTooltipContent(node);
  } catch (error) {
    console.warn('Error generating node tooltip:', error);
    return `<strong>${getNodeName(node)}</strong>`;
  }
};

/**
 * Generate general tooltip content for any node type
 * @param {Object} node - Node data object
 * @returns {string} HTML tooltip content
 */
export const getGeneralNodeTooltipContent = (node) => {
  try {
    const name = getNodeName(node);
    const nodeType = getNodeType(node);
    const properties = node.properties || {};
    
    let tooltip = `<strong>${name}</strong><br/>`;
    tooltip += `<em>Type: ${nodeType}</em><br/>`;
    
    // Add any relevant properties
    if (properties.description) {
      tooltip += `<strong>Description:</strong> ${properties.description.substring(0, 100)}...<br/>`;
    }
    
    if (node.id) {
      tooltip += `<strong>ID:</strong> ${node.id}<br/>`;
    }
    
    // Add connection count if available
    if (properties.connection_count) {
      tooltip += `<strong>Connections:</strong> ${properties.connection_count}<br/>`;
    }
    
    return tooltip;
  } catch (error) {
    console.warn('Error generating general tooltip:', error);
    return `<strong>${getNodeName(node)}</strong>`;
  }
};

/**
 * Calculate adaptive text size based on zoom level
 * @param {number} zoomLevel - Current zoom level (1.0 = 100%)
 * @param {number} baseSize - Base text size in pixels
 * @returns {number} Adjusted text size
 */
export const getAdaptiveTextSize = (zoomLevel, baseSize = 12) => {
  // Ensure text remains readable at all zoom levels
  const minSize = 8;
  const maxSize = 20;
  
  const adjustedSize = baseSize / Math.sqrt(zoomLevel);
  return Math.max(minSize, Math.min(maxSize, adjustedSize));
};

/**
 * Calculate adaptive line width based on zoom level
 * @param {number} zoomLevel - Current zoom level (1.0 = 100%)  
 * @param {number} baseWidth - Base line width in pixels
 * @returns {number} Adjusted line width
 */
export const getAdaptiveLineWidth = (zoomLevel, baseWidth = 1) => {
  // Keep lines visible but not overwhelming
  const minWidth = 0.5;
  const maxWidth = 4;
  
  const adjustedWidth = baseWidth / zoomLevel;
  return Math.max(minWidth, Math.min(maxWidth, adjustedWidth));
};