/**
 * Text Formatting Utility for Knowledge Graph Node Details
 * Handles identification and formatting of relationship terms in node descriptions
 */

import React from 'react';

// Define comprehensive relationship types and domain terms for formatting
const RELATIONSHIP_TYPES = [
  'CONTAINS', 'DISPLAYS', 'USES', 'CONFIGURES_IN', 'REQUIRES', 'VALIDATES',
  'NAVIGATES_TO', 'DEPENDS_ON', 'TRIGGERS', 'HAS_PARAMETER', 'ASSIGNED_TO',
  'APPROVES', 'RELATES_TO', 'PART_OF', 'OPPOSITE_OF', 'PARTICIPATES_IN',
  'APPLIES_TO', 'MENTIONS', 'REFERENCES', 'IMPLEMENTS', 'EXTENDS'
];

const DOMAIN_TERMS = {
  // Core trading terms
  trading: ['spot', 'margin', 'futures', 'forex', 'limit', 'counterparty', 'credit', 'risk', 
           'settlement', 'liquidity', 'volatility', 'spread', 'hedging', 'position', 'order',
           'execution', 'pricing', 'compliance'],
  
  // System/Platform terms
  system: ['workflow', 'system', 'module', 'product', 'market', 'data', 'reporting',
          'integration', 'configuration', 'monitoring', 'alert', 'authentication', 'permission'],
  
  // Entity/Organizational terms
  entities: ['user', 'group', 'country', 'rating', 'entity', 'relationship', 'transaction',
            'portfolio', 'exposure'],
  
  // 360T Platform specific terms
  platform: ['limit_management', 'trade_management', 'pre_trade', 'post_trade', 'LM', 'TM']
};

// Flatten all domain terms into a single array
const ALL_DOMAIN_TERMS = Object.values(DOMAIN_TERMS).flat();

/**
 * Creates a comprehensive term dictionary for formatting
 * @param {Array} relationships - Array of relationship objects from node data
 * @returns {Set} Set of terms that should be formatted
 */
export function createTermDictionary(relationships = []) {
  const terms = new Set();
  
  // Add all relationship types
  RELATIONSHIP_TYPES.forEach(type => {
    terms.add(type.toLowerCase());
    terms.add(type.replace(/_/g, ' ').toLowerCase());
  });
  
  // Add all domain terms
  ALL_DOMAIN_TERMS.forEach(term => {
    terms.add(term.toLowerCase());
    terms.add(term.replace(/_/g, ' ').toLowerCase());
  });
  
  // Add relationship-specific terms from actual node data
  relationships.forEach(rel => {
    if (rel.type) {
      terms.add(rel.type.toLowerCase());
      terms.add(rel.type.replace(/_/g, ' ').toLowerCase());
    }
    if (rel.properties?.name) {
      terms.add(rel.properties.name.toLowerCase());
    }
    if (rel.node?.properties?.name) {
      terms.add(rel.node.properties.name.toLowerCase());
    }
  });
  
  return terms;
}

/**
 * Formats text by identifying and styling relationship terms
 * @param {string} text - The text to format
 * @param {Array} relationships - Array of relationship objects from node data
 * @param {Function} onTermClick - Callback function for term click events
 * @returns {React.Element} Formatted text with styled terms
 */
export function formatTextWithTerms(text, relationships = [], onTermClick = null) {
  if (!text || typeof text !== 'string') {
    return text;
  }
  
  const termDictionary = createTermDictionary(relationships);
  const words = text.split(/(\s+)/); // Split on whitespace but preserve it
  
  return words.map((word, index) => {
    // Clean the word for matching (remove punctuation)
    const cleanWord = word.toLowerCase().replace(/[^\w\s]/g, '');
    
    // Check if this word or phrase should be formatted
    if (termDictionary.has(cleanWord)) {
      const isRelationshipType = RELATIONSHIP_TYPES.some(type => 
        type.toLowerCase() === cleanWord || type.replace(/_/g, ' ').toLowerCase() === cleanWord
      );
      
      if (isRelationshipType) {
        return (
          <span 
            key={index} 
            className="relationship-term relationship-type"
            onClick={() => onTermClick && onTermClick(cleanWord, 'relationship')}
            title={`Relationship type: ${cleanWord}`}
          >
            {word}
          </span>
        );
      } else {
        return (
          <span 
            key={index} 
            className="relationship-term domain-term"
            onClick={() => onTermClick && onTermClick(cleanWord, 'domain')}
            title={`Domain term: ${cleanWord}`}
          >
            {word}
          </span>
        );
      }
    }
    
    return word;
  });
}

/**
 * Helper function to generate plural/singular variations of a word
 * @param {string} word - The word to generate variations for
 * @returns {Array} Array of word variations
 */
function generateWordVariations(word) {
  const variations = [word];
  const lowerWord = word.toLowerCase();

  // Generate plural forms
  if (lowerWord.endsWith('y') && lowerWord.length > 1 && !'aeiou'.includes(lowerWord[lowerWord.length - 2])) {
    // Words ending in consonant + y: party -> parties
    variations.push(lowerWord.slice(0, -1) + 'ies');
  } else if (lowerWord.endsWith('s') || lowerWord.endsWith('sh') || lowerWord.endsWith('ch') ||
             lowerWord.endsWith('x') || lowerWord.endsWith('z')) {
    // Words ending in s, sh, ch, x, z: class -> classes
    variations.push(lowerWord + 'es');
  } else if (lowerWord.endsWith('f')) {
    // Words ending in f: leaf -> leaves
    variations.push(lowerWord.slice(0, -1) + 'ves');
  } else if (lowerWord.endsWith('fe')) {
    // Words ending in fe: knife -> knives
    variations.push(lowerWord.slice(0, -2) + 'ves');
  } else {
    // Regular plural: add 's'
    variations.push(lowerWord + 's');
  }

  // Generate singular forms (reverse of above)
  if (lowerWord.endsWith('ies') && lowerWord.length > 3) {
    // parties -> party
    variations.push(lowerWord.slice(0, -3) + 'y');
  } else if (lowerWord.endsWith('ves') && lowerWord.length > 3) {
    // leaves -> leaf, knives -> knife
    const base = lowerWord.slice(0, -3);
    variations.push(base + 'f');
    variations.push(base + 'fe');
  } else if (lowerWord.endsWith('es') && lowerWord.length > 2) {
    // classes -> class (but be careful not to over-trim)
    const withoutEs = lowerWord.slice(0, -2);
    if (withoutEs.endsWith('s') || withoutEs.endsWith('sh') || withoutEs.endsWith('ch') ||
        withoutEs.endsWith('x') || withoutEs.endsWith('z')) {
      variations.push(withoutEs);
    }
  } else if (lowerWord.endsWith('s') && lowerWord.length > 1) {
    // Regular plural: margins -> margin
    variations.push(lowerWord.slice(0, -1));
  }

  return [...new Set(variations)]; // Remove duplicates
}

/**
 * Enhanced text formatting that creates clickable links for related nodes
 * @param {string} text - The text to format
 * @param {Array} relationships - Array of relationship objects from node data
 * @param {Function} onNodeClick - Callback function for node click events
 * @returns {React.Element} Formatted text with clickable node links
 */
export function formatTextWithNodeLinks(text, relationships = [], onNodeClick = null) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  // Create comprehensive maps for node name matching
  const nodeNameMap = new Map(); // Maps normalized variations to original node
  const allPatterns = []; // All patterns to search for

  relationships.forEach(rel => {
    if (rel.node?.properties?.name) {
      const nodeName = rel.node.properties.name.trim();

      // Handle multi-word node names
      const words = nodeName.toLowerCase().split(/\s+/);

      // For single words, generate plural/singular variations
      if (words.length === 1) {
        const variations = generateWordVariations(words[0]);
        variations.forEach(variation => {
          nodeNameMap.set(variation, rel.node);
          allPatterns.push({
            pattern: variation,
            original: nodeName,
            node: rel.node,
            length: variation.length
          });
        });
      } else {
        // For multi-word names, add the exact phrase and variations of individual words
        const normalizedFullName = nodeName.toLowerCase();
        nodeNameMap.set(normalizedFullName, rel.node);
        allPatterns.push({
          pattern: normalizedFullName,
          original: nodeName,
          node: rel.node,
          length: normalizedFullName.length
        });

        // Also try variations where individual words might be plural/singular
        const wordVariations = words.map(word => generateWordVariations(word));

        // Generate combinations (this can get complex, so we'll do a simple approach)
        // For now, let's handle the most common case: last word plural/singular
        if (words.length >= 2) {
          const lastWordVariations = generateWordVariations(words[words.length - 1]);
          lastWordVariations.forEach(lastVariation => {
            if (lastVariation !== words[words.length - 1]) {
              const variantPhrase = [...words.slice(0, -1), lastVariation].join(' ');
              nodeNameMap.set(variantPhrase, rel.node);
              allPatterns.push({
                pattern: variantPhrase,
                original: nodeName,
                node: rel.node,
                length: variantPhrase.length
              });
            }
          });
        }
      }
    }
  });

  // Sort patterns by length (longest first) to handle overlapping matches correctly
  allPatterns.sort((a, b) => b.length - a.length);

  // If no patterns to match, return original text
  if (allPatterns.length === 0) {
    return text;
  }

  // Create a function to escape special regex characters
  const escapeRegex = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Build regex patterns with proper word boundaries
  const regexPatterns = allPatterns.map(({ pattern }) => {
    const escapedPattern = escapeRegex(pattern);
    // Use word boundaries, but handle multi-word phrases properly
    if (pattern.includes(' ')) {
      // For multi-word phrases, use word boundary at start and end
      return `\\b${escapedPattern}\\b`;
    } else {
      // For single words, use word boundaries
      return `\\b${escapedPattern}\\b`;
    }
  });

  const combinedPattern = new RegExp(`(${regexPatterns.join('|')})`, 'gi');

  // Split text by the combined pattern while preserving the matches
  const parts = text.split(combinedPattern);

  return parts.map((part, index) => {
    if (!part) return null;

    // Check if this part is a node name match (case-insensitive)
    const normalizedPart = part.toLowerCase().trim();
    const matchedNode = nodeNameMap.get(normalizedPart);

    if (matchedNode) {
      return (
        <span
          key={index}
          className="relationship-term node-link"
          onClick={() => onNodeClick && onNodeClick(matchedNode)}
          title={`Click to view: ${matchedNode.properties?.name || part}`}
        >
          {part}
        </span>
      );
    }

    // Return regular text
    return part;
  }).filter(part => part !== null);
}

/**
 * Extracts key terms from text for highlighting
 * @param {string} text - The text to analyze
 * @param {Array} relationships - Array of relationship objects from node data
 * @returns {Array} Array of identified key terms
 */
export function extractKeyTerms(text, relationships = []) {
  if (!text || typeof text !== 'string') {
    return [];
  }
  
  const termDictionary = createTermDictionary(relationships);
  const words = text.toLowerCase().split(/\s+/);
  const keyTerms = [];
  
  words.forEach(word => {
    const cleanWord = word.replace(/[^\w\s]/g, '');
    if (termDictionary.has(cleanWord)) {
      keyTerms.push(cleanWord);
    }
  });
  
  return [...new Set(keyTerms)]; // Remove duplicates
}

/**
 * Formats text with enhanced bullet points and term highlighting
 * @param {string} text - The text to format
 * @param {Array} relationships - Array of relationship objects from node data
 * @param {Function} onNodeClick - Callback function for node click events
 * @returns {React.Element} Formatted text with bullets and highlighted terms
 */
export function formatTextWithBullets(text, relationships = [], onNodeClick = null) {
  if (!text || typeof text !== 'string') {
    return text;
  }
  
  // Split text into sentences
  const sentences = text
    .replace(/([.!?])\s+/g, '$1|SPLIT|')
    .split('|SPLIT|')
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0);
  
  return sentences.map((sentence, index) => (
    <li key={index} className="sentence-bullet-item">
      {formatTextWithNodeLinks(sentence, relationships, onNodeClick)}
    </li>
  ));
}

export default {
  formatTextWithTerms,
  formatTextWithNodeLinks,
  formatTextWithBullets,
  extractKeyTerms,
  createTermDictionary
};