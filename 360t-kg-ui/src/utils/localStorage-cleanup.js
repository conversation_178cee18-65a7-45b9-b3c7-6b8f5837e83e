/**
 * localStorage Cleanup Utility
 * 
 * Clears cached conversation data that may contain invalid integer IDs
 * and ensures fresh state for UUID-based conversation system.
 */

export const clearChatStorageCache = () => {
  try {
    // Clear Zustand persist storage
    localStorage.removeItem('chat-storage');
    localStorage.removeItem('chat-store');
    
    // Clear any other conversation-related keys
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('chat') || key.includes('conversation'))) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Cleared localStorage key: ${key}`);
    });
    
    console.log('✅ Chat storage cache cleared successfully');
    return true;
  } catch (error) {
    console.error('Error clearing chat storage cache:', error);
    return false;
  }
};

export const validateConversationId = (id) => {
  // UUID v4 regex pattern
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidPattern.test(id);
};

export const logStorageDebugInfo = () => {
  console.log('📊 localStorage Debug Info:');
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('chat') || key.includes('conversation'))) {
      console.log(`  ${key}:`, localStorage.getItem(key));
    }
  }
};

// Auto-clear on import in development
if (import.meta.env.DEV) {
  console.log('🧹 Development mode: checking for stale conversation cache...');
  
  // Check if we have any old integer-based conversation IDs cached
  const chatStorage = localStorage.getItem('chat-storage');
  if (chatStorage) {
    try {
      const parsed = JSON.parse(chatStorage);
      const state = parsed.state;
      
      if (state?.currentConversation?.id && !validateConversationId(state.currentConversation.id)) {
        console.log('🚨 Found stale integer conversation ID, clearing cache...');
        clearChatStorageCache();
        window.location.reload();
      }
      
      if (state?.conversations?.length > 0) {
        const hasIntegerIds = state.conversations.some(conv => 
          conv.id && !validateConversationId(conv.id)
        );
        
        if (hasIntegerIds) {
          console.log('🚨 Found stale integer conversation IDs in cache, clearing...');
          clearChatStorageCache();
          window.location.reload();
        }
      }
    } catch (error) {
      console.log('🚨 Corrupted chat storage, clearing...');
      clearChatStorageCache();
    }
  }
}