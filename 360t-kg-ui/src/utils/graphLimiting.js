/**
 * Graph Limiting Utilities
 * 
 * Provides client-side node and link filtering functions for graph visualization.
 * Used as a fallback when API endpoints don't support server-side limiting.
 */

/**
 * Limit the number of nodes in graph data and update associated links
 * @param {Object} graphData - The graph data with nodes and links arrays
 * @param {number} nodeLimit - Maximum number of nodes to keep (0 = no limit)
 * @returns {Object} - Limited graph data with statistics
 */
export const limitGraphNodes = (graphData, nodeLimit) => {
  // Return original data if no limit specified or invalid data
  if (!nodeLimit || nodeLimit <= 0 || !graphData || !graphData.nodes) {
    return {
      nodes: graphData?.nodes || [],
      links: graphData?.links || [],
      statistics: {
        originalNodeCount: graphData?.nodes?.length || 0,
        limitedNodeCount: graphData?.nodes?.length || 0,
        originalLinkCount: graphData?.links?.length || 0,
        limitedLinkCount: graphData?.links?.length || 0,
        wasLimited: false
      }
    };
  }

  const originalNodes = graphData.nodes;
  const originalLinks = graphData.links || [];
  
  // If we have fewer nodes than the limit, return original data
  if (originalNodes.length <= nodeLimit) {
    return {
      nodes: originalNodes,
      links: originalLinks,
      statistics: {
        originalNodeCount: originalNodes.length,
        limitedNodeCount: originalNodes.length,
        originalLinkCount: originalLinks.length,
        limitedLinkCount: originalLinks.length,
        wasLimited: false
      }
    };
  }

  // Limit nodes - take first N nodes (could be improved with better selection algorithm)
  const limitedNodes = originalNodes.slice(0, nodeLimit);
  
  // Create a set of limited node IDs for efficient lookup
  const limitedNodeIds = new Set(limitedNodes.map(node => node.id));
  
  // Filter links to only include those between limited nodes
  const limitedLinks = originalLinks.filter(link => {
    const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
    const targetId = typeof link.target === 'object' ? link.target.id : link.target;
    return limitedNodeIds.has(sourceId) && limitedNodeIds.has(targetId);
  });

  return {
    nodes: limitedNodes,
    links: limitedLinks,
    statistics: {
      originalNodeCount: originalNodes.length,
      limitedNodeCount: limitedNodes.length,
      originalLinkCount: originalLinks.length,
      limitedLinkCount: limitedLinks.length,
      wasLimited: true
    }
  };
};

/**
 * Smart node selection algorithm for limiting
 * Attempts to preserve important nodes based on connectivity and properties
 * @param {Array} nodes - Original nodes array
 * @param {Array} links - Original links array  
 * @param {number} limit - Maximum number of nodes to select
 * @returns {Array} - Selected nodes array
 */
export const selectImportantNodes = (nodes, links, limit) => {
  if (!nodes || nodes.length <= limit) {
    return nodes;
  }

  // Calculate node importance based on:
  // 1. Connection count (degree centrality)
  // 2. Node properties (has name, summary, etc.)
  const nodeImportance = nodes.map(node => {
    // Count connections for this node
    const connectionCount = links.filter(link => {
      const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
      const targetId = typeof link.target === 'object' ? link.target.id : link.target;
      return sourceId === node.id || targetId === node.id;
    }).length;

    // Calculate property score (more complete nodes are more important)
    let propertyScore = 0;
    if (node.properties?.name) propertyScore += 2;
    if (node.properties?.summary) propertyScore += 1;
    if (node.properties?.category) propertyScore += 1;
    if (node.properties?.url) propertyScore += 0.5;

    return {
      node,
      importance: connectionCount * 2 + propertyScore // Weight connections higher
    };
  });

  // Sort by importance (descending) and take top N
  nodeImportance.sort((a, b) => b.importance - a.importance);
  return nodeImportance.slice(0, limit).map(item => item.node);
};

/**
 * Apply smart limiting to graph data
 * Uses intelligent node selection instead of simple truncation
 * @param {Object} graphData - The graph data with nodes and links arrays
 * @param {number} nodeLimit - Maximum number of nodes to keep
 * @returns {Object} - Smartly limited graph data with statistics
 */
export const smartLimitGraphNodes = (graphData, nodeLimit) => {
  // Return original data if no limit specified or invalid data
  if (!nodeLimit || nodeLimit <= 0 || !graphData || !graphData.nodes) {
    return limitGraphNodes(graphData, 0); // Use simple limiting for consistency
  }

  const originalNodes = graphData.nodes;
  const originalLinks = graphData.links || [];
  
  // If we have fewer nodes than the limit, return original data
  if (originalNodes.length <= nodeLimit) {
    return limitGraphNodes(graphData, 0); // No limiting needed
  }

  // Select important nodes
  const selectedNodes = selectImportantNodes(originalNodes, originalLinks, nodeLimit);
  
  // Create a set of selected node IDs for efficient lookup
  const selectedNodeIds = new Set(selectedNodes.map(node => node.id));
  
  // Filter links to only include those between selected nodes
  const filteredLinks = originalLinks.filter(link => {
    const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
    const targetId = typeof link.target === 'object' ? link.target.id : link.target;
    return selectedNodeIds.has(sourceId) && selectedNodeIds.has(targetId);
  });

  return {
    nodes: selectedNodes,
    links: filteredLinks,
    statistics: {
      originalNodeCount: originalNodes.length,
      limitedNodeCount: selectedNodes.length,
      originalLinkCount: originalLinks.length,
      limitedLinkCount: filteredLinks.length,
      wasLimited: true,
      selectionMethod: 'smart'
    }
  };
};

/**
 * Format statistics for display
 * @param {Object} statistics - Statistics object from limiting functions
 * @returns {string} - Formatted display string
 */
export const formatLimitingStatistics = (statistics) => {
  if (!statistics || !statistics.wasLimited) {
    return `${statistics?.originalNodeCount || 0} nodes`;
  }
  
  const method = statistics.selectionMethod === 'smart' ? ' (smart selection)' : '';
  return `${statistics.limitedNodeCount} of ${statistics.originalNodeCount} nodes${method}`;
};

/**
 * Check if graph data needs limiting based on performance thresholds
 * @param {Object} graphData - The graph data to check
 * @param {Object} thresholds - Performance thresholds
 * @returns {Object} - Recommendation object
 */
export const analyzeLimitingNeeds = (graphData, thresholds = {}) => {
  const defaultThresholds = {
    performance2D: 2000,
    performance3D: 1000,
    warning: 5000,
    critical: 10000
  };
  
  const limits = { ...defaultThresholds, ...thresholds };
  const nodeCount = graphData?.nodes?.length || 0;
  
  return {
    nodeCount,
    needsLimiting2D: nodeCount > limits.performance2D,
    needsLimiting3D: nodeCount > limits.performance3D,
    performanceWarning: nodeCount > limits.warning,
    performanceCritical: nodeCount > limits.critical,
    recommended2D: Math.min(nodeCount, limits.performance2D),
    recommended3D: Math.min(nodeCount, limits.performance3D)
  };
};