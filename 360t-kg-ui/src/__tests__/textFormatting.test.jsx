import React from 'react';
import { render, screen } from '@testing-library/react';
import { formatTextWithNodeLinks } from '../utils/textFormatting';

describe('formatTextWithNodeLinks', () => {
  const mockOnNodeClick = jest.fn();

  beforeEach(() => {
    mockOnNodeClick.mockClear();
  });

  const createMockRelationships = (nodeNames) => {
    return nodeNames.map((name, index) => ({
      type: 'USES',
      direction: 'outgoing',
      node: {
        id: `node-${index}`,
        properties: {
          name: name,
          category: 'Module'
        }
      }
    }));
  };

  test('should highlight single-word node names', () => {
    const text = 'The system uses EMS for trading operations.';
    const relationships = createMockRelationships(['EMS']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);
    
    // Render the result to test it
    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Check that EMS is highlighted as a clickable link
    const linkElement = screen.getByText('EMS');
    expect(linkElement).toHaveClass('relationship-term', 'node-link');
    expect(linkElement).toHaveAttribute('title', 'Click to view: EMS');
  });

  test('should highlight multi-word node names', () => {
    const text = 'The Trading System connects to Market Data Feed for real-time information.';
    const relationships = createMockRelationships(['Trading System', 'Market Data Feed']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);
    
    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Check that both multi-word node names are highlighted
    const tradingSystemLink = screen.getByText('Trading System');
    const marketDataLink = screen.getByText('Market Data Feed');
    
    expect(tradingSystemLink).toHaveClass('relationship-term', 'node-link');
    expect(marketDataLink).toHaveClass('relationship-term', 'node-link');
  });

  test('should handle case-insensitive matching', () => {
    const text = 'The ems system and EMS module work together.';
    const relationships = createMockRelationships(['EMS']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);
    
    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Both "ems" and "EMS" should be highlighted
    const links = screen.getAllByText(/ems/i);
    expect(links).toHaveLength(2);
    links.forEach(link => {
      expect(link).toHaveClass('relationship-term', 'node-link');
    });
  });

  test('should handle node names with punctuation', () => {
    const text = 'The EMS, Trading System, and other components work together.';
    const relationships = createMockRelationships(['EMS', 'Trading System']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);
    
    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Check that node names are highlighted even when followed by punctuation
    const emsLink = screen.getByText('EMS');
    const tradingSystemLink = screen.getByText('Trading System');
    
    expect(emsLink).toHaveClass('relationship-term', 'node-link');
    expect(tradingSystemLink).toHaveClass('relationship-term', 'node-link');
  });

  test('should prioritize longer node names over shorter ones', () => {
    const text = 'The Trading System Module is part of the Trading System.';
    const relationships = createMockRelationships(['Trading System', 'Trading System Module']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);
    
    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // "Trading System Module" should be highlighted as one unit, not split
    const moduleLink = screen.getByText('Trading System Module');
    expect(moduleLink).toHaveClass('relationship-term', 'node-link');
    
    // The remaining "Trading System" should also be highlighted
    const systemLink = screen.getByText('Trading System');
    expect(systemLink).toHaveClass('relationship-term', 'node-link');
  });

  test('should return original text when no relationships provided', () => {
    const text = 'This is plain text with no relationships.';
    const relationships = [];

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);
    
    expect(result).toBe(text);
  });

  test('should return original text when text is not a string', () => {
    const result1 = formatTextWithNodeLinks(null, [], mockOnNodeClick);
    const result2 = formatTextWithNodeLinks(undefined, [], mockOnNodeClick);
    const result3 = formatTextWithNodeLinks(123, [], mockOnNodeClick);

    expect(result1).toBe(null);
    expect(result2).toBe(undefined);
    expect(result3).toBe(123);
  });

  test('should handle empty relationships array', () => {
    const text = 'This text mentions EMS but has no relationships.';
    const relationships = [];

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);

    expect(result).toBe(text);
  });

  test('should handle plural/singular variations', () => {
    const text = 'The system processes margins and each margin is validated.';
    const relationships = createMockRelationships(['Margin']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);

    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Both "margins" and "margin" should be highlighted
    const marginLinks = screen.getAllByText(/margin/i);
    expect(marginLinks.length).toBeGreaterThanOrEqual(2);
    marginLinks.forEach(link => {
      expect(link).toHaveClass('relationship-term', 'node-link');
    });
  });

  test('should handle multi-word node names with plural variations', () => {
    const text = 'The Fixing Reference Groups and fixing reference group work together.';
    const relationships = createMockRelationships(['Fixing Reference Groups']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);

    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Both variations should be highlighted
    const groupsLink = screen.getByText('Fixing Reference Groups');
    const groupLink = screen.getByText('fixing reference group');

    expect(groupsLink).toHaveClass('relationship-term', 'node-link');
    expect(groupLink).toHaveClass('relationship-term', 'node-link');
  });

  test('should handle complex pluralization patterns', () => {
    const text = 'The parties involved and each party must agree.';
    const relationships = createMockRelationships(['Party']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);

    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // Both "parties" and "party" should be highlighted
    const partyLinks = screen.getAllByText(/part(y|ies)/i);
    expect(partyLinks.length).toBeGreaterThanOrEqual(2);
    partyLinks.forEach(link => {
      expect(link).toHaveClass('relationship-term', 'node-link');
    });
  });

  test('should maintain case sensitivity in display while matching case-insensitively', () => {
    const text = 'The EMS system and ems module work with Ems components.';
    const relationships = createMockRelationships(['EMS']);

    const result = formatTextWithNodeLinks(text, relationships, mockOnNodeClick);

    const TestComponent = () => <div>{result}</div>;
    render(<TestComponent />);

    // All variations should be highlighted but maintain their original case
    const emsUpperLink = screen.getByText('EMS');
    const emsLowerLink = screen.getByText('ems');
    const emsCapitalizedLink = screen.getByText('Ems');

    expect(emsUpperLink).toHaveClass('relationship-term', 'node-link');
    expect(emsLowerLink).toHaveClass('relationship-term', 'node-link');
    expect(emsCapitalizedLink).toHaveClass('relationship-term', 'node-link');
  });
});
