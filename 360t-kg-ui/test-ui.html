<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Test - Clean Professional Design</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f9fafb;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 20px;
        }
        
        .structured-response {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 24px;
            font-size: 16px;
            line-height: 1.5;
            max-width: 100%;
            width: 100%;
            background: #FFFFFF;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
            color: #1F2937;
        }

        .response-answer {
            font-size: 18px;
            line-height: 1.6;
            color: #1F2937;
            font-weight: 500;
            padding: 32px;
            background: #FFFFFF;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
            word-wrap: break-word;
            overflow-wrap: break-word;
            margin-bottom: 0;
            max-width: 100%;
        }

        .response-explanation {
            padding: 24px;
            background: #F9FAFB;
            border-radius: 12px;
            border: 1px solid #E5E7EB;
            font-size: 16px;
            line-height: 1.5;
            color: #1F2937;
        }

        .explanation-title {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #16A34A;
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            border-bottom: 1px solid #E5E7EB;
            cursor: pointer;
        }

        .reference-link {
            display: inline-block;
            background: #6B7280;
            color: white;
            padding: 0.125rem 0.375rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            margin: 0 0.125rem;
            border: none;
            outline: none;
        }

        .reference-link.entity {
            background: #16A34A;
        }

        .reference-link.entity:hover {
            background: #15803D;
        }

        .followup-question-link {
            display: block;
            width: 100%;
            text-align: left;
            padding: 12px 16px;
            background: #FFFFFF;
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            color: #1F2937;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.5;
            cursor: pointer;
            text-decoration: none;
            font-family: inherit;
            margin-bottom: 8px;
        }

        .followup-question-link:hover {
            background: #16A34A;
            border-color: #15803D;
            color: #FFFFFF;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-size: 14px;
        }

        .status.success {
            background: #F0FDF4;
            color: #15803D;
            border: 1px solid #BBF7D0;
        }

        .status.info {
            background: #EFF6FF;
            color: #1D4ED8;
            border: 1px solid #BFDBFE;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chat UI Test - Clean Professional Design</h1>
        
        <div class="status success">
            ✓ Emoji icons removed from headers and debug logs
        </div>
        
        <div class="status success">
            ✓ Green background changed to clean white
        </div>
        
        <div class="status success">
            ✓ Professional green (#16A34A) used only for highlights
        </div>
        
        <div class="status info">
            Testing clean, maintainable design with white/grey backgrounds
        </div>

        <div class="structured-response">
            <div class="response-answer">
                This is the primary answer with clean white background and dark grey text. It now has a professional appearance without overwhelming green color. Entity references like <span class="reference-link entity">[1]</span> and <span class="reference-link entity">[2]</span> use subtle green highlighting for clickability.
            </div>

            <div class="response-explanation">
                <details open>
                    <summary class="explanation-title">
                        Explanation (2 sections)
                    </summary>
                    <div>
                        <p>This section demonstrates the clean light grey background with professional green headers. The design is now much more readable and maintainable.</p>
                        <p>Source references like <span class="reference-link">[3]</span> and <span class="reference-link">[4]</span> use grey styling to distinguish from entity references.</p>
                    </div>
                </details>
            </div>

            <div class="response-explanation">
                <details>
                    <summary class="explanation-title">
                        Related Entities (5)
                    </summary>
                    <div>
                        <p>Entity references maintain green highlighting for better UX while keeping the overall design clean and professional.</p>
                    </div>
                </details>
            </div>

            <div class="response-explanation">
                <details>
                    <summary class="explanation-title">
                        Follow-up Questions
                    </summary>
                    <div>
                        <div class="followup-question-link">1. How can I configure the risk portfolio settings?</div>
                        <div class="followup-question-link">2. What are the default execution methods available?</div>
                        <div class="followup-question-link">3. How do I manage counterpart relationships?</div>
                    </div>
                </details>
            </div>
        </div>

        <div class="status info">
            Frontend running on: <a href="http://localhost:5178" target="_blank">http://localhost:5178</a> - Click to test live UI
        </div>
    </div>

    <script>
        // Simple test to verify clicking functionality
        document.querySelectorAll('.reference-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                alert(`Reference clicked: ${e.target.textContent} (${e.target.classList.contains('entity') ? 'Entity' : 'Source'})`);
            });
        });

        document.querySelectorAll('.followup-question-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                alert(`Follow-up question clicked: ${e.target.textContent}`);
            });
        });
    </script>
</body>
</html>