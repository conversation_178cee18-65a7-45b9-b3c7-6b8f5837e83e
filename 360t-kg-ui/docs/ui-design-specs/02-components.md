# 02 — Components (Figma-Ready Specs)

All components reference tokens from 01-tokens.md. Sizes follow the 8px grid. Include these as Figma components with documented variants and properties.

---

## Chip

Purpose: Small, compact label for node types, citations, counts, and statuses.

- Sizes:
  - sm: 20px height, 8px horizontal padding
  - md: 24px height, 10px horizontal padding
- Style:
  - Radius: radius.full
  - Font: font.size.sm / font.weight.medium
  - Variants:
    - Neutral: bg color.neutral.100, text color.neutral.700
    - Accent: bg color.brand.accent (10% tint) / text color.brand.primary
    - Status: success / warning / error mapped to semantic colors
    - Outline: 1px stroke color.neutral.300, transparent bg
- Icon (optional):
  - 12–14px, left-aligned, 4px gap to label
- States:
  - De<PERSON>ult, <PERSON>ver (bg darkens by 5–8%), Focus (2px focus ring), Selected (solid bg), Disabled (50% opacity)
- Accessibility:
  - Min touch target 24x24 for interactive chips; add aria-pressed for toggle chips

---

## Accordion (Section)

Purpose: Collapsible content sections in the right panel and chat cards.

- Header:
  - Height: 40px
  - Title font: font.size.md / font.weight.semibold
  - Caret icon 16px, rotates 90° on open
  - Optional meta chip (e.g., count)
- Body:
  - Padding: space.4 (16px)
  - Divider line between sections: 1px color.neutral.200
- Motion:
  - Expand/Collapse: motion.duration.normal with motion.easing.standard
  - Respect prefers-reduced-motion
- States:
  - Focusable header with visible outline
  - Keyboard: Enter/Space toggles; aria-controls/id paired

---

## ResponseCard

Purpose: Structured AI response in Chat.

- Container:
  - Radius: radius.md
  - Elevation: elevation.1
  - Padding: space.5 (24px)
  - Background: color.brand.surface
- Sections:
  - Header row (optional): Title/summary + action icons (copy, collapse thinking)
  - Blocks:
    - Answer (lead paragraph, font.size.lg, line.height.relaxed)
    - Explanation (body, lists)
    - Citations (Chip list; see CitationChip)
    - Follow-up Questions (Pill buttons)
- Spacing:
  - Block gap: space.4
  - List indent: space.3
- Thinking Toggle:
  - Collapsed: single summary line
  - Expanded: timeline with step chips (Retrieve, Rank, Compose)
- Accessibility:
  - Each block has heading (h3/h4) semantics for screen readers
  - Keyboard focus for copy/collapse buttons

---

## SourceCard

Purpose: Display provenance details and quick actions.

- Variants: compact (list), expanded (drawer overlay)
- Compact:
  - Row height: 56–64px
  - Left icon (source type), Title, Descriptor, Reliability tag, “Open” icon
- Expanded:
  - Title + meta row (type, date, origin system)
  - Highlighted snippets with “Jump” anchors
  - Primary action: Open full source
- Styling:
  - Radius: radius.sm
  - Border: 1px color.neutral.200
  - Hover: subtle elevation.1
- Accessibility:
  - All icons labeled; links use aria-label with destination

---

## SegmentedControl (2D / 3D)

Purpose: Mode switching.

- Container height: 32px
- Segments: min width 64px, equal flex
- Selected:
  - Background: color.brand.accent (10–15% tint)
  - Text: color.brand.primary, weight medium
- Indicator (optional): 2px underline with primary color
- Keyboard:
  - Arrow keys navigate; Enter selects
  - role="tablist" semantics are acceptable with role="tab" items

---

## ZoomControl

Purpose: Canvas zoom and centering.

- Vertical cluster:
  - [+] button, [100% dropdown], [-] button, [Center selected] icon
- Sizes:
  - Buttons 32x32, radius sm
  - Gap: space.2
- Dropdown menu:
  - Fit to graph, Fit to selection (brief descriptions)
- Placement:
  - Overlay at bottom-right of canvas (avoid panel overlap)
- Accessibility:
  - Buttons labeled; keyboard shortcuts advertised in tooltip

---

## Toast

Purpose: Non-blocking status messages (load/save/screenshot).

- Container:
  - Radius: radius.md
  - Elevation: elevation.2
  - Padding: 12–16px
- Variants: info/success/warning/error
- Position: stack top-right with max 3 visible
- Motion: slide-in 120ms, fade-out 120ms
- Accessibility:
  - role="status" or role="alert" depending on severity
  - Respect reduced motion

---

## CitationChip

Purpose: Inline references [1], [2] in text.

- Size: 18–20px height; padding 6px horizontal
- Style:
  - Background: color.citation.bg
  - Text: color.citation.text
  - Radius: radius.full
- Interactions:
  - Hover: tooltip preview (title, snippet)
  - Click: opens SourceCard expanded overlay
- Accessibility:
  - Tabbable; Enter opens overlay; tooltip uses aria-describedby

---

## Breadcrumb

Purpose: Node navigation context.

- Items:
  - Truncate middle for long paths, show first/last
- Style:
  - Font.size.sm
  - Divider chevron 12px
- Interaction:
  - Hover shows full path in tooltip
  - Keyboard navigable

---

## Input Bar (Chat)

- Container:
  - Min 1 line, max 4 lines
  - Radius: radius.lg
  - Border: 1px color.neutral.300; focus ring on active
  - Left: prompt icon (optional), Right: send button 36x36
- Helper line:
  - Small caption below input (tips, mode)
- Shortcuts:
  - Cmd/Ctrl+Enter send
  - Up arrow recall last prompt
