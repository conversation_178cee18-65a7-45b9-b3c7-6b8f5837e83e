# 01 — Design Tokens (Figma-Ready)

Copy these into Figma “Styles” and library tokens. Names use a semantic scheme to avoid brand lock-in and to map cleanly to CSS variables.

## Color Tokens

Brand:
- color.brand.primary: #1F8C66
- color.brand.accent: #2DAA80
- color.brand.surface: #FFFFFF
- color.brand.surfaceSubtle: #F5F7F8
- color.brand.onPrimary: #FFFFFF
- color.brand.onSurface: #1F2937

Neutrals:
- color.neutral.900: #0B1220
- color.neutral.800: #111827
- color.neutral.700: #374151
- color.neutral.600: #4B5563
- color.neutral.500: #6B7280
- color.neutral.400: #9CA3AF
- color.neutral.300: #D1D5DB
- color.neutral.200: #E5E7EB
- color.neutral.100: #F3F4F6
- color.neutral.050: #F9FAFB

Semantic:
- color.success: #12B981
- color.warning: #F59E0B
- color.error: #EF4444
- color.info: #3B82F6

Node Status (examples):
- color.node.verified: #16A34A
- color.node.draft: #A3A3A3
- color.node.unknown: #A855F7

Citations:
- color.citation.bg: #E8F5F2
- color.citation.text: #0F5132

## Typography

Font families:
- font.family.ui: Inter, "Segoe UI", Roboto, system-ui, -apple-system, sans-serif
- font.family.mono: "JetBrains Mono", "SFMono-Regular", Menlo, Consolas, monospace

Type scale (Desktop base 16):
- font.size.xs: 12
- font.size.sm: 14
- font.size.md: 16
- font.size.lg: 20
- font.size.xl: 24

Line-height:
- line.height.tight: 1.3
- line.height.normal: 1.5
- line.height.relaxed: 1.7

Weights:
- font.weight.regular: 400
- font.weight.medium: 500
- font.weight.semibold: 600
- font.weight.bold: 700

## Spacing (8px base)

- space.1: 4
- space.2: 8
- space.3: 12
- space.4: 16
- space.5: 24
- space.6: 32

## Radius

- radius.sm: 6
- radius.md: 10
- radius.lg: 14
- radius.full: 999

## Elevation / Shadows

- elevation.0: none
- elevation.1: 0 1px 2px rgba(0,0,0,0.06)
- elevation.2: 0 2px 6px rgba(0,0,0,0.08)
- elevation.4: 0 8px 24px rgba(0,0,0,0.12)

## Motion

- motion.duration.fast: 120ms
- motion.duration.normal: 160ms
- motion.duration.slow: 240ms
- motion.easing.standard: cubic-bezier(0.2, 0, 0, 1)
- motion.respectReduced: true

## Layout Tokens

- panel.width.snap.sm: 360
- panel.width.snap.md: 420
- panel.width.snap.lg: 480
- content.maxLine: 72ch

## CSS Variable Mapping (reference)

```css
:root{
  --color-brand-primary:#1F8C66;
  --color-brand-accent:#2DAA80;
  --surface:#FFFFFF;
  --surface-subtle:#F5F7F8;
  --text:#1F2937;

  --space-1:4px; --space-2:8px; --space-3:12px; --space-4:16px; --space-5:24px; --space-6:32px;
  --radius-sm:6px; --radius-md:10px; --radius-lg:14px;

  --elevation-1:0 1px 2px rgba(0,0,0,.06);
  --elevation-2:0 2px 6px rgba(0,0,0,.08);

  --font-ui:Inter, "Segoe UI", Roboto, system-ui, -apple-system, sans-serif;
  --lh-relaxed:1.7;
}
