# 06 — Figma Mapping & Export Guide

This document maps our tokens and components to Figma Styles, Components, and Variants so the library can be reconstructed quickly. Use this as a checklist when building the Figma file.

---

## 1) File Structure (Figma)

- Pages:
  - 01 — Tokens
  - 02 — Components
  - 03 — Patterns
  - 04 — Screens
  - 05 — Accessibility Notes (for designers/devs)
- Libraries:
  - Enable as a Team Library once styles/components are defined.

---

## 2) Styles (Tokens → Figma Styles)

Create the following style groups. Names mirror code tokens to preserve a 1:1 mapping.

Color Styles
- Color / Brand / Primary — #1F8C66
- Color / Brand / Accent — #2DAA80
- Color / Brand / Surface — #FFFFFF
- Color / Brand / Surface Subtle — #F5F7F8
- Color / Text / On Primary — #FFFFFF
- Color / Text / On Surface — #1F2937
- Color / Neutral / 900 — #0B1220
- Color / Neutral / 800 — #111827
- … through Neutral / 050 — #F9FAFB
- Color / Semantic / Success — #12B981
- Color / Semantic / Warning — #F59E0B
- Color / Semantic / Error — #EF4444
- Color / Semantic / Info — #3B82F6
- Color / Node / Verified — #16A34A
- Color / Node / Draft — #A3A3A3
- Color / Node / Unknown — #A855F7
- Color / Citation / BG — #E8F5F2
- Color / Citation / Text — #0F5132

Text Styles
- Text / UI / XS / Regular — 12 / 1.5
- Text / UI / SM / Regular — 14 / 1.6
- Text / UI / MD / Regular — 16 / 1.7
- Text / UI / LG / Medium — 20 / 1.5
- Text / UI / XL / Semibold — 24 / 1.3
- Family: Inter (or equivalent), Weight per spec

Effects (Elevation)
- Elevation / 0 — none
- Elevation / 1 — 0 1 2 @ 6%
- Elevation / 2 — 0 2 6 @ 8%
- Elevation / 4 — 0 8 24 @ 12%

Grid & Spacing (as documentation frames)
- Spacing scale specimens (4/8/12/16/24/32)
- Radius specimens (6/10/14, Full)

---

## 3) Components & Variants (Figma Components)

Chip
- Variants:
  - Size: sm | md
  - Kind: neutral | accent | success | warning | error | outline
  - State: default | hover | focus | selected | disabled
- Props:
  - WithIcon: true/false
- Auto layout:
  - Horizontal padding: 8 (sm), 10 (md)
  - Height: 20 (sm) / 24 (md)
- Use Text Style: Text / UI / SM / Medium
- Use Color Styles per variant

Accordion
- Variants:
  - State: collapsed | expanded
  - Density: default | compact
- Slots:
  - Header (title, meta chip)
  - Body (content)
- Motion note in description (160ms, easing standard)

ResponseCard
- Sections as subcomponents:
  - Block / Answer
  - Block / Explanation
  - Block / Citations
  - Block / Follow-ups
- Variant:
  - Thinking: collapsed | expanded
- Container:
  - Radius: 10, Elevation: 1, Padding: 24

SourceCard
- Variants:
  - View: compact | expanded
  - Reliability: default | high | low (optional tags)
- Slots:
  - Title, Meta, Snippets list, Primary action

SegmentedControl
- Variants:
  - Selected: left | right (for 2D/3D)
  - Size: md
- Tab items are subcomponents with selected state

ZoomControl
- Buttons as subcomponents:
  - Plus, Percent (dropdown), Minus, Center
- Property:
  - Placement note (canvas overlay)

Toast
- Variants:
  - Type: info | success | warning | error
  - State: idle | entering | exiting (for prototyping)
- Role notes in description

CitationChip
- Single component with interactive note:
  - Tooltip on hover/focus; opens SourceCard expanded

Breadcrumb
- Auto layout with truncation guidance (middle ellipsis)
- Item states: default | hover | focus | active

Input Bar
- Variants:
  - Lines: 1 | 2 | 3 | 4 (visual height examples)
  - State: idle | focus | disabled
- Slots:
  - Left icon, Text area, Right send button

---

## 4) Patterns (As Figma Frames with Annotations)

- Right Panel IA:
  - Show header, chips, accordions, relationships tabs
  - Include notes for snap widths 360/420/480
- Graph Focus Mode:
  - Idle vs Selected states; overlay controls
- Citations & Sources:
  - Inline chip → tooltip → overlay flow
- Chat with Reasoning:
  - ResponseCard collapsed/expanded states
- Global Nav & View Mode:
  - Tabs + SegmentedControl + toasts area

Each pattern frame should include redline annotations:
- Spacing (8px grid multiples)
- Token references (e.g., “Text: Text/UI/MD/Regular”)
- Interaction notes (hover/focus/keyboard)

---

## 5) Export & Documentation

- Use “Copy as Text” from each token/component description to sync back to code if needed.
- Export PNGs/SVGs for:
  - Component cover images
  - Pattern diagrams
- Maintain “Changelog” frame documenting updates with dates.

---

## 6) Import Tips (Dev ↔ Figma)

- When implementing, map CSS variables per 01-tokens.md.
- Keep component names identical to Figma for easier communication.
- Provide a hidden “/styleguide” route in the app mirroring Components and Tokens for visual parity checks.

---

## 7) Acceptance Checklist

- [ ] All color styles created and named exactly per section 2
- [ ] Text styles defined for XS/SM/MD/LG/XL with correct LH and weights
- [ ] Elevation effects defined and applied to components
- [ ] All components exist with specified variants and properties
- [ ] Patterns framed with annotations and redlines
- [ ] Screens assembled referencing components (no detached layers)
- [ ] Page “05 — Accessibility Notes” added with WCAG reminders from 05-accessibility.md
