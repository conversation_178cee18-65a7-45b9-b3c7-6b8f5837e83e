# Knowledge Graph UI — Figma-Ready Design Specs (Exportable)

This package defines tokens, components, and key screens in a Figma-ready spec format that can be copied into Figma descriptions or converted to JSON styles. It is structured for quick implementation by engineering and clear handoff for design.

Contents:
- 01-tokens.md — Color, typography, spacing, radii, elevation, motion, shadows
- 02-components.md — Chips, Accordions, ResponseCard, SourceCard, SegmentedControl, ZoomControl, Toasts
- 03-patterns.md — Right Panel IA, Graph Focus Mode, Chat Reasoning Controls, Citations/Provenance UX
- 04-screens.md — Explorer, Chat, Header/Nav with View Mode
- 05-accessibility.md — WCAG mappings, focus, keyboard, motion, contrast
- 06-figma-mapping.md — Naming and styles mapping for Figma libraries

Use these as the single source of truth for the UI design system.
