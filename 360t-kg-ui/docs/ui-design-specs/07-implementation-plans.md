# Implementation Plans

This document provides two concrete, execution-ready plans:
1) Figma Library Build Plan
2) UX Improvements + Playwright Testing Plan

Each plan includes scope, steps, ownership, and acceptance criteria.

---

## 1) Figma Library Build Plan

Goal: Stand up a Figma Team Library that mirrors the repo specs for tokens, components, patterns, and screens so designers and engineers share a single source of truth.

Prerequisites
- Files in this repo:
  - 01-tokens.md, 02-components.md, 03-patterns.md, 04-screens.md, 05-accessibility.md, 06-figma-mapping.md
- A Figma team space with library publishing rights.

Deliverables
- A published Figma Library with:
  - Color styles
  - Text styles
  - Effects (elevation)
  - Components (with variants): Chip, Accordion, ResponseCard, SourceCard, SegmentedControl, ZoomControl, Toast, CitationChip, Breadcrumb, Input Bar
  - Pattern frames
  - Annotated screens: Explorer, Chat, Internal Styleguide
  - “Accessibility Notes” page

Step-by-step
1. Create Figma File Pages
   - 01 — Tokens
   - 02 — Components
   - 03 — Patterns
   - 04 — Screens
   - 05 — Accessibility Notes

2. Build Styles (01 — Tokens)
   - Color Styles: Add styles exactly named as in 06-figma-mapping.md
   - Text Styles: XS/SM/MD/LG/XL with specified line-height and weights
   - Effects: Elevation 0/1/2/4
   - Document spacing and radius using frames and labels

3. Components (02 — Components)
   - Create base components:
     - Chip (variants: size/kind/state; with/without icon)
     - Accordion (state: collapsed/expanded; density)
     - ResponseCard (thinking: collapsed/expanded; slots)
     - SourceCard (view: compact/expanded)
     - SegmentedControl (selected: left/right)
     - ZoomControl (buttons as subcomponents)
     - Toast (type/state)
     - CitationChip (interactive note)
     - Breadcrumb (item states)
     - Input Bar (lines/state)
   - Use Auto Layout with 8px grid increments
   - Link all layers to styles (colors/text/effects)

4. Patterns (03 — Patterns)
   - Create frames for:
     - Right Panel IA
     - Graph Focus Mode
     - Citations & Sources
     - Chat with Reasoning
     - Global Nav & View Mode
   - Add redline annotations (spacing, tokens, behavior notes)

5. Screens (04 — Screens)
   - Assemble Explorer and Chat screens using components
   - Create Internal Styleguide screen page with component gallery and tokens

6. Accessibility Notes (05 — Accessibility)
   - Copy core criteria into a Figma page for designers/devs

7. Publish Library
   - Run “Publish” to enable as a Team Library
   - Version label: “v0.1 (Foundations + Key Components)”

Acceptance Criteria
- All token names in Figma exactly match 06-figma-mapping.md
- All components have specified variants and are wired to styles
- Patterns and screens reference components (not detached)
- Library published and visible to the team
- Change log noted in Figma file cover page

---

## 2) UX Improvements + Playwright Testing Plan

Goal: Implement UX changes that increase clarity, reduce cognitive load, and validate with automated e2e tests.

Scope (Phase 1: Low-risk, high-value)
- Right Panel IA:
  - Merge Summary + Description into Overview (with “More details”)
  - Add Action Strip (Expand neighbors, Open in Chat, See relationships)
  - “At a glance” top 3 relationships above tabs with “Show all”
  - Replace “Show 1 more sentence” with real accordions
  - Typography/readability: 1.6–1.7 line-height; 72ch max width
- Graph Focus Mode v1:
  - Dim non-connected nodes (60–70%)
  - Selected node halo + thicker edges
  - ZoomControl + Center selected
  - Tooltips as primary affordance (hover action bar deferred to Phase 2)
- Chat Enhancements:
  - ResponseCard structure with sections
  - Long-response ToC when content exceeds threshold
  - Follow-up pills: insert with edit-before-send confirmation
- Citations Overlay v1:
  - Compact first view (core metadata + 1–2 snippets)
  - “View details” to expand
  - “Why this source?” microcopy
- Navigation:
  - Persist 2D/3D mode in URL
  - Header “Help & Shortcuts” with keyboard reference

Non-goals (Phase 1)
- Lasso selection and bulk grouping UX (Phase 2)
- Full label collision solver (use truncation + tooltip)
- Theming/dark mode

Implementation Steps
1. Token/Styles Update (CSS variables only)
   - Apply line-height and max-width policies to panel and response content
   - Normalize spacing to 8px grid; unify radii and elevations for cards/panels

2. Right Panel Refactor
   - Create Overview accordion that merges Summary+Description
   - Add Action Strip (buttons with tooltips and test-friendly data-testid)
   - Add “At a glance” relationships snippet list with “Show all” jumping to tabs

3. Graph Focus Mode v1
   - Add overlay layer to dim non-connected nodes
   - Selected node halo and edge-thickness increase
   - Add ZoomControl overlay + “Center selected”

4. Chat Card Structure
   - Introduce ResponseCard container with slots (Answer, Explanation, Citations, Follow-ups)
   - Add ToC when height exceeds N pixels (configurable)
   - Implement follow-up “insert + edit confirm” pattern

5. Citations Overlay
   - Compact overlay first; “View details” expands
   - Include “Why this source?” reason text

6. URL Mode Persistence
   - Reflect 2D/3D in query param (?mode=2d/3d)
   - Initialize UI from URL on load; update on toggle

7. Help & Shortcuts
   - Header icon opens a simple modal with key shortcuts and a brief onboarding

8. Telemetry (Optional but recommended)
   - Emit events: open_source, expand_neighbors, center_selected, mode_toggle, followup_used
   - Use for measuring success criteria

Playwright Testing Plan (Extensive)

Test Organization
- Location: 360t-kg-ui/tests/e2e/
- Suites:
  - panel-ia.spec.ts
  - graph-focus-mode.spec.ts
  - chat-response-card.spec.ts
  - citations-overlay.spec.ts
  - navigation-mode.spec.ts
  - accessibility-core.spec.ts

Shared Test Utilities
- test-ids: All actionable UI elements get data-testid
- helpers:
  - selectNodeByName(name)
  - openPanelSection(name)
  - ensureMode(mode)
  - waitForToast(text)
  - mockResponseCardLongContent()

1) panel-ia.spec.ts
- “Overview merges Summary+Description and is expanded by default”
- “Action Strip contains Expand neighbors, Open in Chat, See relationships and triggers events”
- “At a glance shows top 3 relationships and ‘Show all’ navigates to full list”
- “Accordions are keyboard togglable and aria-expanded updates”
- Readability checks: compute CSS line-height >= 1.6; max width ≤ 72ch (approx by clientWidth/charWidth heuristic)

2) graph-focus-mode.spec.ts
- “Selecting a node dims non-connected nodes and highlights edges”
- “Center selected recenters graph (bounds check)”
- “ZoomControl buttons change zoom level and are keyboard accessible”
- “Esc clears selection and restores opacity”

3) chat-response-card.spec.ts
- “ResponseCard renders sections and ToC appears for long responses”
- “Follow-up pill inserts text into input with editable preview and confirm”
- “Thinking toggle updates aria-expanded and preserves scroll position”

4) citations-overlay.spec.ts
- “Citation chip opens compact overlay with metadata and 1–2 snippets”
- “View details expands overlay; close returns focus”
- “Why this source? text renders when available”
- “From Chat: clicking citation scrolls panel to Sources and highlights card”

5) navigation-mode.spec.ts
- “Toggling 2D/3D updates ?mode= param”
- “Reload respects mode from URL”
- “Help & Shortcuts modal opens from header and lists key shortcuts”

6) accessibility-core.spec.ts
- Keyboard flows:
  - Tab through header → panel → accordions → actions
  - SegmentedControl arrow navigation + Enter selects
  - Overlay focus trap and Esc to close
- Landmarks exist (banner, navigation, main, complementary)
- Live region announces selection changes (mock assertion)
- Contrast spot checks (via axe or token assertions if axe integration available)

Acceptance Criteria
- All new suites pass consistently in CI
- No “serious” or “critical” AXE violations (if integrated)
- Median e2e runtime acceptable (target ≤ 4–6 minutes total)
- Flake rate < 2% across 10 consecutive runs locally

Rollout & Risk Mitigation
- Feature flag advanced items (ToC, compact overlay) behind config
- Keep Phase 1 changes non-destructive to existing APIs
- Provide a migration note in CHANGELOG: tokens and new testids

Timeline (suggested)
- Week 1:
  - Token updates, Right Panel IA, initial Playwright for panel-ia and navigation-mode
- Week 2:
  - Graph Focus Mode v1 + tests, Citations overlay v1 + tests
- Week 3:
  - Chat ResponseCard + ToC + follow-up edit flow + tests
  - Accessibility core suite + polish

Ownership & Handoff
- Design: Build Figma library per Plan 1; review weekly
- Frontend: Implement Phase 1 scope and wire tests
- QA: Own Playwright flake tracking and AXE scan integration
