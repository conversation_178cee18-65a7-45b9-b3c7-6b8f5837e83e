# 04 — Key Screens (Annotated)

These specs describe the primary screens with layout zones, component usage, and measurements so they can be reproduced in Figma and implemented consistently.

---

## Screen 1 — Explorer (Graph + Right Panel)

Viewport: Desktop 1440×900 reference.

Layout:
- Header (fixed top, 64px height)
  - Left: Brand + Product title
  - Center: Primary tabs (Chat, Explorer, Analysis, Documentation)
  - Right: SegmentedControl (2D / 3D), Context actions (Export, Screenshot)
- Content area (fills remaining viewport)
  - Graph <PERSON> (fluid)
  - Right Panel (resizable, snap 360 / 420 / 480px)

Graph Canvas:
- Floating ZoomControl: bottom-right, 24px margin from edges
- Focus mode states (see Pattern B)
- Labels: max 2 lines, font.size.sm, shadow for contrast on light backgrounds

Right Panel (Properties):
- Panel header:
  - Node title (font.size.lg / semibold)
  - Type Chip + badges (confidence, sources)
- Sections (Accordion):
  - Summary (default open)
  - Description
  - Sources (grouped)
  - Relationships (tabs: Incoming/Outgoing)
- Spacing:
  - Outer padding: 16px
  - Section gap: 12–16px
- Readability:
  - Body text line-height.relaxed
  - Paragraph max width: 72ch

Annotations:
- Keyboard:
  - Tab cycles panel header → sections
  - Arrow keys move between tabs in Relationships
- Motion:
  - Panel resize snaps in 160ms
  - Section expand 160ms easing standard

---

## Screen 2 — Chat

Layout:
- Header identical to Explorer
- Left content column (fluid)
  - Chat transcript: list of ResponseCard components
  - Each ResponseCard with blocks:
    - Answer, Explanation, Citations, Follow-up Questions
    - Thinking toggle (collapsed summary by default)
- Right Panel (optional, when a node is selected from an answer/citation)
  - Same panel architecture as Explorer

Input Bar:
- Sticky bottom bar
- 1–4 lines height, grows with content
- Left: mode/prompt icon (optional)
- Right: send button 36×36
- Helper caption below (e.g., “Press Enter to send, Shift+Enter for newline”)

Citations:
- Inline CitationChip [n]
- Clicking scrolls the right panel to Sources and highlights card for 2s

Annotations:
- Shortcuts:
  - Cmd/Ctrl+Enter send
  - Up arrow to recall previous prompt
- Accessibility:
  - ResponseCard sections have headings
  - Focus moves to first interactive element after response render

---

## Screen 3 — Global Navigation and Tokens Demo

Purpose: Internal style guide page to validate tokens and components (optional hidden route).

Sections:
- Color palette swatches (brand, neutrals, semantic)
- Typography scale specimens (weights and sizes)
- Components gallery: Chip, Accordion, ResponseCard, SourceCard, SegmentedControl, ZoomControl, Toasts
- Interactive examples:
  - Accordion open/close
  - SegmentedControl toggle
  - Toast stack mock

Notes:
- This page mirrors the design system for quick QA and future theming.
- Not shipped to end users by default; used for dev and design validation.

---

## Measurements Summary

- Header height: 64px
- Tabs hit area: min 40px height
- Panel snap widths: 360 / 420 / 480px
- Card padding: 24px
- Section header height: 40px
- Buttons (primary UI): 32–36px min
- Base spacing: 8px grid (4/8/12/16/24/32)

---

## States & Empty Cases

Explorer:
- No node selected:
  - Right panel shows “Select a node to see details” with illustrative empty state
- Loading:
  - Centered spinner over canvas; toast “Loading graph…”

Chat:
- First-time empty:
  - Prompt suggestions as pill buttons
- Streaming response:
  - Animated placeholder lines; reading progress indicator

---

## Assets and Icons

- Source icons: PDF, API, DB, Link (16px)
- Mode icons: 2D, 3D (16px)
- Actions: Copy, Collapse, Expand, External-link (16px)
- Use consistent stroke width (1.5px) and alignment

---

## Example Z-Index Guidelines

- Toasts: 1000
- Overlays/Drawers (SourceCard expanded): 900
- Header: 800
- ZoomControl: 700
- Right Panel: 600
- Graph Canvas: base
