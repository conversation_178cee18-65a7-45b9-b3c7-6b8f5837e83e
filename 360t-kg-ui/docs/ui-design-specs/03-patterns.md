# 03 — UX Patterns

This file specifies the key interaction patterns to standardize across the app. Use together with 01-tokens and 02-components.

---

## Pattern A — Right Panel Information Architecture

Goal: Clear, scannable node details with progressive disclosure.

Structure:
1) Panel Header
   - Node Title (font.size.lg, weight.semibold)
   - Type Chip (Entity/Feature/Parameter)
   - Inline badges: Confidence, Sources count
   - Optional breadcrumb above header (see Pattern E)

2) Sections (Accordion components)
   - Summary (default: expanded)
   - Description
   - Sources (see Pattern C)
   - Relationships (tabs: Incoming / Outgoing)
   - Optional: Notes / Flags

Behavior:
- Drag-resize with snap points at 360 / 420 / 480px.
- Section expand/collapse animates 160ms with easing standard.
- “Show more” is replaced by Accordion with clear state labels.
- Paragraphs constrained to content.maxLine (72ch); line-height.relaxed.

Accessibility:
- Each section uses aria-controls/id; header is a button.
- Landmarks: panel has role="complementary"; headings use h2/h3 semantics.
- Keyboard: Enter/Space toggles sections; Tab order is logical, no traps.

Metrics:
- Readability score target: < 100 words per expanded section by default.
- Truncation threshold for bullets: 3 items; “Show all” reveals rest.

---

## Pattern B — Graph Focus Mode

Goal: Help users focus on a selected node and its near context.

States:
- Idle: normal visualization.
- Selected: 
  - Selected node halo (8–12px soft glow using color.brand.accent at 20–30%).
  - Connected edges increase thickness by 1–2px; connected nodes keep full opacity.
  - Non-connected nodes dim to 60–70% opacity.

Controls:
- Floating ZoomControl at canvas bottom-right.
- Actions on selected node (hover action bar or context pin):
  - Pin, Open Details, Expand Neighbors.
- Quick actions:
  - “Center selected” recenters with 120ms motion (respect reduced motion).

Labels:
- Collision-aware label placement; max 2 lines; ellipsis on overflow.
- Tooltip on hover shows full label and type chip.

Accessibility:
- Tab cycles nodes by proximity weighted order.
- Focus ring: 2px high-contrast outline for focused node.
- Keyboard shortcuts (documented in tooltips):
  - C: Center selected
  - +/-: Zoom in/out
  - Esc: Clear selection

---

## Pattern C — Citations & Sources (Provenance)

Goal: Trust and traceability without context switching.

Inline Citations:
- Render as CitationChip [n] within text.
- Hover: tooltip preview {Title, Snippet, Source type icon}.
- Click: opens SourceCard (expanded) in an overlay/drawer on the right.

Sources Section:
- Group by source system with count badges (PDF, API, DB).
- SourceCard (compact) list; clicking an item expands overlay.
- From Chat responses, clicking a citation scrolls to Sources section and highlights the card for 2s.

Accessibility:
- Tooltip uses aria-describedby and dismisses with Esc.
- Overlay is focus-trapped and returns focus on close.
- All link-outs have explicit aria-labels.

---

## Pattern D — Chat Response with Reasoning Controls

Goal: Scannable answers with optional transparency.

ResponseCard:
- Blocks in order:
  1) Answer (lead)
  2) Explanation (body + bullet lists)
  3) Citations (chips)
  4) Follow-up Questions (pills)

Thinking Toggle:
- Collapsed state shows a summary line: “Derived from X sources and Y relationships”.
- Expanded state shows a step list:
  - Step chips: Retrieve / Rank / Compose.
  - Each step has 1–3 bullets max, with tokens for key terms.

Input Bar:
- Expands from 1–4 lines.
- Helper caption shows tips or mode.
- Shortcut: Cmd/Ctrl+Enter to send; Up to recall previous prompt.

Accessibility:
- Each block is a semantic section with a heading.
- Toggle is keyboard focusable; state announced via aria-expanded.

---

## Pattern E — Breadcrumb for Node Context

Goal: Keep orientation during graph exploration.

Structure:
- Root / … / Parent / Current
- Middle items collapse to an ellipsis when path is long.
- Hover tooltip shows full path; click navigates back and updates selection.

Accessibility:
- role="navigation"; items are links/buttons with labels.
- Keyboard-left/right navigates; Enter to select.

---

## Pattern F — Global Navigation & View Mode

Header:
- Primary tabs: Chat, Explorer, Analysis, Documentation.
- Active tab shows underline and color.brand.primary text.
- Minimum hit area 40px high; spacing uses space.4.

View Mode:
- SegmentedControl (2D / 3D) right-aligned.
- Selected mode shows accent background and medium weight.

Toasts:
- Top-right stack; max 3; Info/Success/Warning/Error variants.
- role="status" or "alert" based on severity.

---

## Quick Wins Checklists

Right Panel:
- [ ] Body text LH ≥ 1.6
- [ ] Max line width 72ch
- [ ] Accordion replaces “Show more”
- [ ] Section headings and keyboard toggle

Graph:
- [ ] Floating ZoomControl with center action
- [ ] Focus dimming + halo + thicker edges
- [ ] Label truncation with tooltip

Chat:
- [ ] ResponseCard with sections
- [ ] Thinking toggle summary + steps
- [ ] Follow-up pills insert prompt preview

Citations:
- [ ] Chips [n] with tooltip + overlay
- [ ] Sources grouped and cross-linked from chat
