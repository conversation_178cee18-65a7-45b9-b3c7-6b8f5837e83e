{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["get-library-docs", "resolve-library-id"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["", "sequentialthinking"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents"], "alwaysAllow": ["list_directory", "list_allowed_directories"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "alwaysAllow": ["create_relations", "add_observations"]}, "time": {"command": "uvx", "args": ["mcp-server-time"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAZXA9r88l0slfNDcyQdbuXcQRcI_7"}, "alwaysAllow": ["brave_web_search"]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--browser=", "--headless=", "--viewport-size="]}}}