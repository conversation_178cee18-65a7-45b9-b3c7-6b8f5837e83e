#!/bin/bash

# Test script for the updated startup process
# Validates that all four services are running and accessible

echo "🔍 Testing updated startup process..."

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to test service health
test_service() {
    local url=$1
    local service_name=$2
    
    echo -n "Testing $service_name... "
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

# Function to test port availability
test_port() {
    local port=$1
    local service_name=$2
    
    echo -n "Checking port $port ($service_name)... "
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ LISTENING${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT LISTENING${NC}"
        return 1
    fi
}

echo ""
echo -e "${BLUE}📡 Port Availability Check:${NC}"
test_port 8000 "FastAPI"
test_port 3002 "Node.js API"
test_port 3003 "Proxy"
test_port 5177 "Frontend"

echo ""
echo -e "${BLUE}🏥 Health Check Tests:${NC}"
test_service "http://localhost:8000/health" "FastAPI service"
test_service "http://localhost:3002/api/health" "Node.js API service"
test_service "http://localhost:3003/health" "Proxy server"

echo ""
echo -e "${BLUE}🔗 API Endpoint Tests:${NC}"
test_service "http://localhost:3002/api/graph/minimal" "Graph data endpoint"
test_service "http://localhost:3003/api/graph/minimal" "Proxy routing to graph"

echo ""
echo -e "${BLUE}🌐 Frontend Accessibility:${NC}"
echo -n "Testing frontend HTML... "
if curl -s "http://localhost:5177/" | grep -q "360T Knowledge Graph" 2>/dev/null; then
    echo -e "${GREEN}✅ OK${NC}"
else
    echo -e "${RED}❌ FAILED${NC}"
fi

echo ""
echo -e "${BLUE}📊 Summary:${NC}"
echo "All services should be:"
echo "  • Running on their designated ports"
echo "  • Responding to health checks"  
echo "  • Serving their expected content"
echo ""
echo -e "${YELLOW}💡 If any tests failed:${NC}"
echo "  1. Check service logs for errors"
echo "  2. Restart the failed service"
echo "  3. Verify the service startup order"
echo "  4. Check for port conflicts"
