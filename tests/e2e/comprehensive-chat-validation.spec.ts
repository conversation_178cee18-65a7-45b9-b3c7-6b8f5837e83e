import { test, expect } from '@playwright/test';

test.describe('Comprehensive Chat Functionality Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for this test suite
    test.setTimeout(120000); // 2 minutes
  });

  test('Complete end-to-end chat workflow with "what is a ukmtf" query', async ({ page }) => {
    console.log('🚀 Starting comprehensive chat validation with UKMTF query...');
    
    const startTime = Date.now();
    
    // Step 1: Navigate to frontend and verify it loads
    console.log('📍 Step 1: Loading frontend...');
    await page.goto('http://localhost:5177/');
    await page.waitForLoadState('networkidle');
    
    // Check for any console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
        console.log('❌ Console error:', msg.text());
      }
    });
    
    console.log('✅ Frontend loaded successfully');
    
    // Step 2: Navigate to chat interface
    console.log('📍 Step 2: Accessing chat interface...');
    
    // Try multiple ways to access chat
    let chatAccessible = false;
    
    // Method 1: Look for Chat button in navigation
    const chatNavButton = page.locator('button:has-text("Chat"), .nav-button:has-text("Chat")').first();
    if (await chatNavButton.isVisible()) {
      await chatNavButton.click();
      chatAccessible = true;
      console.log('✅ Chat accessed via navigation button');
    } else {
      // Method 2: Direct URL navigation
      await page.goto('http://localhost:5177/chat');
      await page.waitForLoadState('networkidle');
      chatAccessible = true;
      console.log('✅ Chat accessed via direct URL');
    }
    
    expect(chatAccessible).toBe(true);
    await page.waitForTimeout(2000);
    
    // Step 3: Verify chat interface elements
    console.log('📍 Step 3: Verifying chat interface elements...');
    
    const chatInput = page.locator('input[type="text"], textarea, [placeholder*="message"], [placeholder*="question"]').first();
    await expect(chatInput).toBeVisible({ timeout: 10000 });
    
    const sendButton = page.locator('button:has-text("Send"), button[type="submit"], button:has([class*="send"])').first();
    
    console.log('✅ Chat interface elements verified');
    
    // Step 4: Send the specific test query
    console.log('📍 Step 4: Sending "what is a ukmtf" query...');
    
    const queryStartTime = Date.now();
    await chatInput.fill('what is a ukmtf');
    
    // Try multiple send methods
    if (await sendButton.isVisible()) {
      await sendButton.click();
      console.log('✅ Message sent via send button');
    } else {
      await chatInput.press('Enter');
      console.log('✅ Message sent via Enter key');
    }
    
    // Step 5: Wait for and verify response
    console.log('📍 Step 5: Waiting for response...');
    
    // Wait for response with timeout
    const responseTimeout = 60000; // 60 seconds
    let responseReceived = false;
    let responseText = '';
    
    try {
      // Look for various response indicators
      const responseSelectors = [
        '[class*="message"]:has-text("Assistant")',
        '[class*="response"]',
        '[class*="chat-message"]',
        'div:has-text("🟢 Answer")',
        'div:has-text("UKMTF")',
        '[class*="assistant"]'
      ];
      
      for (const selector of responseSelectors) {
        try {
          const responseElement = page.locator(selector).last();
          await responseElement.waitFor({ timeout: responseTimeout });
          responseText = await responseElement.textContent() || '';
          if (responseText.length > 10) {
            responseReceived = true;
            console.log('✅ Response received via selector:', selector);
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      // If no specific response found, check for any new content
      if (!responseReceived) {
        await page.waitForTimeout(5000);
        const allMessages = page.locator('[class*="message"], div:contains("UKMTF"), div:contains("Answer")');
        const messageCount = await allMessages.count();
        if (messageCount > 0) {
          responseText = await allMessages.last().textContent() || '';
          responseReceived = responseText.length > 10;
        }
      }
      
    } catch (error) {
      console.log('⚠️ Response timeout or error:', error);
    }
    
    const queryEndTime = Date.now();
    const responseTime = queryEndTime - queryStartTime;
    
    console.log(`⏱️ Response time: ${responseTime}ms`);
    
    // Step 6: Validate response content
    console.log('📍 Step 6: Validating response content...');
    
    if (responseReceived && responseText) {
      console.log('✅ Response received:', responseText.substring(0, 200) + '...');
      
      // Check for expected content patterns
      const expectedPatterns = [
        /UKMTF/i,
        /UK.*MTF/i,
        /multilateral.*trading.*facility/i,
        /trading.*platform/i,
        /financial.*market/i
      ];
      
      const patternMatches = expectedPatterns.some(pattern => pattern.test(responseText));
      if (patternMatches) {
        console.log('✅ Response contains relevant UKMTF information');
      } else {
        console.log('⚠️ Response may not contain expected UKMTF information');
      }
      
    } else {
      console.log('❌ No response received within timeout period');
    }
    
    // Step 7: Performance validation
    console.log('📍 Step 7: Performance validation...');
    
    const totalTime = Date.now() - startTime;
    console.log(`⏱️ Total test time: ${totalTime}ms`);
    
    // Performance assertions
    expect(responseTime).toBeLessThan(120000); // 2 minutes max
    if (responseTime < 10000) {
      console.log('🚀 Excellent response time: < 10 seconds');
    } else if (responseTime < 30000) {
      console.log('✅ Good response time: < 30 seconds');
    } else {
      console.log('⚠️ Slow response time: > 30 seconds');
    }
    
    // Step 8: Error validation
    console.log('📍 Step 8: Error validation...');
    
    if (consoleErrors.length === 0) {
      console.log('✅ No console errors detected');
    } else {
      console.log('⚠️ Console errors detected:', consoleErrors);
    }
    
    // Step 9: Take screenshots for documentation
    console.log('📍 Step 9: Taking screenshots...');
    
    await page.screenshot({ 
      path: 'comprehensive-chat-validation.png', 
      fullPage: true 
    });
    
    // Step 10: Final assertions
    console.log('📍 Step 10: Final validation...');
    
    // Core functionality assertions
    expect(responseReceived).toBe(true);
    expect(responseText.length).toBeGreaterThan(10);
    expect(consoleErrors.filter(error => 
      error.includes('CORS') || 
      error.includes('network') || 
      error.includes('fetch')
    ).length).toBe(0);
    
    console.log('🎉 Comprehensive chat validation completed successfully!');
    
    // Summary report
    console.log('\n📊 VALIDATION SUMMARY:');
    console.log(`✅ Frontend Load: Success`);
    console.log(`✅ Chat Access: Success`);
    console.log(`✅ Query Sent: "what is a ukmtf"`);
    console.log(`✅ Response Received: ${responseReceived ? 'Yes' : 'No'}`);
    console.log(`⏱️ Response Time: ${responseTime}ms`);
    console.log(`🔍 Console Errors: ${consoleErrors.length}`);
    console.log(`📸 Screenshot: comprehensive-chat-validation.png`);
  });

  test('Error scenario validation', async ({ page }) => {
    console.log('🚀 Starting error scenario validation...');
    
    // Test with invalid query to check error handling
    await page.goto('http://localhost:5177/chat');
    await page.waitForLoadState('networkidle');
    
    const chatInput = page.locator('input[type="text"], textarea').first();
    await chatInput.fill(''); // Empty query
    await chatInput.press('Enter');
    
    // Should handle empty queries gracefully
    await page.waitForTimeout(2000);
    
    console.log('✅ Error scenario validation completed');
  });
});
