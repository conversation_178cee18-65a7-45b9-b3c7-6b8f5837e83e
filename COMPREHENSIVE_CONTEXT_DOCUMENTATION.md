# Knowledge Graph Visualizer: Comprehensive Context Documentation

## Executive Summary

This is a sophisticated 5-layer Knowledge Graph Visualizer system for 360T platform components, featuring real-time 2D/3D graph visualization, AI-powered chat functionality, and advanced graph analytics. The architecture balances high performance visualization with robust AI integration through a carefully orchestrated service mesh.

## 1. System Architecture Overview

### 5-Layer Architecture

```
┌─────────────────────────────────────────────────────────────┐
│ LAYER 1: Frontend (React + Vite)                           │
│ Port: 5177 | Tech: React, D3.js, Three.js, WebGL          │
│ Purpose: Interactive 2D/3D graph visualization + chat UI   │
└─────────────────────────────────────────────────────────────┘
                                │
                    HTTP Requests (CORS-enabled)
                                │
┌─────────────────────────────────────────────────────────────┐
│ LAYER 2: Proxy Server (Express.js)                         │
│ Port: 3003 | Tech: Express, Sessions, Rate Limiting        │
│ Purpose: Session management, request routing, CORS handling │
└─────────────────────────────────────────────────────────────┘
                                │
                     ┌──────────┴──────────┐
                     │                     │
          Graph/Analysis Requests    Chat Requests
                     │                     │
┌────────────────────┴────────┐   ┌───────┴──────────────────────┐
│ LAYER 3A: API Backend       │   │ LAYER 3B: AI Layer          │
│ Port: 3002                  │   │ Port: 8000                   │
│ Tech: Express, Neo4j        │   │ Tech: FastAPI, LLM Providers │
│ Purpose: Graph data, GDS    │   │ Purpose: AI chat, context    │
└─────────────────────────────┘   └──────────────────────────────┘
                │                                    │
                │                                    │
┌───────────────┴─────────────────────────────────────┴───────┐
│ LAYER 4: Database Layer (Neo4j)                            │
│ Port: 7687 | Tech: Neo4j, GDS (Graph Data Science)        │
│ Purpose: Graph storage, relationship analytics, GDS algos  │
└─────────────────────────────────────────────────────────────┘
```

### Critical Port Mappings
- **Frontend**: 5177 (Vite dev server)
- **Proxy**: 3003 (session management + routing)
- **API Backend**: 3002 (graph data + Neo4j)
- **AI Layer**: 8000 (FastAPI + LLM providers)
- **Database**: 7687 (Neo4j bolt protocol)

### Data Flow Patterns

#### Graph Data Flow
```
Frontend → Proxy (3003) → API Backend (3002) → Neo4j (7687)
   ↓         ↓                ↓                    ↓
GraphView  Session      GraphRepository      Cypher Queries
         Management     + Transformations    + GDS Algorithms
```

#### Chat Data Flow
```
Frontend → Proxy (3003) → AI Layer (8000) → Neo4j (7687)
   ↓         ↓               ↓                   ↓
ChatView   Session      LLM Manager        Context Queries
        + Request ID   + Provider Failover  + Knowledge Graph
```

## 2. Frontend Architecture Details

### Component Hierarchy

```
App.jsx
├── Header.jsx
├── GraphView.jsx (2D D3.js visualization - PRIMARY)
│   ├── Legend.jsx (node styling system)
│   ├── ConfigActions.jsx
│   ├── useGraphSimulation.js (D3 force simulation)
│   └── ErrorBoundary (rendering error recovery)
├── UnifiedGraphWrapper.jsx
│   ├── GraphViewWrapper.jsx (2D with performance monitoring)
│   └── Unified3DGraphWrapper.jsx (3D Three.js/WebGL)
├── ChatView.jsx
│   ├── StructuredResponse.jsx
│   ├── AnswerWithReferences.jsx
│   ├── MessageReferences.jsx
│   └── ThinkingSection.jsx
├── AdvancedAnalysisPanel.jsx (GDS integration)
├── NodeDetails.jsx + NodeDetailsModern.jsx
├── RelationshipDetails.jsx
└── PerformanceMetrics.jsx
```

### 2D vs 3D Visualization Strategy

#### 2D Mode (GraphView.jsx - Primary Implementation)
- **Tech Stack**: D3.js force simulation + SVG rendering
- **Performance**: Optimized for 1000+ nodes with efficient force calculations
- **Features**: Full interactivity, zoom/pan, node expansion, detailed tooltips
- **Styling**: Legend.jsx controls all visual aspects
- **Use Case**: Default mode, production deployments

#### 3D Mode (Unified3DGraphWrapper.jsx)
- **Tech Stack**: react-force-graph-3d + Three.js + WebGL
- **Performance**: Hardware-accelerated rendering, camera controls
- **Features**: 3D spatial navigation, WebGL context loss recovery
- **Styling**: Separate 3D-specific color/shape mappings
- **Use Case**: Advanced visualization, demos, spatial analysis

### State Management Patterns

#### Settings Persistence
```javascript
// settingsService.js - Multi-layer persistence
localStorage → settingsStore (Zustand) → component state → D3/Three.js
```

#### Chat State Management
```javascript
// ChatContext.jsx + chatStore.js
ChatContext (React Context) ↔ chatStore (Zustand) ↔ sessionStorage
```

#### Performance Monitoring
```javascript
// performanceMonitor.js - Real-time metrics
Component lifecycle → Performance metrics → PerformanceMetrics.jsx
```

### Node Styling System Architecture

#### Color/Size/Icon Mapping
```javascript
// Legend.jsx - Central styling authority
defaultNodeColors: {
  'Product': '#1f77b4',      // Blue
  'Module': '#ff7f0e',       // Orange
  'Workflow': '#2ca02c',     // Green
  // ... business-relevant categories
}

// Constants/iconMap.js - Icon assignments
iconMap: {
  'Product': '💼',
  'Module': '🔧', 
  'Workflow': '⚡',
  // ... SVG icon paths
}
```

#### Dynamic Style Application
```javascript
// GraphView.jsx - Node rendering pipeline
getNodeType(d) → Legend colors/sizes → D3 selection styling → SVG/Canvas
```

### Chat Integration Architecture

#### Request Flow
```javascript
// ChatView.jsx → chatApiService.js → Proxy → FastAPI
sendMessage(message, history, requestId, graphitiSettings) 
  → axios.post('/api/chat')
  → Proxy routing (session management)
  → FastAPI LLM processing
  → Structured response
```

#### Advanced Features
- **Request Cancellation**: Active request tracking with cancelToken
- **Retry Logic**: Exponential backoff with error classification
- **Session Persistence**: Conversation history across browser sessions
- **Structured Responses**: AI responses with citations and follow-ups

## 3. Backend Service Interactions

### API Backend Structure (360t-kg-api/)

#### Core Server Architecture
```javascript
// server.js - Express app with Neo4j driver
Neo4j Connection Pool → GraphRepository → Route Handlers → Response Transformation
```

#### Route Structure
```
/api/graph/
├── initial (complete dataset)
├── visualization (performance-optimized)
├── minimal (memory-optimized)
├── expand/:nodeId (1-hop/2-hop expansion)
└── filter (POST - dynamic filtering)

/api/analysis/
├── hidden-links (GDS Node2Vec)
├── predict-links (ML predictions)
└── gds-status (algorithm status)

/api/chat/
├── send (legacy chat endpoint)
└── conversations (conversation management)

/api/settings/
├── GET/POST (user preferences)
└── defaults (reset functionality)
```

#### Data Transformation Patterns

The system implements **3 distinct graph data endpoints** with different optimization strategies:

##### 1. GET /api/graph/initial
- **Purpose**: Complete dataset with full fidelity
- **Node Properties**: All properties (summary, group_id, category, url, created_at)
- **Relationship Properties**: Both 'name' and 'fact' properties
- **Memory Impact**: Highest - full data retention
- **Use Case**: Initial load, detailed analysis, full-featured UI

##### 2. GET /api/graph/visualization  
- **Purpose**: Performance-optimized rendering
- **Node Properties**: Essential properties, truncated summaries (200 chars)
- **Relationship Properties**: Same as /initial (name + fact)
- **Memory Impact**: Balanced - optimized for rendering
- **Use Case**: High-performance production environments

##### 3. GET /api/graph/minimal
- **Purpose**: Memory-constrained environments
- **Node Properties**: Minimal set, truncated summaries (100 chars)
- **Relationship Properties**: Same as others (name + fact)
- **Memory Impact**: Lowest - essential data only
- **Use Case**: Mobile clients, memory-limited deployments

#### Neo4j Integration Patterns

##### Connection Management
```javascript
// server.js - Connection with retry logic
const driver = neo4j.driver(uri, auth, {
  connectionTimeout: 30000,
  maxConnectionPoolSize: 50,
  maxTransactionRetryTime: 150000
});
```

##### Query Patterns
```javascript
// GraphRepository.js - Query abstraction
async executeQuery(query, params = {}) {
  const session = this.driver.session();
  try {
    const result = await session.run(query, params);
    return result.records.map(record => record.toObject());
  } finally {
    await session.close();
  }
}
```

### Proxy Server Architecture (proxy-server/)

#### Core Responsibilities
1. **Session Management**: Express-session with configurable storage
2. **Request Routing**: Intelligent routing between API backend and AI layer
3. **CORS Handling**: Multi-origin support for development/production
4. **Rate Limiting**: Configurable rate limiting per session
5. **Request Timing**: Performance monitoring and logging

#### Routing Logic
```javascript
// server.js - Route distribution
app.use('/api/chat', chatProxy);           // → AI Layer (8000)
app.use('/api/conversations', fastAPIProxy); // → AI Layer (8000)  
app.use('/api', backendProxy);             // → API Backend (3002)
```

#### Session Configuration
```javascript
session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: { 
    secure: false, // HTTPS in production
    maxAge: 3600000 // 1 hour
  }
})
```

#### CORS Strategy
```javascript
// Dynamic origin validation for development flexibility
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:5177', // Primary frontend port
      'http://localhost:5178', // Vite fallback port
      'http://localhost:3000', // Development port
      'http://localhost:3002'  // API backend port
    ];
    // ... validation logic
  },
  credentials: true
};
```

## 4. AI Integration Complexity

### LLM Abstraction Layer Architecture

#### Provider Abstraction Hierarchy
```python
# llm_abstraction/ - Complete provider abstraction
LLMManager (main interface)
├── ProviderSelector (intelligent selection)
├── ConversationHistory (persistent state)
├── EnhancedErrorHandler (advanced retry logic)
└── Providers/
    ├── OpenAIProvider
    ├── GoogleGeminiProvider  
    ├── OllamaProvider
    └── AzureOpenAIProvider
```

#### Provider Selection Strategy
```python
# provider_selector.py - Intelligent failover
SelectionPolicy.FAILOVER:
  Primary → Fallback → Emergency → Error

SelectionPolicy.ROUND_ROBIN:
  Provider rotation for load balancing

SelectionPolicy.PERFORMANCE_BASED:
  Dynamic selection by response times
```

#### Error Handling & Recovery
```python
# enhanced_error_handler.py - Advanced retry logic
RetryStrategy:
  - Exponential backoff (1s → 2s → 4s → 8s)
  - Provider-specific error classification
  - Circuit breaker pattern for failed providers
  - Automatic provider health checks
```

#### Context Window Management
```python
# context_window_manager.py - Token optimization
def manage_context(conversation_history, max_tokens):
  1. Calculate current token usage
  2. Prioritize recent messages
  3. Compress older context
  4. Maintain conversation continuity
  5. Handle token limit overflow
```

### FastAPI Integration (main.py)

#### Application Architecture
```python
# Async context manager for startup/shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize LLM providers, database connections
    yield
    # Cleanup resources

app = FastAPI(lifespan=lifespan)
```

#### Enhanced Middleware Stack
```python
# Request/Response logging middleware
# Trusted host middleware for security
# CORS middleware with dynamic origins
# Error handling middleware with provider-specific handling
```

#### Chat Endpoint Design
```python
@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    1. Validate request structure
    2. Initialize LLM manager with failover
    3. Build context from Neo4j knowledge graph
    4. Process through selected LLM provider
    5. Format response with structured data
    6. Handle errors with automatic retry
```

### Conversation Persistence Strategy

#### Multi-Layer Storage
```python
# conversation_history.py - Persistent state management
Session Storage (browser) ↔ FastAPI Memory ↔ File System (JSON)
```

#### Session Management
```javascript
// Frontend - Session correlation
const sessionId = sessionStorage.getItem('sessionId') || generateUUID();
// Sent with each request for conversation continuity
```

## 5. Database Schema and Patterns

### Neo4j Schema Design

#### Node Types
```cypher
// Primary business entities
(:Product)     - Platform products
(:Module)      - Software modules  
(:Workflow)    - Business workflows
(:Entity)      - Graphiti knowledge entities
(:Document)    - Documentation/text sources
```

#### Relationship Types
```cypher
// Structured relationships
()-[:USES]->()           // Usage relationships
()-[:CONTAINS]->()       // Containment hierarchies
()-[:SIMILAR_TO]->()     // Computed similarity (GDS)
()-[:RELATES_TO]->()     // General relationships
```

#### Property Patterns
```cypher
// Node properties (standardized)
{
  name: string,           // Display name
  summary: string,        // Description
  category: string,       // Business category
  group_id: string,       // Grouping identifier
  created_at: datetime,   // Timestamp
  uuid: string           // Unique identifier
}

// Relationship properties
{
  name: string,          // Relationship name
  fact: string,          // Relationship description
  weight: float,         // Computed strength
  confidence: float      // ML confidence score
}
```

### GDS (Graph Data Science) Integration

#### Hidden Links Analysis
```cypher
// Node2Vec algorithm for link prediction
CALL gds.node2vec.stream($projectionName) 
YIELD nodeId, embedding
// Compute similarity scores between embeddings
// Generate candidate relationships with confidence scores
```

#### Graph Projection Strategy
```javascript
// Environment configuration
GDS_GRAPH_NODES=Module,Product,Workflow
GDS_GRAPH_RELATIONSHIPS=USES,CONTAINS
```

#### Performance Patterns
```cypher
// Optimized queries with LIMIT clauses
MATCH (n)-[r]->(m) 
WHERE id(n) = $nodeId
RETURN n, r, m 
LIMIT $limit
```

### Query Optimization Patterns

#### Index Strategy
```cypher
// Performance-critical indexes
CREATE INDEX node_category_idx FOR (n:Entity) ON (n.category);
CREATE INDEX node_name_idx FOR (n:Entity) ON (n.name);
CREATE INDEX relationship_type_idx FOR ()-[r:RELATES_TO]-() ON (r.name);
```

#### Connection Pooling
```javascript
// server.js - Optimized driver configuration
const driver = neo4j.driver(uri, auth, {
  maxConnectionPoolSize: 50,
  connectionTimeout: 30000,
  maxTransactionRetryTime: 150000
});
```

## 6. Development Workflow Details

### Multi-Service Orchestration

#### Root Level Scripts
```json
// package.json - Orchestration commands
{
  "scripts": {
    "dev": "node scripts/simple-dev.js",        // Simple orchestration
    "dev-complex": "node scripts/dev.js",       // Advanced orchestration
    "list": "node scripts/dev.js list",         // Service discovery
    "generate": "node scripts/dev.js generate", // Artifact generation
    "parse-prd": "node scripts/dev.js parse-prd" // PRD analysis
  }
}
```

#### Service Startup Sequence
```javascript
// scripts/simple-dev.js - Coordinated startup
1. Check Neo4j availability
2. Start API backend (port 3002)
3. Start Python AI layer (port 8000) 
4. Start proxy server (port 3003)
5. Start frontend (port 5177)
6. Health check all services
```

### Testing Strategy Across Layers

#### Frontend Testing
```javascript
// Multiple testing approaches
Jest Unit Tests:        src/__tests__/
Playwright E2E:         tests/e2e/
Component Integration:  Cross-component testing
Accessibility:          jest-axe (WCAG compliance)
Performance:            Render time validation
Visual Regression:      Screenshot comparison
```

#### Backend Testing  
```javascript
// Comprehensive API testing
Mocha E2E Tests:        test-chat-e2e.js (300s timeout)
Mocha API Tests:        test-chat-api.js
Integration Tests:      Cross-service communication
Database Testing:       Neo4j test isolation
Contract Testing:       OpenAPI specification
Load Testing:           Concurrent request handling
```

#### Python Testing
```python
# AI layer testing
pytest:                 tests/ directory
Async Testing:          pytest-asyncio
LLM Provider Testing:   Mock providers + fallback
Error Handling:         Provider failure scenarios
Context Management:     Token limits + conversation history
Integration:            End-to-end LLM abstraction
```

#### Critical Path E2E Tests
```typescript
// tests/e2e/ - Key user journeys
chat-functionality-comprehensive.spec.ts   // Multi-provider chat
node-expansion.spec.ts                      // Graph interactions
hidden-links.spec.ts                        // GDS algorithm testing
settings-recovery.spec.ts                   // Persistence validation
performance-monitoring.spec.ts              // Real-time metrics
```

### Environment Configuration

#### Required Environment Variables
```bash
# Neo4j Database
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j  
NEO4J_PASSWORD=your_password

# LLM Configuration (provider-specific)
OPENAI_API_KEY=your_key
GOOGLE_API_KEY=your_key  
OLLAMA_URL=http://localhost:11434
AZURE_OPENAI_API_KEY=your_key
AZURE_OPENAI_ENDPOINT=your_endpoint

# Proxy Server Configuration
PROXY_PORT=3003
FASTAPI_URL=http://localhost:8000
SESSION_SECRET=your-secret-key
SESSION_MAX_AGE=3600000

# Performance/GDS Configuration
GDS_GRAPH_NODES=Module,Product,Workflow
GDS_GRAPH_RELATIONSHIPS=USES,CONTAINS
LOG_LEVEL=info
NODE_ENV=development
```

### Common Debugging Patterns

#### Service Health Checks
```bash
# Port availability
npx kill-port 3002 && npx kill-port 3003 && npx kill-port 5177

# Service status
curl http://localhost:3002/health    # API Backend
curl http://localhost:8000/health    # AI Layer  
curl http://localhost:3003/health    # Proxy Server
```

#### Neo4j Connectivity
```javascript
// Connection validation
await driver.verifyConnectivity();
// Query testing
MATCH (n) RETURN count(n) LIMIT 1;
```

#### LLM Provider Issues
```python
# Provider availability check
ollama_health = await ollama_provider.health_check()
google_health = await google_provider.health_check()
# Failover testing
```

## 7. Critical Implementation Details

### Node Styling System Architecture

#### Centralized Style Authority
```javascript
// Legend.jsx - Single source of truth for all visual styling
const defaultNodeColors = {
  'Product': '#1f77b4',    // Strategic blue
  'Module': '#ff7f0e',     // Engineering orange  
  'Workflow': '#2ca02c',   // Process green
  'System': '#d62728',     // Infrastructure red
  'API': '#9467bd',        // Integration purple
  'Database': '#8c564b',   // Data brown
  'Service': '#e377c2',    // Service pink
  'Config': '#7f7f7f',     // Configuration gray
  'Default': '#17becf'     // Fallback cyan
};

const defaultNodeSizes = {
  'Product': 12,      // Largest - strategic importance
  'Module': 10,       // Large - core components
  'Workflow': 8,      // Medium - processes
  'Default': 6        // Small - standard nodes
};
```

#### Dynamic Style Application Pipeline
```javascript
// GraphView.jsx - Runtime style resolution
1. getNodeType(d) - Determine node type from properties
2. Legend.getNodeColor(type) - Resolve color mapping
3. Legend.getNodeSize(type) - Resolve size mapping  
4. iconMap[type] - Resolve icon mapping
5. D3 selection styling - Apply to SVG elements
6. Settings override - User customizations
```

#### 2D vs 3D Style Consistency
```javascript
// UnifiedGraphWrapper.jsx - Mode switching with style preservation
const nodeStyleContext = {
  colors: Legend.defaultNodeColors,
  sizes: Legend.defaultNodeSizes,
  icons: iconMap
};
// Passed to both 2D and 3D implementations
```

### Performance Monitoring Integration

#### Real-Time Metrics Collection
```javascript
// performanceMonitor.js - Comprehensive performance tracking
class PerformanceMonitor {
  trackRenderTime(componentName, renderDuration)
  trackMemoryUsage(heapUsed, heapTotal)  
  trackGraphStats(nodeCount, linkCount)
  trackUserInteraction(interactionType, duration)
  generateReport() // Performance summary
}
```

#### Integration Points
```javascript
// GraphViewWrapper.jsx - Automatic performance tracking
useEffect(() => {
  const startTime = performance.now();
  // ... rendering logic
  const endTime = performance.now();
  monitor.trackRenderTime('GraphView', endTime - startTime);
}, [graphData]);
```

#### Performance Dashboard
```javascript
// PerformanceMetrics.jsx - Real-time display
- Render Times: Graph, Chat, UI components
- Memory Usage: Heap usage, garbage collection
- Graph Statistics: Node/link counts, filter status
- User Interactions: Click response times, search latency
```

### Settings Persistence Mechanisms

#### Multi-Layer Persistence Strategy
```javascript
// settingsService.js - Hierarchical storage
1. Component State (immediate UI updates)
2. Zustand Store (application state)  
3. localStorage (browser persistence)
4. Backend API (cross-device synchronization)

// Conflict resolution
const mergeSettings = (local, remote, defaults) => {
  return { ...defaults, ...remote, ...local };
};
```

#### Configuration Schema
```javascript
// Settings structure with validation
const settingsSchema = {
  visualization: {
    nodeColors: Object,        // User color customizations
    nodeSizes: Object,         // User size customizations  
    simulationStrength: Number, // D3 force parameters
    renderMode: String         // '2d' | '3d'
  },
  performance: {
    maxNodes: Number,          // Rendering limits
    enableMetrics: Boolean     // Performance monitoring
  },
  chat: {
    provider: String,          // Preferred LLM provider
    streamingEnabled: Boolean  // Real-time responses
  }
};
```

### Error Handling and Recovery Flows

#### Frontend Error Boundaries
```javascript
// ErrorBoundary.jsx - Component-level error isolation
class ErrorBoundary extends Component {
  1. Catch rendering errors
  2. Display fallback UI  
  3. Log error details
  4. Provide recovery options
  5. Reset component state
}
```

#### Backend Error Handling
```javascript
// Enhanced error middleware stack
1. Request validation (express-validator)
2. Neo4j connection errors (retry logic)
3. GDS algorithm errors (graceful fallback)
4. Resource limits (memory/timeout protection)
5. Structured error responses
```

#### AI Layer Error Recovery
```python
# enhanced_error_handler.py - Provider failover
@retry_with_provider_fallback
async def process_chat_request(request):
  try:
    return await primary_provider.generate(request)
  except ProviderError:
    return await fallback_provider.generate(request)
  except AllProvidersFailedError:
    return structured_error_response()
```

#### Client-Side Recovery Patterns
```javascript
// chatApiService.js - Request resilience
const retryWithBackoff = async (fn, maxRetries = 3) => {
  1. Attempt request
  2. Classify error type
  3. Determine retry eligibility  
  4. Apply exponential backoff
  5. Track failure patterns
  6. Escalate to user notification
};
```

## 8. Inter-Service Communication Patterns

### Request Routing Matrix

```
Frontend Request → Proxy Decision → Target Service
─────────────────────────────────────────────────
/api/graph/*      → Backend API  (3002)
/api/analysis/*   → Backend API  (3002)  
/api/settings/*   → Backend API  (3002)
/api/chat         → AI Layer     (8000)
/api/conversations → AI Layer    (8000)
/health           → Local        (3003)
```

### Session Correlation Strategy

#### Request Identification
```javascript
// Frontend - Request correlation
const requestId = `req_${Date.now()}_${Math.random()}`;
const sessionId = sessionStorage.getItem('sessionId') || generateUUID();

// Headers sent with every request
headers: {
  'X-Request-ID': requestId,
  'X-Session-ID': sessionId
}
```

#### Cross-Service Context
```python
# AI Layer - Context building from graph data
async def build_context(session_id: str, query: str):
  1. Query Neo4j for relevant entities
  2. Build knowledge graph context
  3. Retrieve conversation history
  4. Format context for LLM processing
```

### Service Discovery and Health Monitoring

#### Health Check Endpoints
```javascript
// Standard health check contract
GET /health → {
  status: 'healthy' | 'degraded' | 'unhealthy',
  timestamp: ISO_8601_timestamp,
  dependencies: {
    neo4j: 'connected' | 'disconnected',
    llm_providers: ['ollama', 'google_genai'],
    memory_usage: percentage
  }
}
```

#### Circuit Breaker Pattern
```python
# Provider health monitoring
class ProviderHealthMonitor:
  def __init__(self):
    self.consecutive_failures = {}
    self.circuit_breakers = {}
    
  async def is_provider_healthy(self, provider_name):
    if self.consecutive_failures[provider_name] > 3:
      return False  # Circuit breaker open
    return await self.ping_provider(provider_name)
```

## 9. Change Impact Analysis

### Component Dependency Map

```
Legend.jsx Changes Impact:
├── GraphView.jsx (2D node styling)
├── Unified3DGraphWrapper.jsx (3D node styling)  
├── NodeDetails.jsx (detail view styling)
├── Settings persistence (color/size customizations)
└── Performance monitoring (style calculation overhead)

LLM Manager Changes Impact:
├── FastAPI chat endpoint (provider selection)
├── Conversation history (session management)
├── Error handling (retry logic)
├── Frontend chat UI (response formatting)
└── Proxy routing (timeout handling)

Neo4j Schema Changes Impact:
├── GraphRepository (query patterns)
├── Graph transformation endpoints (property extraction)
├── GDS algorithms (node/relationship projections)
├── Chat context building (knowledge graph queries)
└── Frontend node display (property rendering)
```

### Safe Change Patterns

#### Frontend Changes
```javascript
// Safe: Add new node types to Legend.jsx
const defaultNodeColors = {
  ...existingColors,
  'NewType': '#newcolor'  // Additive change
};

// Risky: Modify existing node type names
// Impacts: Graph data transformation, persistence, styling pipeline
```

#### Backend API Changes
```javascript
// Safe: Add optional properties to existing endpoints
// Risky: Modify existing property names or remove properties
// Critical: Change endpoint URLs or request/response structure
```

#### Database Changes
```cypher
// Safe: Add new node labels or relationship types
// Risky: Modify existing property names
// Critical: Change core schema (affects all graph queries)
```

## 10. Performance Considerations

### Scalability Thresholds

#### Frontend Rendering Limits
- **2D Mode**: 1000+ nodes (D3 force simulation optimization required)
- **3D Mode**: 500+ nodes (WebGL context memory limits)
- **Relationship Expansion**: 2-hop maximum (combinatorial explosion)

#### Backend Query Optimization
- **Neo4j Connections**: Max 50 concurrent (connection pool limit)
- **GDS Operations**: Memory-intensive (require dedicated resources)
- **Response Size**: 10MB maximum (browser memory limits)

#### AI Layer Processing
- **LLM Requests**: 30-60 second timeouts (model-dependent)
- **Context Building**: 10-second Neo4j query limit
- **Conversation History**: 100 messages maximum (token limits)

### Optimization Strategies

#### Graph Data Caching
```javascript
// Three-tier caching strategy
1. Browser Memory (D3 simulation state)
2. localStorage (user sessions)  
3. Redis (cross-user caching - future enhancement)
```

#### Query Performance
```cypher
// Optimized patterns with indexes
MATCH (n:Entity)-[r:RELATES_TO]->(m:Entity)
WHERE n.category = $category
RETURN n, r, m
LIMIT $limit
USING INDEX n:Entity(category)
```

#### Resource Monitoring
```javascript
// Memory usage tracking
const monitorMemory = () => {
  const memory = performance.memory;
  if (memory.usedJSHeapSize > MEMORY_THRESHOLD) {
    triggerGarbageCollection();
    notifyUserOfPerformanceIssue();
  }
};
```

---

## Conclusion

This Knowledge Graph Visualizer represents a sophisticated integration of modern web technologies, AI capabilities, and graph database systems. The 5-layer architecture provides clear separation of concerns while enabling complex interactions between visualization, AI chat, and graph analytics.

**Key Strengths:**
- Robust error handling and provider failover mechanisms
- Performance-optimized data transformation pipelines  
- Comprehensive testing strategy across all layers
- Flexible styling system with user customization
- Advanced LLM provider abstraction

**Critical Dependencies:**
- Neo4j database connectivity and performance
- LLM provider availability and response times
- Browser rendering capabilities (WebGL, canvas, SVG)
- Session state consistency across service boundaries

**Change Management:**
When modifying this system, consider cross-layer impacts, especially changes to:
- Node/relationship schema (affects visualization and AI context)
- API endpoints (impacts frontend-backend contracts)
- LLM provider interfaces (affects chat functionality)
- Performance monitoring (impacts user experience metrics)

This documentation should serve as the authoritative reference for understanding system architecture, making informed modifications, and onboarding new team members to the codebase complexity.