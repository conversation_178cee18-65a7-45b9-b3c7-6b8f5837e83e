#!/usr/bin/env node

/**
 * Service Connectivity Test Script
 * 
 * Tests the connectivity between proxy server and FastAPI service
 * to verify our Phase 2 fixes are working correctly.
 */

import chalk from 'chalk';

const SERVICES = {
  proxy: 'http://localhost:3003',
  fastapi: 'http://localhost:8000',
  api: 'http://localhost:3002',
  frontend: 'http://localhost:5177'
};

function log(color, message) {
  const timestamp = new Date().toISOString().slice(11, 23);
  console.log(chalk[color](`${timestamp} ${message}`));
}

async function testServiceHealth(name, url) {
  try {
    log('cyan', `Testing ${name} at ${url}...`);
    
    const response = await fetch(`${url}/health`, {
      method: 'GET',
      timeout: 5000,
      headers: { 'User-Agent': 'connectivity-test/1.0' }
    });
    
    if (response.ok) {
      const data = await response.json();
      log('green', `✅ ${name} is healthy`);
      
      if (data.services) {
        Object.entries(data.services).forEach(([service, status]) => {
          const color = status === 'healthy' ? 'green' : status === 'unhealthy' ? 'red' : 'yellow';
          log(color, `   - ${service}: ${status}`);
        });
      }
      
      return { status: 'healthy', data };
    } else {
      log('red', `❌ ${name} returned ${response.status}`);
      return { status: 'unhealthy', error: `HTTP ${response.status}` };
    }
  } catch (error) {
    log('red', `❌ ${name} failed: ${error.message}`);
    return { status: 'error', error: error.message };
  }
}

async function testChatConnectivity() {
  try {
    log('cyan', 'Testing chat connectivity (proxy → FastAPI)...');
    
    const response = await fetch('http://localhost:3003/api/chat/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'connect.sid=test-session-id'
      },
      body: JSON.stringify({
        message: 'Test connectivity',
        conversation_history: [],
        graphitiSettings: {
          llmProvider: 'ollama',
          ollamaModel: 'qwen3:30b-a3b-q8_0',
          graphitiModel: 'gemma3:latest'
        }
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      log('green', '✅ Chat connectivity working');
      log('cyan', `   Response length: ${data.response?.length || 0} chars`);
      return { status: 'working', data };
    } else {
      const errorData = await response.text();
      log('red', `❌ Chat connectivity failed: ${response.status}`);
      log('yellow', `   Error: ${errorData.substring(0, 200)}...`);
      return { status: 'failed', error: errorData };
    }
  } catch (error) {
    log('red', `❌ Chat connectivity error: ${error.message}`);
    return { status: 'error', error: error.message };
  }
}

async function main() {
  console.log(chalk.blue.bold('🔍 Service Connectivity Test'));
  console.log(chalk.gray('Testing Phase 2 fixes for service connectivity issues...\n'));
  
  const results = {};
  
  // Test individual service health
  console.log(chalk.yellow('=== Testing Individual Services ==='));
  for (const [name, url] of Object.entries(SERVICES)) {
    results[name] = await testServiceHealth(name, url);
    console.log(''); // Add spacing
  }
  
  // Test chat connectivity specifically
  console.log(chalk.yellow('=== Testing Chat Connectivity ==='));
  results.chat = await testChatConnectivity();
  console.log('');
  
  // Summary
  console.log(chalk.yellow('=== Summary ==='));
  const healthyServices = Object.entries(results).filter(([_, result]) => 
    result.status === 'healthy' || result.status === 'working'
  );
  const unhealthyServices = Object.entries(results).filter(([_, result]) => 
    result.status !== 'healthy' && result.status !== 'working'
  );
  
  log('cyan', `Healthy services: ${healthyServices.length}/${Object.keys(results).length}`);
  healthyServices.forEach(([name]) => log('green', `  ✅ ${name}`));
  
  if (unhealthyServices.length > 0) {
    log('yellow', `Unhealthy services: ${unhealthyServices.length}`);
    unhealthyServices.forEach(([name, result]) => {
      log('red', `  ❌ ${name}: ${result.error || result.status}`);
    });
  }
  
  // Specific Phase 2 validation
  console.log(chalk.yellow('\n=== Phase 2 Fix Validation ==='));
  
  if (results.fastapi?.status === 'healthy') {
    log('green', '✅ FastAPI service is running on port 8000');
  } else {
    log('red', '❌ FastAPI service issue detected');
    log('yellow', '💡 Check: .venv/bin/python main.py from root directory');
  }
  
  if (results.proxy?.status === 'healthy') {
    log('green', '✅ Proxy server is running on port 3003');
  } else {
    log('red', '❌ Proxy server issue detected');
  }
  
  if (results.chat?.status === 'working') {
    log('green', '✅ Proxy → FastAPI communication working');
  } else {
    log('red', '❌ Proxy → FastAPI communication failed');
    log('yellow', '💡 Check FASTAPI_URL environment variable');
  }
  
  const overallStatus = unhealthyServices.length === 0 ? 'SUCCESS' : 'ISSUES_DETECTED';
  const statusColor = overallStatus === 'SUCCESS' ? 'green' : 'red';
  
  console.log(chalk[statusColor].bold(`\n🏁 Overall Status: ${overallStatus}`));
  
  if (overallStatus === 'SUCCESS') {
    log('green', '🎉 All Phase 2 service connectivity fixes are working!');
  } else {
    log('yellow', '🔧 Some services need attention. Check the errors above.');
  }
}

// Run the test
main().catch(error => {
  log('red', `Test script failed: ${error.message}`);
  process.exit(1);
});