/**
 * Development Configuration
 * Unified configuration for all development services and startup behavior
 */

module.exports = {
  // Service configuration with startup order and dependencies
  services: {
    api: {
      name: 'API Server',
      port: 3002,
      command: 'npm run dev',
      cwd: './360t-kg-api',
      color: 'magenta',
      startupOrder: 1,
      startupDelay: 0,
      healthCheck: {
        endpoint: 'http://127.0.0.1:3002/',
        readinessEndpoint: 'http://127.0.0.1:3002/api/health',
        timeout: 5000
      }
    },
    proxy: {
      name: 'Proxy Server',
      port: 3003,
      command: 'npm run dev',
      cwd: './proxy-server',
      color: 'cyan',
      startupOrder: 2,
      startupDelay: 3000, // Wait for API server to be ready
      dependsOn: ['api'],
      healthCheck: {
        endpoint: 'http://127.0.0.1:3003/health',
        readinessEndpoint: 'http://127.0.0.1:3003/api/ready',
        timeout: 5000
      }
    },
    pythonAI: {
      name: 'Python AI Service',
      port: 8000,
      command: '.venv/bin/python main.py',
      cwd: '.',
      color: 'green',
      startupOrder: 3,
      startupDelay: 2000, // Can start in parallel with proxy
      dependsOn: [], // Independent of other services
      healthCheck: {
        endpoint: 'http://127.0.0.1:8000/health',
        readinessEndpoint: 'http://127.0.0.1:8000/ready',
        timeout: 8000 // Python services need more time
      }
    },
    frontend: {
      name: 'UI Server',
      port: 5177,
      command: 'npm run dev',
      cwd: './360t-kg-ui',
      color: 'blue',
      startupOrder: 4,
      startupDelay: 6000, // Wait for all backend services
      dependsOn: ['api', 'proxy', 'pythonAI'],
      healthCheck: {
        endpoint: 'http://127.0.0.1:5177/',
        readinessEndpoint: 'http://127.0.0.1:5177/',
        timeout: 10000 // Frontend needs time to compile
      }
    }
  },

  // Port management configuration
  ports: {
    clear: [3002, 3003, 5177, 5178, 5179, 5180, 5181, 5182, 8000],
    cleanup: {
      timeout: 5000, // Time to wait for graceful shutdown
      retries: 3,
      verifyFreed: true // Verify ports are actually freed
    }
  },

  // Health check configuration
  healthCheck: {
    maxAttempts: 30,
    intervalMs: 1000,
    initialDelayMs: 2000,
    timeoutMs: 5000,
    exponentialBackoff: true,
    maxBackoffMs: 10000
  },

  // Startup behavior
  startup: {
    sequential: true, // Start services in order, not parallel
    failFast: true, // Stop if any critical service fails
    gracefulShutdown: true,
    logLevel: 'info' // 'debug', 'info', 'warn', 'error'
  },

  // Service readiness criteria
  readiness: {
    // API Server readiness
    api: {
      checks: [
        { type: 'http', url: 'http://127.0.0.1:3002/' },
        { type: 'endpoint', url: 'http://127.0.0.1:3002/api/graph/health' }
      ]
    },
    // Proxy readiness 
    proxy: {
      checks: [
        { type: 'http', url: 'http://127.0.0.1:3003/health' },
        { type: 'upstream', url: 'http://127.0.0.1:3003/api/upstream-health' }
      ]
    },
    // Python AI readiness
    pythonAI: {
      checks: [
        { type: 'http', url: 'http://127.0.0.1:8000/health' },
        { type: 'database', url: 'http://127.0.0.1:8000/db-health' }
      ]
    },
    // Frontend readiness
    frontend: {
      checks: [
        { type: 'http', url: 'http://127.0.0.1:5177/' }
      ]
    }
  },

  // Development environment settings
  environment: {
    NODE_ENV: 'development',
    DEBUG: '1',
    LOG_LEVEL: 'info'
  }
};